<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <link rel="stylesheet" href="../../assets/icomoon/style.css">
    <link rel="stylesheet" href="../../assets/css/manage_admin/public.css">
    <link rel="stylesheet" href="../../assets/lib/element-ui/lib/theme-chalk/index.css">
    <link rel="stylesheet" href="../../assets/css/manage_admin/website_setting.css">
</head>

<body>
    <div class="website-setting" id="website-setting" v-cloak>
        <div class="setting-form">
            <h2 class="inner-title">基本信息</h2>
            <div class="setting-form-item">
                <div class="setting-form-item-label">网站名称</div>
                <div class="setting-form-item-content">
                    <!-- <p class="website-name">{{websiteJson.name}}</p> -->
                    <input type="text" class="website-name" v-model="websiteJson.name">
                </div>
            </div>
            <div class="setting-form-item">
                <div class="setting-form-item-label">网站简介</div>
                <div class="setting-form-item-content">
                    <textarea name="" id="" v-model="websiteJson.intro" placeholder="请输入"></textarea>
                </div>
            </div>
            <div class="setting-form-item">
                <div class="setting-form-item-label">网站图标
                    <el-tooltip popper-class="item-tips" effect="dark" placement="top">
                        <div slot="content">
                            <p>示例图</p>
                            <img src="../../assets/images/manage_admin/mh_bg_1.jpg" height="39" width="189"/>
                        </div>
                        <i class="icon-mh-info"></i>
                    </el-tooltip>
                </div>
                <div class="setting-form-item-content">
                    <div class="logo-content">
                        <div class="logo-box">
                            <p class="logo-txt">Logo1</p>
                            <div class="logo-upload-box">
                                <div class="logo-img">
                                    <img class="imgSrc" src="../../assets/images/manage_admin/demo.png" alt="">
                                    <div class="delete-btn">
                                        <span class="delete-icon"></span>
                                    </div>
                                </div>
                                <div class="logo-img failure-box">
                                    <div class="fail-content">
                                        <img src="../../assets/images/manage_admin/attention.png" alt="">
                                        <p>上传失败</p>
                                        <div class="retry-btn">重试</div>
                                    </div>
                                </div>
                                <div class="right-upload-btn">
                                    <el-upload class="upload-demo" action="https://jsonplaceholder.typicode.com/posts/"
                                               :on-preview="handlePreview" :on-remove="handleRemove"
                                               :before-remove="beforeRemove" multiple :limit="3" :on-exceed="handleExceed"
                                               :file-list="fileList3">
                                        <p>格式：PNG/JPG/GIF<br>大小：32x32，16KB以下</p>
                                        <div class="upload-btn">上传</div>
                                    </el-upload>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div style="background: #F0F0F0;margin: 20px 0;height: 1px;"></div>
            <h2 class="inner-title">网站素材</h2>
            <div class="setting-form-item">
                <div class="setting-form-item-label">单位Logo
                    <el-tooltip popper-class="item-tips" effect="dark" placement="top">
                        <div slot="content">
                            <p>示例图</p>
                            <img src="../../assets/images/manage_admin/mh_bg_2.jpg" height="137" width="301"/>
                        </div>
                        <i class="icon-mh-info"></i>
                    </el-tooltip>
                </div>
                <div class="setting-form-item-content">
                    <div class="logo-content">
                        <div class="logo-box">
                            <p class="logo-txt">Logo1</p>
                            <div class="logo-upload-box">
                                <div class="logo-img">
                                    <div class="transparent-bg"></div>
                                </div>
                                <div class="logo-img">
                                    <img src="../../assets/images/manage_admin/demo.png" alt="">
                                    <div class="delete-btn">
                                        <span class="delete-icon"></span>
                                    </div>
                                </div>
                                <div class="logo-img failure-box">
                                    <div class="fail-content">
                                        <img src="../../assets/images/manage_admin/attention.png" alt="">
                                        <p>上传失败</p>
                                        <div class="retry-btn">重试</div>
                                    </div>
                                </div>
                                <div class="right-upload-btn">
                                    <el-upload class="upload-demo" action="https://jsonplaceholder.typicode.com/posts/"
                                        :on-preview="handlePreview" :on-remove="handleRemove"
                                        :before-remove="beforeRemove" multiple :limit="3" :on-exceed="handleExceed"
                                        :file-list="fileList3">
                                        <p>格式：PNG/JPG/GIF<br>大小：32x32，16KB以下</p>
                                        <div class="upload-btn">上传</div>
                                    </el-upload>
                                </div>
                            </div>
                        </div>
                        <div class="logo-box">
                            <p class="logo-txt">Logo2</p>
                            <div class="logo-upload-box">
                                <div class="logo-img">
                                    <div class="transparent-bg"></div>
                                </div>
                                <div class="right-upload-btn">
                                    <el-upload class="upload-demo" action="https://jsonplaceholder.typicode.com/posts/"
                                        :on-preview="handlePreview" :on-remove="handleRemove"
                                        :before-remove="beforeRemove" multiple :limit="3" :on-exceed="handleExceed"
                                        :file-list="fileList4">
                                        <p>格式：PNG/JPG/GIF<br>大小：32x32，16KB以下</p>
                                        <div class="upload-btn">上传</div>
                                    </el-upload>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="setting-form-item">
                <div class="setting-form-item-label">
                    首屏背景
                    <el-tooltip popper-class="item-tips"  effect="dark" placement="top">
                        <div slot="content">
                            <p>示例图</p>
                            <img src="../../assets/images/manage_admin/mh_bg_3.jpg" height="137" width="301"/>
                        </div>
                        <i class="icon-mh-info"></i>
                    </el-tooltip>
                </div>
                <div class="setting-form-item-content">
                    <div class="logo-content">
                        <div class="logo-box type2">
                            <div class="logo-upload-box">
                                <div class="logo-img type2">
                                    <img class="imgSrc" src="../../assets/images/manage_admin/image 4 (1).png" alt="">
                                    <div class="delete-btn">
                                        <span class="delete-icon"></span>
                                    </div>
                                </div>
                                <div class="logo-img type2 failure-box">
                                    <div class="fail-content">
                                        <img src=".././../assets/images/manage_admin/attention.png" alt="">
                                        <p>上传失败</p>
                                        <div class="retry-btn">重试</div>
                                    </div>
                                </div>
                                <div class="right-upload-btn">
                                    <el-upload class="upload-demo" action="https://jsonplaceholder.typicode.com/posts/"
                                        :on-preview="handlePreview" :on-remove="handleRemove"
                                        :before-remove="beforeRemove" multiple :limit="3" :on-exceed="handleExceed"
                                        :file-list="fileList">
                                        <div class="upload-btn" slot="trigger">上传</div>
                                        <div class="upload-btn" @click="resourceCoolPop=true">资源库</div>
                                        <p>格式：PNG/JPG/GIF/MP4<br>大小：32x32，16KB以下</p>
                                    </el-upload>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
<!--            <div class="setting-form-item">-->
<!--                <div class="setting-form-item-label">视频</div>-->
<!--                <div class="setting-form-item-content">-->
<!--                    <div class="logo-content">-->
<!--                        <div class="logo-box type2">-->
<!--                            <div class="logo-upload-box">-->
<!--                                <div class="logo-img">-->
<!--                                    <div class="transparent-bg"></div>-->
<!--                                </div>-->
<!--                                <div class="right-upload-btn">-->
<!--                                    <el-upload class="upload-demo" action="https://jsonplaceholder.typicode.com/posts/"-->
<!--                                        :on-preview="handlePreview" :on-remove="handleRemove"-->
<!--                                        :before-remove="beforeRemove" multiple :limit="3" :on-exceed="handleExceed"-->
<!--                                        :file-list="fileList2">-->
<!--                                        <p>格式：PNG/JPG/GIF/MP4<br>大小：5M以下</p>-->
<!--                                        <div class="upload-btn">上传</div>-->
<!--                                    </el-upload>-->
<!--                                </div>-->
<!--                            </div>-->
<!--                        </div>-->
<!--                    </div>-->
<!--                </div>-->
<!--            </div>-->

        </div>
        <div class="save-btn">保存</div>
<!--        弹框：资源库-->
        <el-dialog
                title="资源库"
                custom-class="resource-pool-pop"
                :visible.sync="resourceCoolPop"
                width="1000px">
            <div class="pop-content">
                <div class="left-box">
                    <ul class="tabs-box">
                        <li :class="resourceCoolLeftTabIndex == 0 ? 'current':''" @click="resourceCoolLeftTabIndex=0">
                            视频库
                        </li>
                        <li :class="resourceCoolLeftTabIndex == 1 ? 'current':''" @click="resourceCoolLeftTabIndex=1">图片库</li>
                    </ul>
                </div>
                <div class="right-box">
                    <ul class="video-resource-box">
                        <li v-for="(item,index) in videoLists" :key="index">
                            <div class="img-box">
                                <el-image
                                        class="cover-img"
                                        style="width: 100%; height: 100%"
                                        :src="item.url"
                                        :fit="imgFit"></el-image>
                                <el-image
                                        class="hover-img"
                                        style="width: 100%; height: 100%"
                                        :src="item.gifUrl"
                                        :fit="imgFit"></el-image>
                            </div>
                            <div class="name overHidden1">{{item.name}}</div>
                        </li>
                    </ul>
                    <!--  空数据提示    -->
                    <div class="loading-div">
                        <img src="../../assets/images/manage_admin/loading-no.png" height="150" width="180"/>
                    </div>
                </div>

            </div>
        </el-dialog>
    </div>
</body>
<script src="../../assets/lib/vue.js"></script>
<script src="../../assets/lib/element-ui/lib/index.js"></script>
<script>
    var vm = new Vue({
        el: '#website-setting',
        data () {
            return {
                websiteJson: {
                    name: '成都图书馆',
                    intro:''
                },
                fileList:[],
                fileList2:[],
                fileList3:[],
                fileList4:[],
                resourceCoolPop:false,
                resourceCoolLeftTabIndex:0,
                imgFit:'cover',
                videoLists: [
                    {
                        name: '成都图书馆',
                        url: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg',
                        gifUrl: '../../assets/images/load.gif'
                    }, {
                        name: '视频名称视频名称视频名称视频名称视频名称1',
                        url: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg',
                        gifUrl: '../../assets/images/mhload.gif'
                    }, {
                        name: '视频名称视频名称视频名称视频名称视频名称2',
                        url: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg',
                        gifUrl: '../../assets/images/mhload.gif'
                    }, {
                        name: '视频名称视频名称视频名称视频名称视频名称3',
                        url: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg',
                        gifUrl: '../../assets/images/mhload2.gif'
                    }, {
                        name: '视频名称视频名称视频名称视频名称视频名称4',
                        url: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg',
                        gifUrl: '../../assets/images/mhload.gif'
                    }, {
                        name: '视频名称视频名称视频名称视频名称视频名称1',
                        url: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg',
                        gifUrl: '../../assets/images/mhload.gif'
                    }, {
                        name: '视频名称视频名称视频名称视频名称视频名称2',
                        url: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg',
                        gifUrl: '../../assets/images/mhload.gif'
                    }, {
                        name: '视频名称视频名称视频名称视频名称视频名称3',
                        url: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg',
                        gifUrl: '../../assets/images/mhload2.gif'
                    }, {
                        name: '视频名称视频名称视频名称视频名称视频名称4',
                        url: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg',
                        gifUrl: '../../assets/images/mhload.gif'
                    }, {
                        name: '视频名称视频名称视频名称视频名称视频名称1',
                        url: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg',
                        gifUrl: '../../assets/images/mhload.gif'
                    }, {
                        name: '视频名称视频名称视频名称视频名称视频名称2',
                        url: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg',
                        gifUrl: '../../assets/images/mhload.gif'
                    }, {
                        name: '视频名称视频名称视频名称视频名称视频名称3',
                        url: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg',
                        gifUrl: '../../assets/images/mhload2.gif'
                    }, {
                        name: '视频名称视频名称视频名称视频名称视频名称4',
                        url: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg',
                        gifUrl: '../../assets/images/mhload.gif'
                    }
                ]

            }
        },
        methods: {
            handleRemove (file, fileList) {
                console.log(file, fileList);
            },
            handlePreview (file) {
                console.log(file);
            },
            handleExceed (files, fileList) {
                this.$message.warning(`当前限制选择 3 个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length} 个文件`);
            },
            beforeRemove (file, fileList) {
                return this.$confirm(`确定移除 ${file.name}？`);
            }
        },
    })
</script>

</html>
