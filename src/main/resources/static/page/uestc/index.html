<!doctype html>
<html lang="zh-CN">
<head>
    <!--<meta charset="UTF-8">-->
    <!--<meta name="keywords" content="智慧门户"/>-->
    <!--<meta name="description" content="智慧门户"/>-->
    <!--<meta http-equiv="X-UA-Compatible" content="ie=edge">-->
    <!--<meta name="renderer" content="webkit">-->
    <!--<meta name="viewport" content="width=device-width,initial-scale=1.0,minimum-scale=1.0,maximum-scale=1.0,user-scalable=0">-->
    <!--<title>智慧门户</title>-->

     <link rel="stylesheet" href="//static.mh.chaoxing.com/assets/css/index_vendor.min.css?v=20250508">
    <link rel="stylesheet" href="/assets/lib/layui/css/layui.css?v=020911">
    <link data-id="myStyle"  rel="stylesheet" href="/assets/icomoon/style.css?v=20250508">
    <link rel="stylesheet" href="/assets/css/common.css?v=20250508">
    <link rel="stylesheet" href="//static.mh.chaoxing.com/assets/css/index_page.min.css?v=20250508">

    <link rel="stylesheet" href="//static.mh.chaoxing.com/assets/css/uestc/editor.css?v=20250508">

    <link rel="stylesheet" href="//static.mh.chaoxing.com/assets/css/media_screen.css?v=20250508">

    <style>
        [v-cloak] {
            display: none;
        }
    </style>
</head>
<body class="responsive-page">
<div id="index" v-cloak>

    <!--header-->
    <div id="header"  data-app-id="header" class="full-width base-container" v-if="showHeader"></div>

    <!--左侧导航的展开收起图标-->
    <div id="navontroller" class="expand bg-theme"><span class="icon-pack-down"></span></div>

    <div class="content-wrap">
        <!--左侧导航-->
        <div id="leftNavBox" class="layui-side  w-side-menu">
            <div class="layui-side-scroll">
                <ul class="layui-nav layui-nav-tree" lay-filter="pageNavClick">
                    <li v-for="page in pagesList"
                        class="layui-nav-item layui-nav-itemed"
                        :class="page.id===activePageId ?'layui-this':''">
                        <a href="javascript:;" @click="pageNavClick(page)">{{page.name}}</a>
                        <ol v-if="page.listSub.length>0" class="layui-nav-child">
                            <dd v-for="subPage in page.listSub"
                                :class="subPage.id===activePageId ?'layui-this':''">
                                <a href="javascript:;" @click="pageNavClick(subPage)">{{subPage.name}}</a>
                                <dl v-if="subPage.listSub.length>0" class="layui-nav-child">
                                    <dd v-for="thirdPage in subPage.listSub"
                                        :class="thirdPage.id===activePageId?'layui-this':''"
                                        @click="pageNavClick(thirdPage)">
                                        <a href="javascript:;">{{thirdPage.name}}</a>
                                    </dd>
                                </dl>
                            </dd>
                        </ol>
                    </li>
                </ul>
            </div>
            <!--悬浮bubble-->
            <div class="bubble" id="pageNavBubble"><span class="bubble-text">测试文案</span></div>
        </div>

        <div class="right-content-wrap">
            <div v-show="showPaging" class="layui-icon w-tabs-control layui-icon-prev" id="leftPage"></div>
            <div v-show="showPaging" class="layui-icon w-tabs-control layui-icon-next" id="rightPage"></div>
            <div class="layui-tab" :class="showPaging?'paddingl40':''">
                <ul class="layui-tab-title" id="layuiTabTitle">
                    <li v-for="(page,index) in opendPageList"
                        :class="page.id===activePageId || (index===0 && !activePageId) ?'layui-this txt-theme':''">
                        <div class="title-text" @click="pagetabClick(page.id)">
                            {{index===0?'首页':page.name}}<span class="right-filter"></span>
                        </div>
                        <span v-if="index>0" class="icon icon-del"
                              @click="closePageTab($event,page.id,opendPageList[index-1].id)"></span>
                    </li>
                </ul>
                <div class="layui-tab-content">
                    <div v-for="(pageContent,index) in opendPageList" class="layui-tab-item"
                         :class="pageContent.id===activePageId?'layui-show':''">
                        <div v-if="index===0">
                            <!--首屏-->
                            <div id="firstSection" class="multi-nest-container">
                                <!--banner-->
                                <div id="banner"  data-app-id="full-banner" class="base-container" v-if="showBanner"></div>
                                <div class="grid-wrap nest-container">
                                    <div class="grid-wrap-border">
                                        <div id="grid1" class="grid-stack"
                                             :data-nav-id="leftNavs.length>0?leftNavs[0].id:''"></div>
                                    </div>
                                </div>
                            </div>
                            <!--中间内容-->
                            <div class="content">
                                <!--aid-->
                                <div class="aid-group-wrap">
                                    <div class="aid-groups">
                                        <!--左边导航-->
                                        <div id="anchorsNav" :class="showMao?'':'hide'">
                                            <div v-show="showToTopDis">
                                                <ul class="anchors-nav" :class="'right-nav'+leftNavSets.typeId" v-if="leftNavSets.typeId<4">
                                                    <li v-for="(item,index) in leftNavs">
                                                        <a :href="'#grid'+(index+1)">
                                                            <span class="nav-name-span">{{item.name}}</span>
                                                        </a>
                                                    </li>
                                                    <li class="back-top" v-show="showToTopDis" onclick="backTop('.layui-tab-content')">
                                                        <p>顶部</p>
                                                        <i class="icon-up"></i>
                                                    </li>
                                                </ul>
                                                <!--公用样式1-->
                                                <ul class="anchors-nav common-right-nav1" v-if="leftNavSets.typeId==4">
                                                    <li v-for="(item,index) in leftNavs" class="nav">
                                                        <a :href="'#grid'+(index+1)">
                                                            <img v-if="item.url" :src="item.url" class="pagination-item-icon">
                                                            <p v-else class="placeholder"></p>
                                                            <p class="overHidden1" :title="item.name">{{item.name}}</p>
                                                        </a>
                                                    </li>
                                                    <li class="back-top" v-show="showToTopDis" onclick="backTop('.layui-tab-content')">
                                                        <p>顶部</p>
                                                        <i class="icon-up"></i>
                                                    </li>
                                                </ul>
                                                <!--公共样式2-->
                                                <ul class="anchors-nav common-right-nav2" v-if="leftNavSets.typeId==5">
                                                    <li v-for="(item,index) in leftNavs">
                                                        <a :href="'#grid'+(index+1)">
                                                            <img v-if="item.url" :src="item.url" class="pagination-item-icon">
                                                            <p class="anchors-name overHidden1">{{item.name}}</p>
                                                        </a>
                                                    </li>
                                                    <li class="back-top" v-show="showToTopDis"
                                                        onclick="backTop('.layui-tab-content')">
                                                        <p>顶部</p>
                                                        <i class="icon-up"></i>
                                                    </li>
                                                </ul>
                                            </div>

                                        </div>
                                        <div v-if="index>0" class="grid-wrap"
                                             v-for="(item,index) in leftNavs">
                                            <div :id="'grid'+(index+1)" class="grid-stack" :data-nav-id="item.id"></div>
                                        </div>
                                    </div>

                                    <!--背景列表-->
                                    <ul id="bgs" :class="bgMode===1?'single-bg-mode':''">
                                        <li v-for="bg in pageBgs" :class="bg.img?'img-bg':'color-bg'">
                                            <div v-if="bg.img"
                                                 :style="{'height':bg.imgHeight+'px'}">
                                                <div v-if="bg.styleClass" class="bg-box"
                                                     :class="bg.styleClass"
                                                     :style="{'background-image':'url('+bg.img+')','opacity':bg.ransparency}"></div>
                                                <div v-else class="bg-box"
                                                      :class="{'img-stretch':bg.showStyle==0, 'img-tile':bg.showStyle==1, 'img-center':bg.showStyle==2}"
                                                     :style="{'background-image':'url('+bg.img+')','opacity':bg.ransparency}"></div>
                                            </div>
                                            <div v-else
                                                 :style="{'height': bg.bgColorHeight+'px'}">
                                                <div class="bg-box" :style="{'background':bg.bgColor}"></div>
                                            </div>
                                            <div></div>
                                        </li>
                                    </ul>
                                </div>

                            </div>

                            <!--footer-->
                            <div id="footer" data-app-id="footer"  data-show-status="showFooter" class="full-width base-container" v-show="showFooter"></div>
                        </div>
                        <div class="iframe-wrap" v-else>
                            <iframe scrolling="auto" frameborder="0" :src="pageContent.url" class="w-iframe"></iframe>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- 背景音乐 -->
    <bg-music v-show="musicStyle.show" ref="bgMusic"></bg-music>
</div>
<script type="text/javascript">
    window.jQuery || document.write('<script src="//static.mh.chaoxing.com/assets/lib/jquery.min.js"><\/script>');
    window.layui || document.write('<script src="//static.mh.chaoxing.com/assets/lib/layui/layui.js?v=020911"><\/script>');
    window.Vue || document.write('<script src="//static.mh.chaoxing.com/assets/lib/vue.js"><\/script>');
</script>

<script>
    function loadScript(url, callback){
        var script = document.createElement ("script")
        script.type = "text/javascript";
        script.src = url;
        script.defer = false;
        script.async = false;
        document.getElementsByTagName("head")[0].appendChild(script);
        if (script.readyState){ //IE
            script.onreadystatechange = function(){
                if (script.readyState == "loaded" || script.readyState == "complete"){
                    script.onreadystatechange = null;
                    callback();
                }
            };
        } else { //Others
            script.onended = callback();
            // script.onloadeddata = callback();
        }
    }

    //依次下载脚本文件
    loadScript("//static.mh.chaoxing.com/assets/js/index_all.min.js?v=20250508", function(){
        loadScript("//static.mh.chaoxing.com/assets/js/component/bg_music.js?v=20250508", function(){
            loadScript("//static.mh.chaoxing.com/assets/js/index_page.min.js?v=20250508", function(){
                loadScript("//static.mh.chaoxing.com/assets/js/uestc/index.js?v=20250508", function(){
                    loadScript("//static.mh.chaoxing.com/assets/js/nestElementHandle.js?v=20250508", function(){
                    });
                });
            });
        });
    });
</script>
</body>
</html>

