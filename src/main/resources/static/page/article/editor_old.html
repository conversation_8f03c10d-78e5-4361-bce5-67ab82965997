<!doctype html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <meta name="renderer" content="webkit">
    <!--<meta name="viewport" content="width=device-width,initial-scale=1.0,minimum-scale=1.0,maximum-scale=1.0,user-scalable=0">-->
    <title></title>

    <!--gridstack 样式表-->
    <link rel="stylesheet" href="/assets/lib/gridstack/gridstack.css">
    <link rel="stylesheet" href="/assets/lib/gridstack/gridstack-extra.css">

    <!--select2-->
    <link rel="stylesheet" href="/assets/lib/select2/select2.min.css">

    <!--swiper-->
    <link rel="stylesheet" href="/assets/lib/swiper2/idangerous.swiper.css">
    <link rel="stylesheet" href="/assets/lib/swiper2/idangerous.swiper.3dflow.css">

    <!--jquery ui-->
    <link rel="stylesheet" href="/assets/lib/jQueryUi/jquery.ui.css">

    <!--滚动条美化-->
    <link rel="stylesheet" href="/assets/lib/mCustomScrollBar/jquery.mCustomScrollbar.css">

    <!--颜色选择-->
    <link rel="stylesheet" href="/assets/lib/spectrum/spectrum.css">

    <!--图标-->
    <link rel="stylesheet" href="/assets/lib/fontawesome/css/all.min.css">
     <link rel="stylesheet" href="/assets/icomoon/style.css?v=20250508">

    <!--词云-->
    <link rel="stylesheet" href="/assets/lib/jqcloud/jqcloud.css">

    <!--通用-->
    <link rel="stylesheet" href="/assets/css/common.css?v=20250508">
    <!--通用左侧面板的样式-->
    <link rel="stylesheet" href="/assets/css/pop_nav.css?v=20250508">

    <!--通用顶部的样式-->
    <link rel="stylesheet" href="/assets/css/general_top.css?v=20250508">

    <link rel="stylesheet" href="/assets/css/page_header.css?v=20250508">
    <link rel="stylesheet" href="/assets/css/article/editor.css?v=20250508">
    <link rel="stylesheet" href="/assets/css/index_common.css?v=20250508">

    <!--自适应的样式-->
    <!--<link rel="stylesheet" href="../assets/css/media.css">-->
</head>
<body class="responsive-page grid-bottom-spacer">
<div id="index" class="editor-page" v-cloak>
    <!--顶部-->
    <general-top ref="generalTop" @toast="showToastEvent" @save="getCoordinates"></general-top>

    <!--header-->
    <div id="header" class="full-width base-container" v-show="showHeader"></div>

    <!--中间内容-->
    <div class="content">
        <div>
            <textarea id="title" placeholder="标题" v-model="contentInfo.title"></textarea>
            <div>
                <input id="author" type="text" placeholder="请输入作者" v-model="contentInfo.author">
            </div>
            <div>
                <input readonly type="text" id="publicDate" class="layui-input" placeholder="请选择日期"
                       v-model="contentInfo.date">
            </div>
            <textarea id="zEditorBox">{{contentInfo.content}}</textarea>
        </div>
    </div>

    <!--toast 弹框-->
    <div class="toast" v-show="showToast">
        <span v-if="toastType"
              class="icon"
              :class="{'icon-failure':toastType =='failure','icon-succeed':toastType=='success'}"></span>
        <span>{{toastMessage}}</span>
    </div>
    <!--footer-->
    <div id="footer" class="full-width base-container" v-show="showFooter"></div>
</div>

<script src="/assets/lib/jquery.min.js"></script>
<script src="/assets/lib/jquery-migrate.min.js"></script>
<script src="/assets/lib/vue.js"></script>

<!--拖拽，拉伸-->
<script src="/assets/lib/jQueryUi/jquery.ui.js"></script>
<script src="/assets/lib/gridstack/gridstack.js"></script>
<script src="/assets/lib/gridstack.jQueryUI.js"></script>

<!--滚动条美化-->
<script src="/assets/lib/mCustomScrollBar/jquery.mCustomScrollbar.min.js"></script>

<!--layui-->
<link rel="stylesheet" href="/assets/lib/layui/css/layui.css?v=020911">

<!--select2-->
<script src="/assets/lib/select2/select2.min.js"></script>

<!--自定义颜色-->
<script src="/assets/lib/spectrum/spectrum.js"></script>
<script src="/assets/js/select_color.js"></script>

<!--common.js-->
<script src="/assets/js/common.js?v=20250508"></script>

<!--通用页面顶部-->
<script src="/assets/js/component/general_top.js?v=20250508"></script>

<!--layui-->
<script src="/assets/lib/layui/layui.js?v=020911"></script>

<!--编辑器样式-->
<script src="/assets/lib/ckeditor/ckeditor.js"></script>

<!--页面逻辑-->
<script src="/assets/js/editor_common.js?v=20250508"></script>
<script src="/assets/js/base_module_drag.js?v=20250508"></script>
<script src="/assets/js/article/editor.js?v=20250508"></script>

</body>
</html>

