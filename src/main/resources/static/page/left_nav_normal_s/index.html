<!doctype html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="keywords" content="智慧门户"/>
    <meta name="description" content="智慧门户"/>
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <meta name="renderer" content="webkit">
    <meta name="viewport" content="width=device-width,initial-scale=1.0,minimum-scale=1.0,maximum-scale=1.0,user-scalable=0">
    <title>智慧门户</title>

     <link rel="stylesheet" href="//static.mh.chaoxing.com/assets/css/index_vendor.min.css?v=20250508">
    <link rel="stylesheet" href="/assets/lib/layui/css/layui.css?v=020911">
    <link data-id="myStyle" rel="stylesheet" href="/assets/icomoon/style.css?v=20250508">
    <link rel="stylesheet" href="/assets/css/common.css?v=20250508">
    <link rel="stylesheet" href="//static.mh.chaoxing.com/assets/css/index_page.min.css?v=20250508">
    <link rel="stylesheet" href="//static.mh.chaoxing.com/assets/css/left_nav_normal/editor.css?v=20250508">

    <!--自适应样式-->
    <link rel="stylesheet" href="//static.mh.chaoxing.com/assets/css/media_screen.css?v=20250508">
    <style>
        [v-cloak] {
            display: none;
        }
    </style>
</head>
<body class="grid-bottom-spacer">
<div id="index" v-cloak :class="{'middle-area-1200':leftNavType=='3004'}">
    <!--header-->
    <div id="header"  data-app-id="header"  class="base-container" v-if="showHeader"></div>

    <!--banner-->
    <div id="banner" data-app-id="full-banner"  class="base-container" v-if="showBanner"></div>

    <!--中间内容-->
    <div class="content">
        <!--sid-->
        <div id="sidGroups"></div>

        <!--aid-->
        <div class="aid-groups">
            <div class="grid-wrap" :class="leftNavType=='3004'?'middle-size':''">
                <!--左侧导航-->
                <div id="contentNavs" class="layui-side  w-side-menu" :class="{'nav-style3':leftNavType=='3003','nav-style4':leftNavType=='3004'}">
                    <div class="layui-side-scroll">
                        <ul class="layui-nav layui-nav-tree" lay-filter="pageNavClick">
                            <li v-for="(page,index) in pagesList"
                                class="layui-nav-item" :class="index===0?'layui-nav-itemed':''">
                                <a class="first-nav"
                                   :data-nav-id="page.id"
                                   >
                                    <span class="nav-name" @click="pageNavClick(page,$event)"> {{page.name}}</span>
                                </a>
                                <ol v-if="page.listSub.length>0" class="layui-nav-child">
                                    <dd v-for="subPage in page.listSub"  :class="index===0?'layui-nav-itemed':''">
                                        <a :data-nav-id="subPage.id">
                                            <span class="nav-name" @click="pageNavClick(subPage,$event)">{{subPage.name}}</span>
                                        </a>
                                        <dl v-if="subPage.listSub.length>0" class="layui-nav-child">
                                            <dd v-for="thirdPage in subPage.listSub">
                                                <a class="third-nav-title"
                                                   :data-nav-id="thirdPage.id"
                                                   >

                                                    <span class="nav-name" @click="pageNavClick(thirdPage,$event)">{{thirdPage.name}}</span>
                                                </a>
                                            </dd>
                                        </dl>
                                    </dd>
                                </ol>
                            </li>
                        </ul>
                    </div>
                </div>
                <div class="grid-stack-groups">
                    <!--面包屑-->
                    <div class="bread-crumbs-container" v-if="leftNavType=='3004'">
                        <ul class="bread-crumbs bder-theme">
                            <li v-for="(breadcrumb,index) in breadcrumbs">
                                <a :href="breadcrumb.url?breadcrumb.url:'javascript:;'"
                                   :class="index == breadcrumbs.length-1?'txt-theme':''">
                                    {{breadcrumb.name}}</a>
                                {{index == breadcrumbs.length-1?'':'/'}}
                            </li>
                        </ul>
                    </div>
                    <div class="grid-stack" id="grid1"></div>
                </div>
            </div>
            <!--背景列表-->
            <ul id="bgs" :class="bgMode===1?'single-bg-mode':''">
                <li v-for="bg in pageBgs" :class="bg.img?'img-bg':'color-bg'">
                    <div v-if="bg.img"
                         :style="{'height':bg.imgHeight+'px'}">
                        <div v-if="bg.styleClass" class="bg-box"
                             :class="bg.styleClass"
                             :style="{'background-image':'url('+bg.img+')','opacity':bg.ransparency}"></div>
                        <div v-else class="bg-box"
                              :class="{'img-stretch':bg.showStyle==0, 'img-tile':bg.showStyle==1, 'img-center':bg.showStyle==2}"
                             :style="{'background-image':'url('+bg.img+')','opacity':bg.ransparency}"></div>
                    </div>
                    <div v-else
                         :style="{'height': bg.bgColorHeight+'px'}">
                        <div class="bg-box" :style="{'background':bg.bgColor}"></div>
                    </div>
                </li>
            </ul>
        </div>

    </div>

    <!--飘窗-->
    <bay-window  ref="bayWindow" v-show="showBayWindow"></bay-window>

    <!-- 背景音乐 -->
    <bg-music v-show="musicStyle.show" ref="bgMusic"></bg-music>
    <!--footer-->
    <div id="footer" data-app-id="footer" data-show-status="showFooter" class="base-container" v-show="showFooter"></div>

</div>
<script type="text/javascript">
    window.jQuery || document.write('<script src="//static.mh.chaoxing.com/assets/lib/jquery.min.js"><\/script>');
    window.layui || document.write('<script src="//static.mh.chaoxing.com/assets/lib/layui/layui.js?v=020911"><\/script>');
    window.Vue || document.write('<script src="//static.mh.chaoxing.com/assets/lib/vue.js"><\/script>');
</script>
<script>
    function loadScript(url, callback){
        var script = document.createElement ("script")
        script.type = "text/javascript";
        script.src = url;
        script.defer = false;
        script.async = false;
        document.getElementsByTagName("head")[0].appendChild(script);
        if (script.readyState){ //IE
            script.onreadystatechange = function(){
                if (script.readyState == "loaded" || script.readyState == "complete"){
                    script.onreadystatechange = null;
                    callback();
                }
            };
        } else { //Others
            script.onended = callback();
            // script.onloadeddata = callback();
        }
    }

    //依次下载脚本文件
    loadScript("/assets/lib/iframeHeight/set-iframe-height-parent.js", function(){
    loadScript("//static.mh.chaoxing.com/assets/js/index_all.min.js?v=20250508", function(){
        loadScript("//static.mh.chaoxing.com/assets/js/component/bg_music.js?v=20250508", function(){
            loadScript("//static.mh.chaoxing.com/assets/js/index_page.min.js?v=20250508", function(){
                loadScript("//static.mh.chaoxing.com/assets/js/left_nav_normal_s/index.js?v=20250508", function(){
                    loadScript("//static.mh.chaoxing.com/assets/js/nestElementHandle.js?v=20250508", function(){
                    });
                });
            });
        });
    });
    });
</script>
</body>
</html>

