<!doctype html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <meta name="renderer" content="webkit">
    <!--<meta name="viewport"-->
    <!--content="width=device-width,initial-scale=1.0,minimum-scale=1.0,maximum-scale=1.0,user-scalable=0">-->
    <title></title>

    <link rel="stylesheet" href="/assets/css/vendor.min.css">
    <link rel="stylesheet" href="/assets/lib/layui/css/layui.css?v=020911">
     <link rel="stylesheet" href="/assets/icomoon/style.css?v=20250508">
    <link rel="stylesheet" href="/assets/css/page.min.css?v=20250508">

    <link rel="stylesheet" href="/assets/css/free_overlap/editor.css?v=20250508">

</head>
<body class="responsive-page">
<div id="index" class="editor-page" v-cloak>
    <!--顶部-->
    <general-top ref="generalTop" @toast="showToastEvent" @save="getCoordinates">
        <!--<div class="layout-mode-switch">-->
            <!--<span>布局模式</span>-->
            <!--<div id="layoutSwitch" @click="switchLayoutMode" class="unselect  form-switch "><em>关</em><i></i></div>-->
        <!--</div>-->
    </general-top>

    <!--popNav-->
    <pop-nav
            :bgs="pageBgs"
            :bgmusic="musicStyle"
            :showheader="showHeader"
            :showfooter="showFooter"
            :showbanner="showBanner"
            :showothermodule="otherGlobalModule"
            :webjson="webJson"
            @updateglobalmodule="updateModule"
            @updatebg="updateBg"
            @updatebgmusic="updateBgmusic"></pop-nav>
    <!--header-->
    <div id="header" class="full-width base-container" v-show="showHeader"></div>

    <div class="container">
        <!--首屏-->
        <div id="firstSection" class="multi-nest-container">
            <!--banner-->
            <div id="banner" class="base-container" v-show="showBanner"></div>
            <div class="grid-wrap editor-grid nest-container">
                <div class="grid-wrap-border">
                    <div id="grid1" class="grid-stack" :data-nav-id="leftNavs.length>0?leftNavs[0].id:''"></div>
                </div>
            </div>
        </div>

        <!--sid-->
        <div id="sidGroups">
            <!--快捷入口-->
            <div id="quickEntry" v-show="showQuery">
                <div class="general-set">
                    <div>
                        <a class="set-btn" :href="'/engine2/shortcut/entry/admin/'+pageId" target="_blank">
                            <span class="icon-set"></span><span>设置</span>
                        </a>
                    </div>
                </div>
            </div>
            <!--返回顶部-->
            <div id="toTop" v-show="showToTop && showToTopDis">
                <div class="general-set">
                    <div>
                        <a class="set-btn" :href="'/engine2/scroll/top/admin/'+pageId" target="_blank">
                            <span class="icon-set"></span><span>设置</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!--背景-->
        <ul v-if="!showBanner" id="bgs" :class="bgMode===1?'single-bg-mode':''">
            <li v-for="bg in pageBg" :class="bg.img?'img-bg':'color-bg'">
                <div v-if="bg.img"
                     :style="{'height':bg.imgHeight+'px'}">
                    <div v-if="bg.styleClass" class="bg-box"
                         :class="bg.styleClass"
                         :style="{'background-image':'url('+bg.img+')','opacity':bg.ransparency}"></div>
                    <div v-else class="bg-box"
                          :class="{'img-stretch':bg.showStyle==0, 'img-tile':bg.showStyle==1, 'img-center':bg.showStyle==2}"
                         :style="{'background-image':'url('+bg.img+')','opacity':bg.ransparency}"></div>
                    <span class="bg-height fs14">{{bg.imgHeight}}</span>
                </div>
                <div v-else
                     :style="{'height': bg.bgColorHeight+'px'}">
                    <div class="bg-box" :style="{'background':bg.bgColor}"></div>
                    <span class="bg-height fs14">{{bg.bgColorHeight}}</span>
                </div>
                <div></div>
            </li>
        </ul>

        <!--中间内容-->
        <div class="content" :class="showBanner?'full-width':''">
            <!--aid-->
            <div class="aid-group-wrap">
                <div class="aid-groups">
                    <div v-if="index>0" class="grid-wrap editor-grid" v-for="(item,index) in leftNavs">
                        <div :id="'grid'+(index+1)" class="grid-stack" :data-nav-id="item.id"></div>
                    </div>
                </div>
                <!--左边导航-->
                <div id="anchorsNav" :class="{'hide': !showMao,'anchorPositoinTL':leftNavSets.typeId && leftNavSets.typeId==8}">
                    <div class="general-set">
                        <div>
                            <a class="set-btn" target="_blank" :href="'/engine2/navigation/admin/basic?pageId='+pageId">
                                <span class="icon-set"></span><span>设置</span>
                            </a>
                        </div>
                    </div>
                    <ul class="anchors-nav" :class="'right-nav'+leftNavSets.typeId" v-if="leftNavSets.typeId<4">
                        <li v-for="(item,index) in leftNavs">
                            <span class="nav-name">{{item.name}}</span>
                             <a :href="index===0?'#header':'#grid'+(index+1)">{{index>8 ?index+1:'0'+(index+1)}}</a>
                        </li>
                    </ul>

                    <!--公用样式1-->
                    <ul class="anchors-nav common-right-nav1" v-if="leftNavSets.typeId==4">
                        <li v-for="(item,index) in leftNavs" class="nav">
                            <a :href="index===0?'#header':'#grid'+(index+1)">
                                <img v-if="item.url" :src="item.url" class="pagination-item-icon">
                                <p v-else class="placeholder"></p>
                                <p class="overHidden1" :title="item.name">{{item.name}}</p>
                            </a>
                        </li>
                    </ul>
                    <!--公共样式2-->
                    <ul class="anchors-nav common-right-nav2" v-if="leftNavSets.typeId==5">
                        <li v-for="(item,index) in leftNavs">
                            <a :href="index===0?'#header':'#grid'+(index+1)">
                                <img v-if="item.url" :src="item.url" class="pagination-item-icon">
                                <p class="anchors-name overHidden1">{{item.name}}</p>
                            </a>
                        </li>
                    </ul>
                    <!-- 公共样式6 -- 复用后台管理样式4 -->
                    <ul class="anchors-nav common-right-nav3" v-if="leftNavSets.typeId==6">
                        <li v-for="(item,index) in leftNavs" class="nav bg-hover-theme">
                            <a :href="index===0?'#header':'#grid'+(index+1)">
                                <img v-if="item.url" :src="item.url" class="pagination-item-icon img-normal">
                                <img v-if="item.url" :src="item.url2" class="pagination-item-icon img-hover">
                                <p v-else class="placeholder"></p>
                                <p class="overHidden1">{{item.name}}</p>
                            </a>
                        </li>
                    </ul>
                    <!-- 公共锚点样式8，展开收起 -->
                    <div class="common-left-nav1" v-if="leftNavSets.typeId && leftNavSets.typeId==8" :style="{'background-image':'url('+leftNavSets.logoUrl+')'}">
                        <ul class="anchors-nav txt-theme fs22">
                            <li v-for="(nav,index) in leftNavs" class="nav">
                                <a :href="index===0?'#header':'#grid'+(index+1)">
                                    <p class="overHidden1">{{nav.name}}</p>
                                </a>
                            </li>
                        </ul>
                        <div class="toggle-box" @click="anchorToggleFunc">
                            <div class="btn-box bg-theme fs18 btn-open">
                                <span class="txt-1">收起</span><span class="txt-2">展开</span><i class="icon-arrow-down2"></i>
                            </div>
                        </div>
                    </div>
                    <!-- 锚点样式9 -->
                    <div class="anchors-nav common-right-nav4" v-if="leftNavSets.typeId && leftNavSets.typeId==9" :style="{'background-image':'url('+leftNavSets.logoUrl+')'}">
                        <ul class="anchors-nav">
                            <li v-for="(item,index) in leftNavs" class="nav">
                                <a :href="index===0?'#firstSection':'#grid'+(index+1)">
                                    <div class="img-box"><img v-if="item.url" :src="item.url" class="pagination-item-icon"></div>
                                    <p class="overHidden1">{{item.name}}</p>
                                </a>
                            </li>
                        </ul>
                        <div class="nav-totop">
                            <a href="#header">
                                <div class="img-box"><img src="/assets/images/to_top1.png" class="pagination-item-icon"></div>
                                <p class="overHidden1">返回顶部</p>
                            </a>
                        </div>
                    </div>
                    <!-- 锚点样式10，展开收起-->
                    <div class="pagination-color common-right-nav5"
                         v-if="leftNavSets.typeId && leftNavSets.typeId==10">
                        <div class="logo-box" @click="anchors10Toggle($event)">
                            <img :src="leftNavSets.logoUrl" alt="">
                        </div>
                        <ul class="anchors-nav bg-theme-rgba20">
                            <li v-for="(item,index) in leftNavs" class="nav">
                                <a href="#" class="bg-hover-theme">
                                    <div class="img-box">
                                        <img v-if="item.url" :src="item.url" class="pagination-item-icon">
                                        <img v-if="item.url2" :src="item.url2" class="pagination-item-icon-active">
                                    </div>
                                    <span class="overHidden1">{{item.name}}</span>
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>

                <!--背景列表-->
                <ul id="bgs" v-if="showBanner" :class="bgMode===1?'single-bg-mode':''">
                    <li v-for="bg in pageBg" :class="bg.img?'img-bg':'color-bg'">
                        <div v-if="bg.img"
                             :style="{'height':bg.imgHeight+'px'}">
                            <div v-if="bg.styleClass" class="bg-box"
                                 :class="bg.styleClass"
                                 :style="{'background-image':'url('+bg.img+')','opacity':bg.ransparency}"></div>
                            <div v-else class="bg-box"
                                  :class="{'img-stretch':bg.showStyle==0, 'img-tile':bg.showStyle==1, 'img-center':bg.showStyle==2}"
                                 :style="{'background-image':'url('+bg.img+')','opacity':bg.ransparency}"></div>
                            <span class="bg-height fs14">{{bg.imgHeight}}</span>
                        </div>
                        <div v-else
                             :style="{'height': bg.bgColorHeight+'px'}">
                            <div class="bg-box" :style="{'background':bg.bgColor}"></div>
                            <span class="bg-height fs14">{{bg.bgColorHeight}}</span>
                        </div>
                        <div></div>
                    </li>
                </ul>
            </div>

        </div>
    </div>

    <!--编辑模块名称弹框-->
    <div id="nameEdit" class="pop name-edit-pop" v-show="showEditName">
        <div class="pop-content">
            <div class="top clear">
                <span class="fs16 colo666 fl" id="moduleTitle"></span>
                <i class="icon icon-del fr canncle-edit-name"></i>
            </div>
            <div class="middle fs14 col333">
                <label>应用生成名称:</label>
                <input class="inp-name" id="moduleNewName" type="text" placeholder="输入模块名称">
            </div>
            <div class="bottom-btns">
                <input class="btn btn-fff-auto canncle-edit-name" type="button" value="取消">
                <input class="btn btn-blue-auto" type="button" value="保存" id="saveName">
            </div>
        </div>
    </div>

    <!--toast 弹框-->
    <div class="toast" v-show="showToast">
        <span v-if="toastType"
              class="icon"
              :class="{'icon-failure':toastType =='failure','icon-succeed':toastType=='success'}"></span>
        <span>{{toastMessage}}</span>
    </div>

    <!--常用模块编辑栏-->
    <edit-btn @toast="showToastEvent"></edit-btn>

    <!--按钮的基本设置-->
    <module-set ref="moduleSet" @toast="showToastEvent"></module-set>

    <!--常用图片编辑-->
    <edit-img></edit-img>

    <!--素材库-->
    <picture-lib ref="pictureLib"></picture-lib>

    <!--飘窗-->
    <bay-window-set ref="bayWindowSet"></bay-window-set>
    <bay-window v-show="showBayWindow" ref="bayWindow"></bay-window>

    <!--视频设置-->
    <video-set ref="videoSet"></video-set>

    <!--横向标签样式设置-->
    <layout-tab-set></layout-tab-set>

    <!--嵌套搜索的设置-->
    <search-set ref="searchSet"></search-set>

    <!--天气日历的设置-->
    <weather-date-set ref="weatherDate"></weather-date-set>

    <!--    插件设置-->
    <plugin-set ref="pluginSet"></plugin-set>

    <!--footer-->
    <div id="footer" class="full-width base-container" v-show="showFooter"></div>
    <!-- 页面锁定，禁止编辑 弹窗 -->
    <page-close ref="showPageForbid"></page-close>
    <!-- 背景音乐 -->
    <bg-music v-show="musicStyle.show" ref="bgMusic"></bg-music>
    <!--    地图设置-->
    <maps-set ref="mapsSet"></maps-set>
    <!--下拉嵌套设置-->
    <edit-select ref="selectSet"></edit-select>
</div>

<script src="/assets/lib/jquery.min.js"></script>
<script src="/assets/lib/vue.js"></script>

<!--layui-->
<script src="/assets/lib/layui/layui.js?v=020911"></script>
<script src="/assets/lib/spark-md5.min.js"></script>

<!--echart-->
<script src="/assets/lib/echarts4/echarts.min.js"></script><script src="/assets/lib/echarts4/china.js"></script><script src="/assets/lib/echarts4/world.js"></script>

<script>
    /*baidu map https 适配*/
    if(document.location.protocol.split(':')[0] == 'https'){
        window.HOST_TYPE='2';
    }
</script>
<!--打包好的所有插件-->
<script src="/assets/js/all.min.js"></script>


<!--编辑按钮样式,设置-->
<script src="/assets/js/component.min.js"></script>
<!--编辑器样式-->
<script src="/assets/lib/ckeditor/ckeditor.js"></script>

<!--页面逻辑-->
<script src="/assets/js/page.min.js?v=20250508"></script>
<script src="/assets/js/free_overlap/editor.js?v=20250508"></script>

</body>
</html>
