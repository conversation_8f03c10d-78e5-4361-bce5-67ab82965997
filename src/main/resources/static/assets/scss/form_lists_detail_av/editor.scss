/* 享活动-音视频详情 */
body {
  background-color: rgb(242, 244, 247);
}

#pageTopWrap,
#editor,
#index{
  min-width: 1400px;
}

#mh-header{
  position: relative;
  z-index: 99;
}

/*标题的文字加粗*/
.title-t > b {
  font-weight: bold;
}

/*按钮的样式*/
.btn-fff-s {
  width: 44px;
  height: 24px;
  text-align: center;
  border-radius: 2px;
  color: #3D82F2;
  font-size: 14px;
  border: 1px solid;
  cursor: pointer;
  background-color: #fff;
}

.btn-blue-s {
  width: 44px;
  height: 24px;
  text-align: center;
  background: #3d82f2;
  border-radius: 2px;
  color: #fff;
  font-size: 14px;
  cursor: pointer;
}

.btn-blue-s:disabled {
  background-color: #ccc;
  color: #fff;
  cursor: default;
}

.sortable-ghost {
  opacity: 0;
}

#items-1 {
  width: 1400px;
  margin: 0 auto;
  min-height: 600px;
  padding: 20px 0;
  word-break: break-all;
  &.list-dash-border {
    border: 2px dotted #ccc;
  }
}
.list-group {
  margin: 20px;
  .list-group-item {
    position: relative;
    background-color: white;
    //border-radius: 8px;
    margin: 0 auto;
    margin-bottom: 20px;
    min-height: 80px;
    .top-btns {
      position: absolute;
      display: none;
      top: -30px;
      right: 10px;
      z-index: 100;
      padding-bottom: 10px;
      height: 33px;
      line-height: 33px;
      font-size: 12px;
      color: #666;
      -webkit-user-select: none;
      -moz-user-select: none;
      -ms-user-select: none;
      user-select: none;
      .top-btn-groups {
        padding: 0 16px;
        background: #fff;
        -webkit-box-shadow: 0 0 5px 0 rgba(0, 0, 0, 0.3);
        box-shadow: 0 0 5px 0 rgba(0, 0, 0, 0.3);
        border-radius: 4px;
        & > span:not(:last-child) {
          margin-right: 12px
        }
        i {
          margin-right: 4px
        }
        .delete, .set, .handle {
          cursor: pointer
        }
      }
    }
    .padding50{
      padding: 30px 46px;
    }
    &:hover {
      .top-btns {
        display: block;
      }
    }
    .item-engine-title{
      position: relative;
      padding: 0;
      height: 39px;
      line-height: 39px;
      overflow: hidden;
      .title-t{
        float: left;
        font-size: 0;
        span{
          font-size: 17px;
          display: inline-block;
          vertical-align: middle;
        }
      }
      .title-icon{
        height: 26px;
        margin-right: 14px;
      }
      .title-bg{
        display: inline-block;
        background-repeat: no-repeat;
        background-image: url(../../images/form_lists/titlebg.png);
        background-position: top left;
        height: 36px;
        width: 710px;
        margin-top: 2px;
        float: right;
      }
    }
    // 表格的样式
    .table-content{
      padding: 10px;
    }
    &.minH0{
      min-height: 0;
      background-color: transparent;
    }
  }
}

button {
  margin: 40px 20px;
  float: right;
}

.sp-container button {
  margin: 8px 0 0;
}

// 弹窗
.setListDialog {
  .middle {
    min-height: 200px;
    max-height: 600px;
    overflow-y: auto;
    .item-engine-title{
      .title-t{
        font-size: 14px;
        .title-icon{
          width: 5px;
          height: 12px;
          margin-right: 6px;
        }
        span{
          display: inline-block;
          vertical-align: middle;
        }
      }
    }
  }
  //字段选择内容
  .item-set-box {
    .show-lists {
      margin-bottom: 20px;

      label {
        margin: 10px;
        display: inline-block;
        line-height: 20px;
        span,input{
          display: inline-block;
          vertical-align: middle;
        }
      }
    }
    .sort-box {
      .list-title {
        margin: 20px 0;
      }
      ul {
        li {
          line-height: 30px;
          overflow: hidden;
          border-bottom: 1px solid #f5f5f5;
          &:last-child{
            border-bottom: 0;
          }
          .list-name{
            max-width: 80%;
            overflow: hidden;
            -ms-text-overflow: ellipsis;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          .list-icon {
            cursor: pointer;
            margin-top: 8px;
          }
        }
      }
      .selected-list-box{
        .number-box{
          width: 10%;
          float: left;
        }
        .list-box{
          width: 90%;
          float: left;
        }
      }
    }
  }

}

// 数据源选择弹窗
.dataSourceDialog {
  .dialog-content {
    .middle {
      max-height: 300px;
      overflow-y: auto;
      .show-lists {
        padding-top: 12px;
        .radio-box {
          padding-right: 8px;
          margin-bottom: 12px;
          display: inline-block;
          input {
            margin: 0 8px;
          }
        }
      }
    }
  }
}
//文本样式--场地详情
//文本样式
.text-box{
  position: relative;
  overflow: hidden;
  .top-box{
    position: relative;
    .tips{
      display: inline-block;
      vertical-align: top;
      margin-top: 5.5px;
      padding: 3px 9px;
      background: #fff;
      border-radius: 4px;
      margin-left: 16px;
      font-size: 14px;
      line-height: 20px;
      letter-spacing: 0.06em;
      border-width: 1px;
      border-style: solid;
      text-align: center;
    }
    .txt-title{
      font-weight: 500;
      font-size: 28px;
      line-height: 39px;
      letter-spacing: 0.06em;
      color: #333333;
    }
  }
  .bottom-box{
    margin-top: 16px;
    line-height: 25px;
    overflow: hidden;
    .time{
      font-size: 18px;
      color: #666666;
      margin-right: 16px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      width: 50%;
    }
    .type{
      border: 1px solid rgba(153, 153, 153, 0.6);
      box-sizing: border-box;
      border-radius: 4px;
      padding: 3px 9px;
      font-size: 12px;
      line-height: 17px;
      letter-spacing: 0.06em;
      color: #666666;
    }
    .icon-box{
      font-size: 14px;
      color: #666666;
      margin-left: 8px;
      cursor: pointer;
      line-height: 25px;
      i{
        display: inline-block;
        vertical-align: top;
        margin-top: 5px;
        margin-right: 10px;
      }
      span{
        display: inline-block;
        min-width: 30px;
        max-width: 100px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
  .txt-desc{
    margin-top: 20px;
    font-size: 16px;
    line-height: 22px;
    letter-spacing: 0.06em;
    color: #666666;

  }
}
//音视频的多行字段
.av-text-box{
  .text-lists{
    li{
      margin-bottom: 26px;
      line-height: 22px;
    }
  }
}

// 多行文本样式
.introduce-box{
  .introduce-content{
    font-size: 14px;
    line-height: 20px;
    color: #333;
  }
}
// 头部的编辑样式
.page-top {
  .show-name {
    .btn-edit-form{
      font-size: 14px;
      margin-left: 10px;
      &:before {
        color: #3D82F2;
      }
    }
  }
}
// 背景
#formbg {
  position: fixed;
  width: 100%;
  z-index: -1;
  top: 0;
  bottom: 0;
  overflow: hidden;
  &>div{
    width: 100%;
    height: 100%;
  }
}

//iframe
.iframe-box{
  iframe{
    width: 100%;
    min-height: 30px;
  }
}
#mh-footer{
  position:relative;
}
.page-header .top-box .after-login{
  z-index: 9;
}

//音频
.audio-box{
  position: relative;
  .audio-cover{
    width: 100%;
    height: 664px;
    img{
      width: 100%;
      height: 100%;
    }
  }
  .audio-item{
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    z-index: 1;
  }
  &.height54{
    min-height: 54px;
  }
}
//视频
.video-box{
  .video-item{
    width: 100%;
    height: 787.5px;
  }
  .video-cover{
    position: absolute;
    z-index: 1;
    width: 100%;
    height: 787.5px;
    top: 0;
    left: 0;
    .cover{
      width: 100%;
      height: 100%;
    }
    .video-play{
      position: absolute;
      z-index: 2;
      top: 50%;
      left: 50%;
      transform: translate(-50%,-50%);
      cursor: pointer;
      width: 80px;
      height: 80px;
    }
  }
  .video-loading,.video-error{
    position: absolute;
    z-index: 2;
    top: 36%;
    left: 50%;
    transform: translate(-50%,0);
    text-align: center;
    display: none;
    img{
      width: 100px;
      height: 100px;
    }
    p{
      font-size: 20px;
      margin-top: 15px;
      color: rgba(255, 255, 255, 0.6);
    }
  }
}
//富文本样式
.richtext-box{
  .richtext-content{
    font-size: 14px;
    line-height: 20px;
    color: #333;
  }
  .richtext-iframe{
    padding: 0 10px;
    iframe{
      width: 100%;
      min-height: 100px;
    }
  }
}

.gobackPage{
  display: none;
}

.lh-32 {
  line-height: 32px;
}

.form-search-module {
  position: relative;

  .search-inp {
    width: 100%;
    height: 32px;
    font-size: 14px;
    color: #333;
    padding: 0 40px 0 10px;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    border: 1px solid #c1c1c1;
    border-radius: 4px;
    background-color: transparent;
  }

  .icon-search {
    position: absolute;
    top: 50%;
    right: 10px;
    font-size: 14px;
    color: #c1c1c1;
    transform: translateY(-50%);
    -webkit-transform: translateY(-50%);
    -moz-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    -o-transform: translateY(-50%);
    cursor: pointer;
  }
}

/*图片瀑布流*/
.imgs-stream-container{
  .imgs-stream-box{
    padding: 30px 0;
    box-sizing: border-box;
    font-size: 0;
    letter-spacing: 0;
    li{
      display: inline-block;
      vertical-align: top;
      width: calc(25% - 12px);
      margin-bottom: 12px;
      cursor: pointer;
      &:hover{
        border: 4px solid;
        box-sizing: border-box;
      }
      img{
        width: 100%;
      }
    }

  }

  .viewer-canvas{
    //width: 1200px;
    top: 0;
    //left: 50%;
    bottom: 295px;
    //transform: translateX(-50%);
  }

  .viewer-swiper-lists{
    width: 1200px;
    position: fixed;
    bottom: 70px;
    left: 50%;
    transform: translateX(-50%);
    padding: 0 57px;
    z-index: 9999;
    opacity: 0;
    //display: none;
    .device{
      span{
        display: inline-block;
        width: 24px;
        height: 48px;
        line-height: 48px;
        text-align: center;
        border-radius: 2px;
        background: #FFF;
        font-size: 20px;
        color: rgba(0,0,0,0.4);
        cursor: pointer;
      }
    }
    .arrow-left{
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%) rotate(180deg);
    }
    .arrow-right{
      position: absolute;
      right: 0;
      top: 50%;
      transform: translateY(-50%);
    }
    .swiper-slide{
      height: 110px;
      padding: 0 6px;
      box-sizing: border-box;
      .img-box{
        width: 100%;
        height: 100%;
        overflow: hidden;
        img{
          width: 100%;
          height: 100%;
        }
      }
    }
    .index-tip{
      margin-top: 30px;
      font-size: 22px;
      color: #fff;
      text-align: center;
    }
  }
}

@media only screen and (min-width: 1px) and (max-width: 926px) {
  #index{
    min-width: auto;
  }
  #items-1{
    width: 100%;
  }
  .text-box{
    padding: 1.375rem .9375rem;
    margin-bottom: .625rem;
    .top-box{
      .tips{
        margin-top: .09375rem;
        padding: .09375rem .28125rem;
        font-size: .75rem;
        line-height: 1.0625rem;
        margin-left: .75rem;
      }
      .txt-title{
        font-size: 1rem;
        line-height: 1.3125rem;
      }
    }
    .bottom-box{
      margin-top: .75rem;
      .time{
        font-size: .875rem;
        line-height: 1.21875rem;
        letter-spacing: 0.04em;
      }
      .icon-box{
        line-height: 1.0625rem;
        i{
          margin-top: .15625rem;
          margin-right: .3125rem;
          font-size: .9375rem;
        }
        span{
          min-width: auto;
          font-size: .75rem;
        }
      }
    }
    .txt-desc{
      margin-top: 1.25rem;
      font-size: .75rem;
      line-height: 1.0625rem;
      letter-spacing: 0.04em;
    }
  }
  .av-text-box{
    padding: 0.9375rem;
    .text-lists{
      font-size: 0.75rem;
      line-height: 1.0625rem;
      li{
        margin-bottom: 0.8125rem;
      }
    }
  }
  .video-box .video-item{
    width: 100vw;
    height: 56.25vw;
  }

  .video-box{
    .video-cover{
      width: 100vw;
      height: 56.25vw;
      .video-play{
        width: 2.5rem;
        height: 2.5rem;
      }
    }
    .video-loading,.video-error{
      img{
        width: 3.125rem;
        height: 3.125rem;
      }
      p{
        font-size: .625rem;
        margin-top: .46875rem;
      }
    }
  }
  .audio-box .audio-cover{
    height: 13.0625rem;
  }
  .iframe-box{
    padding: 0.9375rem;
  }

  /*图片瀑布流*/
  .imgs-stream-container{
    .imgs-stream-box{
      padding: 30px 0;
      box-sizing: border-box;
      font-size: 0;
      letter-spacing: 0;
      li{
        display: inline-block;
        vertical-align: top;
        width: calc(25% - 12px);
        margin-bottom: 12px;
        cursor: pointer;
        &:hover{
          border: 4px solid;
          box-sizing: border-box;
        }
        img{
          width: 100%;
        }
      }

    }

    .viewer-canvas{
      //width: 1200px;
      top: 0;
      //left: 50%;
      bottom: 295px;
      //transform: translateX(-50%);
    }

    .viewer-swiper-lists{
      width: 1200px;
      position: fixed;
      bottom: 70px;
      left: 50%;
      transform: translateX(-50%);
      padding: 0 57px;
      z-index: 9999;
      opacity: 0;
      //display: none;
      .device{
        span{
          display: inline-block;
          width: 24px;
          height: 48px;
          line-height: 48px;
          text-align: center;
          border-radius: 2px;
          background: #FFF;
          font-size: 20px;
          color: rgba(0,0,0,0.4);
          cursor: pointer;
        }
      }
      .arrow-left{
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%) rotate(180deg);
      }
      .arrow-right{
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
      }
      .swiper-slide{
        height: 110px;
        padding: 0 6px;
        box-sizing: border-box;
        .img-box{
          width: 100%;
          height: 100%;
          overflow: hidden;
          img{
            width: 100%;
            height: 100%;
          }
        }
      }
      .index-tip{
        margin-top: 30px;
        font-size: 22px;
        color: #fff;
        text-align: center;
      }
    }
  }

  //  隐藏头底
  #mh-header,
  #mh-footer{
    display: none;
  }
  .gobackPage{
    display: block;
    position: fixed;
    bottom: 3.125rem;
    right: 0.625rem;
    z-index: 9;
    color: #ffffff;
    font-size: 0.75rem;
    padding: 0 0.5rem;
    line-height: 1.6875rem;
    box-sizing: border-box;
    border-radius: 3.125rem;
  }
}
@media only screen and (min-width: 1px) and (max-width: 1080px) {
  .touchmachine #index{
    min-width: auto;
  }
  .touchmachine #items-1{
    width: 100%;
  }
  .touchmachine .text-box{
    padding: 1.375rem .9375rem;
    margin-bottom: .625rem;
    .top-box{
      .tips{
        margin-top: .09375rem;
        padding: .09375rem .28125rem;
        font-size: .75rem;
        line-height: 1.0625rem;
        margin-left: .75rem;
      }
      .txt-title{
        font-size: 1rem;
        line-height: 1.3125rem;
      }
    }
    .bottom-box{
      margin-top: .75rem;
      .time{
        font-size: .875rem;
        line-height: 1.21875rem;
        letter-spacing: 0.04em;
      }
      .icon-box{
        line-height: 1.0625rem;
        i{
          margin-top: .15625rem;
          margin-right: .3125rem;
          font-size: .9375rem;
        }
        span{
          min-width: auto;
          font-size: .75rem;
        }
      }
    }
    .txt-desc{
      margin-top: 1.25rem;
      font-size: .75rem;
      line-height: 1.0625rem;
      letter-spacing: 0.04em;
    }
  }
  .touchmachine .av-text-box{
    padding: 0.9375rem;
    .text-lists{
      font-size: 0.75rem;
      line-height: 1.0625rem;
      li{
        margin-bottom: 0.8125rem;
      }
    }
  }
  .touchmachine .video-box .video-item{
    height: 56.25vw;
  }
  .touchmachine .video-box{
    .video-loading,.video-error{
      img{
        width: 3.125rem;
        height: 3.125rem;
      }
      p{
        font-size: .625rem;
        margin-top: .46875rem;
      }
    }
  }
  .touchmachine .audio-box .audio-cover{
    height: 13.0625rem;
  }
  .touchmachine .iframe-box{
    padding: 0.9375rem;
  }

  //  隐藏头底
  .touchmachine #mh-header,
  .touchmachine #mh-footer{
    display: none;
  }
  .touchmachine .gobackPage{
    display: block;
    position: fixed;
    bottom: 3.125rem;
    right: 0.625rem;
    z-index: 9;
    color: #ffffff;
    font-size: 0.75rem;
    padding: 0 0.5rem;
    line-height: 1.6875rem;
    box-sizing: border-box;
    border-radius: 3.125rem;
  }
  .imgs-stream-container{
    .viewer-swiper-lists{
      display: none;
      .index-tip{
        display: none;
      }
    }
    .imgs-stream-box{
      padding: 1.5rem 0;
      li{
        width: 100%;
      }
    }
    .viewer-canvas{
      width: 100%;
      transform: unset;
      left: unset;
      bottom: 0;
    }
  }
}
