body {
  background: #F5F5F5;
}

.w-200 {
  width: 200px !important;
}

.w-1200 {
  width: 1200px;
  margin: 0 auto;
}

.header {
  background: #3D82F2;
  .logo-txt {
    height: 60px;
    line-height: 60px;
    font-size: 24px;
    color: #FFFFFF;
  }
}

/*面包屑样式*/
.breadcrumb-box {
  line-height: 54px;
}

.breadcrumb-box .item {
  font-size: 16px;
  color: rgba(0, 0, 0, 0.4);
  display: inline-block;
}

.breadcrumb-box .item.current {
  color: #3D82F2;
}

.breadcrumb-box .item.prev {
  color: #F7B528;
}

.breadcrumb-box .item:last-child .icon-up {
  display: none;
}

.breadcrumb-box .icon-up {
  font-size: 12px;
  display: inline-block;
  color: rgba(0, 0, 0, 0.4);
  margin: 0 5px;
  -webkit-transform: rotate(90deg);
  -moz-transform: rotate(90deg);
  -ms-transform: rotate(90deg);
  -o-transform: rotate(90deg);
  transform: rotate(90deg);
}

.page-content {
  min-width: 1200px;
  background: #ffffff;
  min-height: 700px;

  .temp-list {
    display: none;
    padding: 24px;
  }

  .tabs {
    border-bottom: 1px solid #DDDDDD;
    padding-left: 24px;

    span {
      display: inline-block;
      padding-top: 16px;
      padding-bottom: 17px;
      font-size: 16px;
      color: #666666;
      margin-right: 16px;
      cursor: pointer;
    }

    .current {
      padding-bottom: 15px;
      color: #3D82F2;
      border-bottom: 2px solid #3D82F2;
    }
  }

  .btn-href {
    display: inline-block;
    border: 1px solid #DDDDDD;
    width: 72px;
    height: 30px;
    line-height: 30px;
    background: #F5F5F5;
    font-size: 14px;
    color: #333333;
    margin-right: 12px;
    text-align: center;
    margin-bottom: 24px;
    outline: none;
  }

  .form-box {
    text-align: left;

    .item {
      margin-bottom: 16px;
      line-height: 30px;
      position: relative;

      &:last-child {
        margin-bottom: 0;
      }

      .item-label {
        display: inline-block;
        width: 172px;
        text-align: left;
        font-size: 14px;
        color: #333333;
        vertical-align: top;
        &.flex-style {
          display: flex;
        }
      }

      .ipt-box {
        width: 343px;
        height: 30px;
        vertical-align: top;
        border: 1px solid #DDDDDD;
        -webkit-border-radius: 2px;
        -moz-border-radius: 2px;
        border-radius: 2px;
        text-indent: 8px;
      }
      .marginLR6 {margin: 0 6px;
      width: 300px;
      }
      .marginL20 {
        margin-left: 20px;
      }
      .col8A9099 {
        color: #8A9099;
      }
      .col474F59 {
        color: #474F59;
      }

      .ui-select-mini {
        width: 320px;
      }

      .select2-container--default .select2-selection--single {
        border-color: #dddddd;
      }
      .tip-div {
        position: relative;
        .btn-tip {
          display: inline-block;
          width: 14px;
          height: 30px;
          background: url("../../images/icon-14-info.png") center center no-repeat;
          background-size: 100%;
          cursor: pointer;
        }
        .tip-txt {
          position: absolute;
          top: -77px;
          left: -15px;
          width: 240px;
          height: 79px;
          background: rgba(24, 30, 51, 0.8);
          border-radius: 4px;
          color: #FFFFFF;
          font-size: 12px;
          box-sizing:border-box;
          padding: 10px 16px;
          display: none;
          img {
            width: 94px;
            height: 19px;
          }
          .triangle {
            position: absolute;
            bottom: -10px;
            display: block;
            width: 0;
            height: 0;
            border: 6px solid;
            border-color: rgba(24, 30, 51, 0.8) transparent transparent transparent;
          }
        }
        &:hover {
          .btn-tip {
            background: url("../../images/icon-14-info-active.png") center center  no-repeat;
            background-size: 100%;
          }
          .tip-txt {
            display: block;
          }
        }
      }
      .login-style-list {
        padding: 24px 20px;
        background: #F7F8FA;
        border-radius: 4px;
        //width: 771px;
        margin-top: 16px;
        ul {
          //width: 771px;
          white-space: nowrap;
          overflow-x: auto;
          overflow-y: hidden;
          //&::-webkit-scrollbar{
          //  display: none;
          //}
          li {
            position: relative;
            width: 180px;
            margin-right: 20px;
            cursor: pointer;
            text-align: center;
            display: inline-block;
            .img-box {
              height: 180px;
              padding: 19px 0;
              background: #F2F4F7;
              border-radius: 4px;
              box-sizing:border-box;
              border: 1px solid #E1E5EB;
            }
            .check-icon {
              position: absolute;
              top: 5px;
              right: 5px;
              display: inline-block;
              width: 20px;
              height: 20px;
              background: url("../../images/icon-20-gou.png") no-repeat;
              background-size: 100%;
              display: none;
            }
            p{
              margin-top: 20px;
              color: #8A9099;
              font-size: 14px;
            }
            &.active {
              .img-box {
                border: 2px solid #3A8BFF;
              }
              .check-icon {
                display: block;
              }
            }
          }
        }
        .mCSB_scrollTools_horizontal {
          bottom: -20px;
        }
        .mCSB_dragger_bar {
          height: 8px !important;
          background: #DADFE6;
        }

      }
      .no-margin {
        display: flex;
        margin-left: 20px;
        position: relative;
        top: 6px;
        .layui-form-radio {
          margin: 0;
          padding: 0;
        }
        .layui-input {
          height: 30px;
          line-height: 30px;
          margin-right: 6px;
        }
      }
      .layui-div {
        display: flex;
        .layui-input {
          width: 60px;
        }

      }
      .layui-table-box {
        .delete-style {
          color: #3A8BFF;
          font-size: 13px;
        }
        .layui-table thead tr {
          background-color: #F5F6F7;
        }
        .layui-table th {
          border-color: #EBEDF0;
        }
        .layui-form-checked[lay-skin=primary] i {
          border-color: #3A8BFF!important;
          background-color: #3A8BFF;
        }
        .layui-form-checkbox[lay-skin=primary]:hover i {
          border-color: #3A8BFF!important;
        }
      }
      .btn-div {
        .btn-style {
          width: 60px;
          height: 28px;
          line-height: 28px;
          border-radius: 4px;
          border: 1px solid #91B6F2;
          font-size: 13px;
          font-weight: 400;
          color: #3E82F2;
          background: #ffffff;
          margin-right: 20px;
          cursor: pointer;
        }
        .line {
          display: inline-block;
          position: relative;
          top: 4px;
          margin-right: 20px;
          width: 1px;
          height: 20px;
          background: #F2F2F2;
          border-radius: 1px;
        }
      }
      .upload-div {
        .upload-style {
          position: relative;
          width: 80px;
          height: 80px;
          background: #F2F4F7;
          border-radius: 4px;
          border: 1px dashed #DADFE6;
          text-align: center;
          .file-button {
            position: absolute;
            top: 0;
            left: 0;
            width: 80px;
            height: 80px;
            opacity: 0;
            cursor: pointer;
          }
          .icon {
            display: inline-block;
            width: 25px;
            height: 24px;
            background: url("../../images/icon-24-upload.png") no-repeat;
            background-size: 100%;
            margin-top: 17px;
          }
          p{
            font-size: 12px;
            font-weight: 400;
            color: #3A8BFF;
            line-height: 20px;
          }
          .img-box {
            width: 80px;
            height: 80px;
          }
        }
        .file-btn-div {
          span {
            display: inline-block;
            font-size: 14px;
            color: #3A8BFF;
            line-height: 20px;
            margin-top: 59px;
            margin-left: 14px;
            cursor: pointer;
          }
          .again {
            font-size: 14px;
            color: #3A8BFF;
            line-height: 20px;
            margin-top: 59px;
            margin-left: 14px;
            position: relative;
            cursor: pointer;
            .file-button {
              position: absolute;
              width: 56px;
              height: 20px;
              opacity: 0;
              left: 0;
              top: 0;
              cursor: pointer;
            }
          }
        }
        .file-tips {
          font-size: 12px;
          color: #8A9099;
          line-height: 20px;
          margin-left: 14px;
          margin-top: 59px;
        }
        .txt {
          font-size: 12px;
          color: #8A9099;
          line-height: 20px;
          margin: 20px 0;
        }
        .result-show {
          display: flex;
          width: 180px;
          height: 20px;
          background: #F5F7FA;
          border-radius: 2px;
          border: 1px solid #EDEDED;
          padding: 5px 34px 5px 40px;
          position: relative;
          img {
            width: 20px;
            height: 20px;
            position: absolute;
            left: 10px;
            top: 5px;
          }
          span {
            font-size: 12px;
            color: #131B26;
            line-height: 20px;
            display: block;
          }
          .delete-icon {
            display: inline-block;
            width: 12px;
            height: 12px;
            background: url("../../images/icon-12-delete.png") no-repeat;
            background-size: 100%;
            position: absolute;
            right: 10px;
            top: 9px;
          }
        }
      }
      .inline-style {
        display: inline-block;
        width: 200px;
        height: 30px;
        line-height: 30px;
        margin-top: 6px;
      }
    }
    .url-open {

      .url-bg {
        min-width: 460px;
        background: #F5F7FA;
        padding: 20px;
        .tips {
          font-size: 12px;
          color: #8A9099;
          margin-top: 10px;
          margin-left: 77px;
          &:last-child {
            color: #FF7D00;
          }
          a{
            color: #3A8BFF;
          }
        }
      }
    }

    .link-list a {
      display: block;
      line-height: 20px;
      margin-bottom: 9px;
    }

    .col-yellow {
      color: #F1A135;
      margin-left: 16px;
    }

    .item-login-info {
      display: inline-block;

    }

    .login-way-info {
      display: none;
    }

    .login-default {
      display: block;
      margin-top: 16px;

      span {
        font-size: 14px;
        color: #333333;

        .col-red {
          color: #FC5B5B;
          vertical-align: top;
        }
      }
    }

    .item-li {
      margin-top: 16px;
      position: relative;

      span {
        font-size: 14px;
        color: #333333;

        .col-red {
          color: #FC5B5B;
          vertical-align: top;
        }
      }

      .mini-ipt {
        width: 320px;
        height: 28px;
        border: 1px solid #DDDDDD;
        -webkit-box-sizing: border-box;
        -moz-box-sizing: border-box;
        box-sizing: border-box;
        text-indent: 8px;
        font-size: 14px;
        border-radius: 2px;
        -webkit-border-radius: 2px;
        -moz-border-radius: 2px;
      }
    }

    .item-reg-address {
      display: none;
      margin-top: 5px;
    }

    .item-checkbox {
      padding-left: 0;
      font-size: 14px;
      color: #333333;
      margin-top: 10px;

      .ipt-checkbox {
        margin-right: 3px;
        cursor: pointer;
      }
    }

    .btn-save {
      width: 120px;
      height: 30px;
      -webkit-border-radius: 4px;
      -moz-border-radius: 4px;
      border-radius: 4px;
      background: #3D82F2;
      font-size: 14px;
      color: #fff;
      border: none;
      margin: 20px 0 0 172px;
      outline: none;
      cursor: pointer;
    }

    .ui-select-mini .select2-container .select2-selection--single .select2-selection__rendered {
      padding-left: 8px;
    }
  }

}

.manager-list {
  padding-top: 0 !important;

  .btn-add {
    height: 30px;
    padding: 0 8px;
    line-height: 30px;
    border: 1px solid #3D82F2;
    -webkit-border-radius: 2px;
    -moz-border-radius: 2px;
    border-radius: 2px;
    font-size: 14px;
    color: #fff;
    background-color: #3D82F2;
    margin: 16px 0;
    outline: none;
    cursor: pointer;

    &:hover {
      opacity: 0.8;
    }
  }

  .ui-table {
    border-color: rgba(0, 0, 0, 0.05);
    font-size: 14px;
    color: #333333;
    text-align: left;

    th {
      background: #F6F5F8;
      height: 40px;
      padding: 0 20px;
      box-sizing: border-box;
    }

    td {
      height: 50px;
      padding: 0 20px;
      word-break: break-all;
      box-sizing: border-box;

      .icon-edit {
        font-size: 16px;
        color: #CCC9CD;
        cursor: pointer;
      }

      .icon-rubbish {
        font-size: 16px;
        margin-left: 40px;
        cursor: pointer;

        &:before {
          color: #CCC9CD;
        }
      }
    }

    .rule-list {
      padding: 5px 20px 0;
      .title {
        display: inline-block;
        width: 30%;
        height: 100%;
        vertical-align: top;
        line-height: 24px;
      }
      .module{
        display: inline-block;
        width: 69%;
      }
      span {
        display: inline-block;
        height: 24px;
        line-height: 24px;
        padding: 0 8px;
        background: #F3F3F5;
        margin-right: 8px;
        -webkit-border-radius: 2px;
        -moz-border-radius: 2px;
        border-radius: 2px;
        margin-bottom: 5px;

        &:hover {
          background: #3D82F2;
          color: #fff;
        }
      }
    }
  }
}

/*弹框：添加管理员*/
.add-manager-pop {
  .pop-content {
    width: 1050px;
  }

  .pop-info {
    padding: 0 24px 24px;
  }

  .search-box {
    width: 196px;
    height: 30px;
    border: 1px solid #DDDDDD;
    margin: 14px 0;
    -webkit-border-radius: 2px;
    -moz-border-radius: 2px;
    border-radius: 2px;
    overflow: hidden;

    .ipt-search {
      width: 164px;
      height: 100%;
      border: none;
      vertical-align: top;
      text-indent: 8px;

    }

    .icon-search {
      font-size: 16px;
      color: rgba(0, 0, 0, 0.4);
      float: right;
      margin: 6px 8px 0 0;
    }
  }

  .table-box {
    height: 300px;
  }

  .ui-table {
    border-color: rgba(0, 0, 0, 0.05);
    font-size: 14px;
    color: #333333;
    text-align: left;

    th {
      background: #F6F5F8;
    }

    th,
    td {
      height: 40px;
      padding: 0 20px;
      box-sizing: border-box;
    }

    input[type='radio'] {
      font-size: 14px;
      color: red;
      cursor: pointer;
    }
  }

  .bottom-btns {
    border-top: 1px solid #EEEEEE;
    padding-bottom: 16px;

    .btn {
      width: 78px;
      height: 30px;
      -webkit-border-radius: 4px;
      -moz-border-radius: 4px;
      border-radius: 4px;
    }
  }
}

/*确认权限弹框*/
.sure-limits-pop {
  .pop-content {
    width: 640px;
  }

  .middle {
    padding: 24px;
    max-height: 400px;
    overflow-y: auto;

    .item {
      position: relative;
      text-align: left;
      font-size: 14px;
      color: #333333;
      padding-left: 73px;
      margin-bottom: 14px;

      .label {
        display: inline-block;
        width: 73px;
        position: absolute;
        left: 0;
        top: 0;
      }

      input[type="checkbox"] {
        font-size: 14px;
        margin-right: 3px;
        vertical-align: inherit;
        cursor: pointer;
      }
    }
  }

  .limit-list {
    li {
      display: inline-block;
      margin: 0 16px 10px 0;

      &.all {
        // display: block;
      }
    }
  }

  .bottom-btns {
    border-top: 1px solid #EEEEEE;
    padding-bottom: 16px;

    .btn {
      width: 78px;
      height: 30px;
      -webkit-border-radius: 4px;
      -moz-border-radius: 4px;
      border-radius: 4px;
    }
  }
}

/*删除弹框*/
.delete-temp-pop {
  .pop-content {
    width: 348px;

    .middle {
      text-align: center;
      padding: 20px 0;
    }

    .icon-delete {
      width: 90px;
      margin-bottom: 16px;
    }

    .tip {
      font-size: 14px;
      color: #333333;
    }

    .bottom-btns {
      border-top: 1px solid #EEEEEE;
      padding-bottom: 16px;

      .btn {
        width: 64px;
        height: 30px;
        -webkit-border-radius: 4px;
        -moz-border-radius: 4px;
        border-radius: 4px;
      }
    }
  }
}


/*弹框：添加管理员2*/
.add-manager-pop-2 .pop-content {
  width: 465px;
}

.add-manager-pop-2 .middle {
  padding: 20px;
}

.add-manager-pop-3 .pop-content {
  width: 1020px;
}

.input-item {
  width: 100%;
  height: 48px;
  line-height: 48px;
}

.input-item label {
  float: left;
  width: 85px;
}

.input-item input[type="text"] {
  width: 300px;
  height: 30px;
  border: solid 1px #ddd;
  border-radius: 2px;
  padding: 0 10px;
}

.input-item.model-1 input[type="text"] {
  width: 400px;
}

.input-item.model-2 input[type="text"] {
  width: 172px;
}

.input-item.model-1,
.input-item.model-2 {
  display: none;
}

/* 白名单 操作日志 */
.white-list .page-content,
.operation-log .page-content,
.review-content .page-content {
  width: 100%;
}

.white-list .page-content .temp-list,
.operation-log .page-content .temp-list {
  display: block;
}

.white-list .manager-list .ui-table tr th:nth-of-type(1),
.white-list .manager-list .ui-table tr th:nth-of-type(3),
.white-list .manager-list .ui-table tr td:nth-of-type(1),
.white-list .manager-list .ui-table tr td:nth-of-type(3) {
  text-align: center;
}
.white-list{
  .forbid-tips{
    .layui-form-item{
      margin-bottom: 0;
      padding-top: 16px;
      width: calc(100% - 80px);
    }
    .layui-form-label{
      width: 225px;
      text-align: left;
      padding: 9px 0;
    }
    .layui-input-inline{
      padding: 5px 0;
      width: auto;
      .layui-form-radio{
        margin-top: 0;
        margin-right: 0;
      }
      .tip-div{
        position: relative;
        display: inline-block;
        margin: 6px 10px 0 -10px;

        .btn-tip{
          cursor: pointer;
        }
        .tip-txt{
          position: absolute;
          top: 26px;
          left: -100px;
          min-width: 200px;
          line-height: 20px;
          padding: 4px 0;
          background-color: #666666;
          font-size: 12px;
          color: #ffffff;
          border-radius: 4px;
          text-align: center;
          display: none;
        }
        &:hover{
          .tip-txt{
            display: block;
          }
        }
      }
    }
    .tip-input{
      width: calc(100% - 400px);
      max-width: 800px;
      input{
        width: 100%;
        height: 30px;
        background: #ffffff;
        border: 1px solid rgba(0,0,0,0.14);
        border-radius: 2px;
        padding: 5px 12px;
        line-height: 20px;
        box-sizing: border-box;
      }
    }
    .btn-add{
      margin-top: 20px;
    }
  }
}

button.btn-remove {
  height: 30px;
  padding: 0 12px;
  line-height: 30px;
  border: 1px solid #ddd;
  border-radius: 2px;
  font-size: 14px;
  color: #333;
  background: #fff;
  margin: 16px auto;
  outline: none;
  cursor: pointer;
}

button.btn-remove:hover {
  border: 1px solid #3d82f2;
  color: #3d82f2
}

.operation-log .layui-form-item {
  clear: none;
  padding: 16px 0;
  margin-bottom: 0;
}

.operation-log .layui-form-select dl dd.layui-this {
  background-color: #3d82f2;
}

.operation-log .layui-form-label {
  padding: 5px 15px;
}

.operation-log .layui-form-select dl {
  top: 30px;
}

.layui-laydate-content td.laydate-selected {
  background-color: rgba(61, 130, 242, .1) !important;
}

.operation-log .layui-input {
  height: 30px;
}

.operation-log .manager-list .btn-add {
  margin: 0;
}

/* 文章审核 */
.custom-search {
  position: relative;
  width: 196px;
  margin: 10px 0;
}

.custom-search .layui-inline {
  width: 100%;
}

.custom-search .layui-input,
.custom-search .layui-btn {
  height: 30px;
  line-height: 30px;
}

.custom-search .layui-btn {
  position: absolute;
  right: 0;
  top: 0;
  z-index: 2;
  background-color: transparent;
  color: rgb(153, 153, 153);
  padding: 0 5px;
}

.custom-search .layui-btn:hover {
  color: rgb(153, 153, 153);
}

.nearlist {
  width: 100%;
  height: 300px;
  overflow-y: scroll;
  border-top: solid 1px rgba(0, 0, 0, .1);
  border-bottom: solid 1px rgba(0, 0, 0, .1);
}

.nearlist .layui-form-checked[lay-skin=primary] i,
.review-content .layui-form-checked[lay-skin=primary] i,
.add-auditor-pop .layui-form-checked[lay-skin=primary] i  {
  border-color: #3d82f2 !important;
  background-color: #3d82f2;
}

.nearlist .layui-form-checkbox[lay-skin=primary]:hover i,
.review-content .layui-form-checkbox[lay-skin=primary]:hover i,
.add-auditor-pop .layui-form-checkbox[lay-skin=primary]:hover i  {
  border-color: #3d82f2;
}

.review-content .page-content .temp-list {
  display: block;
}

.review-content .clear .layui-btn {
  margin: 10px 10px 10px 0;
}

.review-content .layui-btn-sm {
  height: 24px;
  line-height: 24px;
  padding: 0 8px;
  font-size: 14px;
}

.review-content .layui-btn-primary {
  border: 1px solid #C9C9C9;
  background-color: #fff;
  color: #555;
}

.review-content .js-review-button {
  height: 44px;
}

.review-content .js-review-button button {
  display: none;
}

.review-iframe {
  width: 100%;
  height: 600px;
  border: 0;
}

/* 添加审核人员弹窗 */
.add-auditor-pop{
  .pop-content{
    width: 650px;
    .middle{
      padding: 0 20px;
      height: 400px;
      overflow-y: auto;
      -webkit-box-sizing: border-box;
      -moz-box-sizing: border-box;
      box-sizing: border-box;
      .ui-table{
        th{
          background: #F6F5F8;
          height: 40px;
          padding: 0 0 0 20px;
          -webkit-box-sizing: border-box;
          -moz-box-sizing: border-box;
          box-sizing: border-box;
          text-align: left;
        }
        td{
          height: 40px;
          padding: 0 20px;
          -webkit-box-sizing: border-box;
          -moz-box-sizing: border-box;
          box-sizing: border-box;
        }
      }
    }
  }
}

.btn-bg-blue{
  border: 1px solid #3D82F2;
  padding: 0 8px;
  -webkit-border-radius: 2px;
  -moz-border-radius: 2px;
  border-radius: 2px;
  font-size: 14px;
  color: #fff;
  background-color: #3D82F2;
  outline: none;
}
/* 模块审核设置 */
.module-check-list{
  padding-top: 0 !important;

  .btn-bg-blue {
    height: 30px;
    line-height: 30px;
    margin: 10px 16px 10px 0;
    cursor: pointer;
    &:hover {
      opacity: 0.8;
    }
  }
  .ui-table {
    border-color: rgba(0, 0, 0, 0.05);
    font-size: 14px;
    color: #333333;
    text-align: left;

    th {
      background: #F6F5F8;
      height: 40px;
      padding: 0 20px;
      box-sizing: border-box;
    }

    td {
      height: 50px;
      padding: 0 20px;
      box-sizing: border-box;
      .btn-link{
        text-decoration: underline;
      }
    }
    .table-step{
      li{
        display: inline-block;
        color: #3D82F2;
        &:last-child{
          .step-arrow{
            display: none;
          }
        }
      }
    }
  }
  .layui-form-onswitch{
    border-color: #3D82F2;
    background-color: #3D82F2;
  }
}
.layui-form-onswitch{
  border-color: #3D82F2;
  background-color: #3D82F2;
}
/* 审核流程设置 */
.check-step-page{
  .page-content{
    padding-bottom: 30px;
    position: relative;
    overflow: hidden;
  }
  .top-btns{
    overflow: hidden;
    border-bottom: 1px solid #ddd;
    button{
      box-sizing: border-box;
      line-height: 40px;
      height: 40px;
      margin: 10px;
      padding: 0 10px;
      cursor: pointer;
    }
    .btn-border-blue{
      border: 1px solid #3D82F2;
      font-size: 16px;
      color: #3D82F2;
      background-color: white;
    }
    .btn-bg-blue{
      border: 1px solid #3D82F2;
    }
  }
  .check-step-content{
    padding: 30px 0 0 50px;
    box-sizing: border-box;
    color: #3D82F2;
    width: 400px;
    text-align: center;
    .step-border-blue{
      border: 1px solid #3D82F2;
      padding: 10px;
      display: inline-block;
      width: 120px;
      text-align: center;
      font-size: 18px;
      box-sizing: border-box;
    }
    .next-add{
      width: 120px;
      text-align: center;
      box-sizing: border-box;
      margin: 0 auto;
      .next-line{
        position: relative;
        width: 2px;
        height: 40px;
        background-color: #3D82F2;
        margin: 0 auto;
      }
      .line-down{
        height: 40px;
        i{
          position: absolute;
          bottom: 5px;
          left: -13px;
          transform: rotate(-90deg);
        }
      }
      .step-add{
        color: #ffffff;
        background-color: #3D82F2;
        border-radius: 50%;
        width: 26px;
        height: 26px;
        line-height: 26px;
        font-size: 14px;
        margin: 0 auto;
        cursor: pointer;
      }
    }
    .new-step-lists{
      .list-content{
        position: relative;
        display: inline-block;
        .layui-icon-close-fill{
          position: absolute;
          top: -12px;
          right: -12px;
          color: #3D82F2;
          font-size: 24px;
          opacity: 0;
          cursor: pointer;
        }
        &:hover{
          .layui-icon-close-fill{
            opacity: 1;
          }
        }
        .list-border{
          border: 1px solid #ddd;
          padding: 20px 10px;
          box-sizing: border-box;
          .list-avatar{
            width: 40px;
            height: 40px;
            line-height: 40px;
            border-radius: 50%;
            background-color: #3D82F2;
            text-align: center;
            color: #ffffff;
            display: inline-block;
            vertical-align: middle;
            margin: 0 10px;
            .layui-icon-friends{
              font-size: 30px;

            }
          }
          .list-right{
            display: inline-block;
            vertical-align: middle;
            margin: 0 10px;
            .list-name{
              color: #ffffff;
              background-color: #666;
              padding: 0 10px;
              margin-top: 10px;
              border-radius: 4px;
            }
          }
        }
      }
    }
  }
}
.fs0{
  font-size: 0;
}
/* 审核人员弹窗 */
#auditorPop{
  &.pop{
    position: absolute;
    background: rgba(0, 0, 0, 0);
    left: auto;
    .pop-content{
      right: 0;
      left: auto;
      top: 100px;
      bottom: auto;
      transform: unset;
      width: 260px;
      height: 600px;

      .middle{
        position: absolute;
        top: 55px;
        bottom: 63px;
        overflow-y: auto;
        width: 100%;
      }
      .select-lists{
        padding-left: 30px;
        .layui-anim.layui-icon{
          font-size: 16px;
        }
      }
      .bottom-btns{
        position: absolute;
        width: 100%;
        bottom: 0;
      }
    }
  }
}
.animated {
  animation-duration: 1s;
  animation-fill-mode: both;
}

@keyframes slideInRight {
  from {
    transform: translate3d(100%, 0, 0);
    visibility: visible;
  }

  to {
    transform: translate3d(0, 0, 0);
  }
}
.slideInRight {
  animation-name: slideInRight;
}

@keyframes slideOutRight {
  from {
    transform: translate3d(0, 0, 0);
  }

  to {
    visibility: hidden;
    transform: translate3d(100%, 0, 0);
  }
}

.slideOutRight {
  animation-name: slideOutRight;
}
/* 模块数据审核 */
.tab-check-page{
  .temp-list{
    width: 100%;
    box-sizing: border-box;
    padding: 24px 0;
    .content-left{
      width: 25%;
      .custom-search{
        width: auto;
        margin: 0 20px;
      }
      .select-lists{
        .check-all{
          margin: 20px;
        }
        .lists{
          .list{
            cursor: pointer;
            display: block;
            border-bottom: 1px solid #eee;
            padding: 10px 20px;
            &.active{
              border-color: #3D82F2;
            }
          }
        }
      }
    }
    .js-review-button {
      margin: 10px 20px;
      .layui-btn-primary:hover{
        border-color: #3D82F2;
      }
    }
    .content-center{
      width: 50%;
      padding-left: 20px;
      box-sizing: border-box;
      .info{
        padding: 4px;
        font-weight: 500;
        margin-bottom: 12px;
        span{
          margin-right: 10px;
        }
      }
      .iframe-content{
        position: relative;
        background-color: #aaa;
        iframe{
          margin: 0;
          width: 100%;
          min-height: 600px;
        }
      }
      .check-table{
         font-size: 14px;
         color: #333;
         line-height: 20px;
         box-sizing: border-box;
         margin-bottom: 20px;
         .table-list{
           overflow: hidden;
           width: 100%;
           box-sizing: border-box;
           border-bottom: 1px solid #eee;
           &.table-head{
             background-color: #F5F5F5;
             li{
               padding: 16px 13px;
             }
           }
           li{
             border-left: 1px solid #eee;
             padding: 10px 13px;
             float: left;
             box-sizing: border-box;
             overflow: hidden;
             text-overflow: ellipsis;
             white-space: nowrap;
             &:last-child{
               border-right: 1px solid #eee;
             }
             &.btn-link{
               color: #3D82F2;
               cursor: pointer;
             }
           }
           .status-icon{
             display: inline-block;
             width: 6px;
             height: 6px;
             margin-right: 6px;
             border-radius: 50%;
           }
           .status-pass{
             .status-icon{
               background-color: #21C32B;
             }
           }
           .status-pass-no{
             .status-icon{
               background-color: #E92222;
             }
           }
         }
       }
      .table-list3{
        .table-list{
          li{
            &.name{
              width: 24%;
            }
            &.link{
              width: 57%;
            }
            &.operate{
              width: 19%;
            }
            &.status{
              width: 19%;
            }
          }
        }
      }
      .check-input-content{
        .input-content{
          position: relative;
          border: 1px solid #ddd;
          .input-area{
            width: 100%;
            height: 200px;
            padding: 16px 16px 30px 16px;
            box-sizing: border-box;
          }
          .count-num{
            position: absolute;
            bottom: 0;
            right: 0;
            padding: 6px 20px;
          }
        }
        .btns{
          padding-top: 20px;
          button{
            color: white;
            &.btn-pass{
              background-color: rgb(28,200,133);
            }
            &.btn-pass-no{
              background-color: rgb(194,0,54);
            }
          }
        }
      }
      .table-list4{
        .table-list{
          li{
            &.name{
              width: 24%;
            }
            &.link{
              width: 42%;
            }
            &.status{
              width: 19%;
            }
            &.operate{
              width: 15%;
            }
          }
        }
      }
      .check-opinion{
        line-height: 20px;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 5;
        -webkit-box-orient: vertical;
      }
    }
    .content-right{
      width: 25%;
      .step-lists{
        padding: 0 20px;
        .step-list{
          .list-top{
            overflow: hidden;
          }
          .list-avatar{
            width: 60px;
            height: 60px;
            border-radius: 50%;
            border: 1px solid #eee;
            margin-right: 10px;
            img{
              width: 100%;
              height: 100%;
              border-radius: 50%;
            }
          }
          .list-info{
            .layui-btn{
              background-color: #3D82F2;
              height: 24px;
              line-height: 24px;
              padding: 0 10px;
            }
            .list-name{
              width: 90px;
              line-height: 24px;
            }
            .list-time{
              margin-top: 16px;
            }
          }
          .list-line{
            min-height: 50px;
            margin-left: 30px;
            border-left: 2px solid #3D82F2;
            padding-left: 40px;
            padding-top: 4px;
            box-sizing: border-box;
          }
          &:last-child{
            .list-line{
              border-color: #ffffff;
            }
          }
          &.list-disable{
            .list-avatar{
              img{
                opacity: 0.5;
              }
            }
            .list-name{
              color: #ccc;
            }
            .list-status{
              display: none;
            }
            .list-time{
              display: none;
            }
            .list-line{
              border-color: #eee;
            }
          }
        }
      }

    }
    .check-input-content{
      padding: 20px 20px 0 20px;
      .input-content{
        position: relative;
        border: 1px solid #ddd;
        .input-area{
          width: 100%;
          height: 300px;
          padding: 10px 10px 30px 10px;
          box-sizing: border-box;
        }
        .count-num{
          position: absolute;
          bottom: 0;
          right: 0;
          padding: 6px 20px;
        }
      }
      .btns{
        padding-top: 20px;
        button{
          color: white;
          &.btn-pass{
            background-color: rgb(28,200,133);
          }
          &.btn-pass-no{
            background-color: rgb(194,0,54);
            float: right;
          }
        }
      }
    }
    &.my-checked{
      .content-left{
        .select-lists{
          text-align: center;
          margin-top: 20px;
          .list.active{
            color: #3D82F2;
          }
        }
      }
    }
    .checked-status{
      position: absolute;
      bottom: 20px;
      right: 20px;
      img{
        width: 150px;
      }
      &.passed{
        .status-pass-no{
          display: none;
        }
      }
      &.passed-no{
        .status-pass{
          display: none;
        }
      }
    }
  }
  .layui-form-checkbox[lay-skin=primary]{
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    span{
      display: block;
    }
  }
  .layui-form-checked[lay-skin=primary]{
    span{
      color: #3d82f2;
    }
    i{
      border-color: #3d82f2 !important;
      background-color: #3d82f2;
    }
  }
  .layui-form-checkbox[lay-skin=primary]:hover{
    span{
      color: #3d82f2;
    }
    i{
      border-color: #3d82f2;
    }
  }
}
.flex-style {
  display: flex;
  flex-wrap: wrap;
}
.my-layer .custom-link {
  display: block;
  width: 100%;
  height: 42px;
  line-height: 50px;
  text-align: left;
}
.my-layer .layui-upload-drag .layui-icon {
  color: #0099ff;
}
.my-layer.layui-layer {
  border-radius: 6px;
}
.my-layer{
  .layui-layer-title {
    text-align: left;
    border-radius: 6px;
    background: #fff;
    padding: 0 80px 0 32px;
  }
  .custom-layer {
    text-align: center;
    padding: 16px 24px 10px 24px;
    overflow: hidden;
    .layui-upload-drag {
      width: calc(100% - 60px);
      height: 90px;
      border-radius: 4px;
    }
    a{
      color: #0099ff;
    }
  }
  .layui-layer-btn{
    border-top: solid 1px rgba(238, 238, 238, 1);
    text-align: center;
  }
}
