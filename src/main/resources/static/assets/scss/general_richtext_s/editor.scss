$colorMain: #3D82F2;
body {
    background-color: #F5F5F5;
    //min-width: 1200px;
}

#index {
    position: relative;
    #header{
        position: relative!important;
        top:0 !important;
    }
}

/*sid-groups部分的样式*/
#header,
#banner,
#footer {
    position: relative;
    z-index: 5;
    //&:hover {
    //    z-index: 99;
    //    .module-set {
    //        display: block;
    //    }
    //}
}
#header:hover{
    z-index: 103;
}
.editor-page{
    #header,
    #banner,
    #footer {
        &:hover {
            z-index: 99;
            .module-set {
                display: block;
            }
        }
    }
    #header{
        z-index: 6!important;
    }
}
#header{
    z-index: 6;
    //&:hover{
    //    .module-set{
    //        display: block;
    //    }
    //}
}
#footer {
    z-index: 10;
}

/*aid-groups部分的样式*/
#grid1 {
    width: 1200px;
    margin: 0 auto;
}

/*中间画布的样式*/
.content {
    position: relative;
    z-index: 6;
    .aid-groups {
        position: relative;
        z-index: 5;
    }
}

#grid1 {
    .grid-stack-item-content {
        overflow-y: hidden;
        > div {
            min-height: 100%;
        }
    }
}

//.grid-stack-item-content {
//    img {
//        width: 30px;
//    }
//}

.aid-groups {
    //margin: 20px 0;
    margin-bottom: 0;
    .grid-wrap {
        position: relative;
        width: 1200px;
        margin: 0 auto;
        .grid-stack-item-content {
            border: 2px solid transparent;
            box-sizing: border-box;
        }
        &.editor-grid {
            &:hover {
                &:before {
                    display: inline-block;
                    position: absolute;
                    content: "";
                    top: 0;
                    bottom: 0;
                    left: -1px;
                    width: 0;
                    border-left: 1px solid $colorMain;
                    z-index: 200;
                }
                &:after {
                    display: inline-block;
                    position: absolute;
                    content: "";
                    top: 0;
                    bottom: 0;
                    right: -1px;
                    width: 0;
                    border-left: 1px solid $colorMain;
                    z-index: 200;
                }
            }
            .grid-stack-item-content {
                border: 2px dashed #ddd;
                box-sizing: border-box;
            }
            .grid-stack-item:hover {
                .top-btns {
                    display: block;
                }
                .grid-stack-item-content {
                    border: 2px dashed $colorMain;
                }
            }
        }
    }
    #grid1 {
        margin: 0 auto;
        min-height: 700px;
        z-index: 10;
    }
    //.grid-stack-item[data-gs-y='0'] {
    //    .top-btns {
    //        top: -40px;
    //    }
    //}
    .top-btns {
        position: absolute;
        display: none;
        top: -30px;
        right: 10px;
        z-index: 100;
        padding-bottom: 10px;
        height: 33px;
        line-height: 33px;
        font-size: 12px;
        color: #666;
        user-select: none;
        //min-width: 190px;
        .icon {
            display: inline-block;
            font-size: 16px;
            vertical-align: middle;
        }
        span {
            display: inline-block;
            vertical-align: middle;
        }
    }
    .top-btn-groups {
        padding: 0 16px;
        background: rgba(255, 255, 255, 1);
        box-shadow: 0 0 5px 0 rgba(0, 0, 0, 0.3);
        border-radius: 4px;
        > span {
            &:not(:last-child) {
                //margin-right: 12px;
                margin-right: 10px;
            }
        }
        i {
            margin-right: 4px;
        }
        .set,
        .delete {
            cursor: pointer;
        }
        .drag-nav {
            cursor: move;
        }
    }
}

/*背景列表的样式*/
.editor-page {
    #bgs {
        > li {
            &:hover {
                border-bottom: 2px dashed #eee;
                box-sizing: border-box;
                .bg-height {
                    display: inline-block;
                }
            }
        }
    }
}
#bgs{
    min-height: 500px;
}
/*编辑模块名字的弹框样式*/
.name-edit-pop {
    .pop-content {
        width: 380px;
    }
    .middle {
        text-align: center;
        padding: 40px 16px;
        box-sizing: border-box;
    }
    label {
        display: inline-block;
        margin-right: 8px;
        vertical-align: middle;
    }
    .inp-name {
        padding: 0 8px;
        width: 240px;
        height: 30px;
        border-radius: 2px;
        border: 1px solid rgba(221, 221, 221, 1);
        box-sizing: border-box;
    }
}


/*布局模式的样式---待优化*/
.editor-page.layout-mode{
    min-width: 1420px;
    #popupNav{
        position: absolute;
        top:48px;
        left:0!important;
        bottom:0;
        width: 200px;
       .tab-con-wrap{
            max-height: none;
        }
        .menus{
            width: 100%;
            border-radius:0;
            height: 100%;
            box-shadow:0 0 5px 0 rgba(0, 0, 0, 0.2);
        }
        .modules img{
            width: 50px;
            height: 34px;
        }
        .module-name{
            font-size: 12px;
        }
        .edit-page-bg{
            left: 210px;
        }
        .pop-nav-content{
            height: 100%;
        }
        .tab-con-wrap{
            height: calc(100% - 50px);
            overflow-y: auto;
        }
        #scaleLeft{
            display: none;
        }
        .colors > li:not(:nth-child(8n)){
            margin-right: 2px;
        }
    }
    .content,
    #header,
    #banner,
    #footer{
        width: 1204px;
        margin-left: 212px;
    }
}

/*针对东师需要嵌套页面尺寸的修改*/
.middle-area-s{
    min-width: 900px;
    #grid1,
    .aid-groups .grid-wrap{
        width: 900px;
    }
}

@media only screen and (min-width: 1px) and (max-width: 926px) {
    #grid1{
        width: 100%;
    }
    .aid-groups .grid-wrap{
        width: 100%;
    }
    .middle-area-s{
        min-width: 100%;
        #grid1,
        .aid-groups .grid-wrap{
            width: 100%;
        }
    }
    #index:not(.no-app-style) .item-drag-wrap {
        display: inline-block !important;
        max-width: 100% !important;
    }
    #index:not(.no-app-style) .item-drag-wrap.text-module-container {
        width: 100% !important;
    }
}
body{
    zoom: 1!important;
}