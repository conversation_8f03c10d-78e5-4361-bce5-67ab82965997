/* 安徽大学图书馆 二级页面 插件 */
new_element = document.createElement("style");
new_element.innerHTML = ("#index > .header-container{z-index:9;}.page-container .info-box .ds_content p{text-indent:0;}@media only screen and (min-width:1px) and (max-width:926px){.richtext .table{touch-action:auto !important;}.page-header-style37 .header-nav-list .item .item-list{max-height:60vh !important;overflow-y:auto;left:0;right:0;width:100% !important;}.page-header-style37 .header-nav-list .item .item-list.is-long .li-second{width:100%;}.page-header-style37 .header-nav-list > .item:hover > .item-list{display:none;}}");
document.body.appendChild(new_element);

var script = document.createElement("script");
script.language = "javascript";
script.text = "$(function(){function showT(){var bodyW=$('body').outerWidth(true);if(bodyW<927){$('.page-header-style37 .header-nav-list').on('click','.item > a',function(){$(this).parents('.item').find('.item-list').slideToggle();$(this).parents('.item').siblings().find('.item-list').slideUp()})}}setTimeout(showT,1800)});";
document.body.appendChild(script);