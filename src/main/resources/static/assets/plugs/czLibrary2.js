/* 滁州学院图书馆插件 */
new_element = document.createElement("style");
new_element.innerHTML = (".page-header.page-header-style29.mini-header{padding-top:74px}@media only screen and (min-width:927px) and (max-width:4096px){.page-header-style29 .quick-entry{display:none}.page-header-style29 .header-top{margin-bottom:30px}.page-header-style29 .header-top .login-box{margin-right:24px}.page-banner-style10 .full-banner .swiper-slide .img-box{margin:0 auto;}}@media only screen and (min-width:1px) and (max-width:926px){.page-header.page-header-style29.mini-header,.eng-search-style6 .down-info .down-bg-left{display:none;}}");
document.body.appendChild(new_element);

var script = document.createElement("script");
script.language = "javascript";
script.text = "$(function(){function showT(){$('.new-eng-textlist-style19 .eng-tabs-info li .text').each(function(index,item){$(item).attr('title',$(item).text())});$('.eng-textlist-style148 .item-text-list li .item-t').each(function(index,item){$(item).attr('title',$(item).text())});$('.new-eng-image-style1 .list .name').each(function(index,item){$(item).attr('title',$(item).text())});$('.new-eng-image-style1 .list .hint').each(function(index,item){$(item).attr('title',$(item).text())});$('.page-banner-style10 .full-banner .swiper-slide .li-title').each(function(index,item){$(item).attr('title',$(item).text())});$('.page-banner-style10 .full-banner .swiper-slide .hint').each(function(index,item){$(item).attr('title',$(item).text())})}setTimeout(showT,1500)})";
document.body.appendChild(script);

