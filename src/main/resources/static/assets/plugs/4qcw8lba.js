/* 计算机应用技术专业教学资源库插件 */
new_element = document.createElement("style");
new_element.innerHTML = (".page-header-style134 .header-top{z-index:10;}@media only screen and (min-width:927px) and (max-width:4096px){.custom-text-style2 .list-item .big-box{left:-100px;}.custom-text-style2 .list-item:hover .big-box{display:block !important;-webkit-box-shadow:none;-moz-box-shadow:none;box-shadow:none;background:transparent;}}");
document.body.appendChild(new_element);
