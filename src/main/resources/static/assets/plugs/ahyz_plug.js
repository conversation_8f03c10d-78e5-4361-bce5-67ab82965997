// 判断是否移动端
$(function () {
    setTimeout(initAhyzClick, 1000);
//更换验证码
    function initAhyzClick() {
        if (isMobileDevice()){
            $('body').off('click',iconTemp66.data.divId+' .icons-list .item').on('click', iconTemp66.data.divId + ' .icons-list .item', function () {
               window.open('http://thirdapp?ios=cxstudy://cxstudy&android=com.chaoxing.mobile&install=https://apps.chaoxing.com');
            });
        }
    }
})