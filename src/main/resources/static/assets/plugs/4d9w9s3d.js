/* 新疆医科大学智慧图书馆插件 */
new_element = document.createElement("style");
new_element.innerHTML = (".page-header .nav-down-list,.page-header-style1 .header-nav-list .item .item-list{min-width:160px;}.eng-icons-style32 .pagination,.page-footer-style14 .btm{display:none;}.page-footer-style14 .href-list{max-width:800px;}.eng-search-style34 .ipt-box .btn-search{background:url(/upload/portal/bbxy-01.png) no-repeat center center;background-size:28px;text-indent:-99999px;}.page-footer-style14 .href-list{max-width:800px;}.page-footer-style14 .href-list a{display:inline-block;margin-right:16px;}.eng-textlist-style189{height:100%;}.eng-textlist-style189 .eng-tabs-info{height:calc(100% - 42px);overflow:auto;}.eng-search-style34 .radio-list .radio-box{font-size:16px;}.eng-search-style34 .radio-list .radio-box .radio-checked{position:relative;top:-1px;}.eng-search-style34 .radio-list .radio-box .radio-unchecked{top:-2px;}.eng-graphic-style163 .num-box .number{font-family:'SF Pro Display','Helvetica Neue',Helvetica,Tahoma,Arial,'PingFang SC','Hiragino Sans GB','Microsoft YaHei',sans-serif,'Apple Color Emoji','Segoe UI Emoji','Segoe UI Symbol','Noto Color Emoji' !important;font-weight:400;}@media only screen and (min-width:927px) and (max-width:4096px){.eng-graphic-style163 .item-list .item{width:calc(100% / 4);}.eng-graphic-style163 .item-img{display:none;}.eng-graphic-style163 .text-box{margin-left:0;}.eng-search-style34 .search-tabs > span{font-size:20px;margin-right:36px;}.page-footer-style14 .href-list{padding-top:30px;padding-bottom:30px;}.page-footer-style14 .href-list a{font-size:16px;}.page-footer-style14 .href-list > div{font-size:16px;margin-top:20px;}.page-footer-style14 .href-list > div > span{display:block;}.page-footer-style14 .share-in p{font-size:16px;}.page-header-style31.mini-header .img-logo{height:auto;}.page-header-style31.mini-header .header-top{display:block;}.prep_tip{display:none;width:368px;padding:8px 16px;border-radius:4px;background:#fff;box-shadow:0 1px 4px 0 rgba(0,0,0,.06);position:absolute;left:-135px;top:-36px;z-index:2;}.grid-stack-item[data-app-id='2912766'] .text-module-container:hover .prep_tip{display:block;}}@media only screen and (min-width:1px) and (max-width:926px){.page-header{background:#ae262a !important;}.new-eng-text-style9 .eng-tabs-info{padding-top:0;}.page-footer-style14 .href-list{max-width:unset;}.page-footer-style14 .href-list a{margin:0 1.1% 10px;}}@media only screen and (min-width:1201px) and (max-width:1320px){body:not(.responsive-page){zoom:.75;}}@media only screen and (min-width:1321px) and (max-width:1500px){body:not(.responsive-page){zoom:.8;}}@media only screen and (min-width:1501px) and (max-width:1650px){body:not(.responsive-page){zoom:.9;}}");
document.body.appendChild(new_element);

var script = document.createElement("script");
script.language = "javascript";
script.text = "$(function(){function showT(){bodyW=$('body').outerWidth(true);if(bodyW<927){$('.grid-stack-item').each(function(){if($(this).attr('data-app-id')==33996){var cssText=$(this).find('.item-drag-wrap').attr('style')+';display:block!important;margin-top:42px;';$(this).find('.item-drag-wrap').css('cssText',cssText)};if($(this).attr('data-app-id')==33992){var cssText=$(this).find('.item-drag-wrap').attr('style')+';display:block!important;';$(this).find('.item-drag-wrap').css('cssText',cssText)}})}else if(bodyW>927){$('.grid-stack-item[data-app-id=\"2912766\"] .text-module-container').prepend('<div class=\"prep_tip\">查询个人信息，借阅历史，积分及自助离校办理。</div>')};$('.eng-textlist-style189 .item').each(function(){var _html=$(this).find('.time-box .month').html();_html=_html.replace(/-/g,'.');$(this).find('.time-box .month').html(_html)});$('.grid-stack-item[data-app-id=\"layout_2\"]').on('mouseenter','.tab-layout .tab-layout-tabs>li',function(){$(this).trigger('click')})}setTimeout(showT,1500)})";
document.body.appendChild(script);
/* 新疆医科大学智慧图书馆 */
