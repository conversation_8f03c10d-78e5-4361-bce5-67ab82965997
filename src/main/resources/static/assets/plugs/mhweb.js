/* 民航网站插件 */
new_element = document.createElement("style");
new_element.innerHTML = (".fixLogin-item .link-lists .list:first-child{display:none;}.fixLogin-item .link-lists .list:nth-of-type(2){text-align:left;}");
document.body.appendChild(new_element);

var script = document.createElement("script");
script.language = "javascript";
script.text = "$(function(){function showT(){$('.fixLogin-item .link-lists .link-jigou').text('账号登录');$('body').on('click','.fixLogin-item .link-lists .link-jigou',function(){setTimeout(function(){$('.fixLogin-item .link-lists .link-phone').text('手机密码登录');$('.fixLogin-item .link-lists .link-pcode').text('手机验证码登录');$('.fixLogin-item .link-lists .link-jigou').text('账号登录')},200)});$('body').on('click','.fixLogin-item .link-lists .link-phone',function(){setTimeout(function(){$('.fixLogin-item .link-lists .link-jigou').text('账号登录');$('.fixLogin-item .link-lists .link-pcode').text('手机验证码登录');$('.fixLogin-item .link-lists .link-phone').text('手机密码登录')},200)});$('body').on('click','.fixLogin-item .link-lists .link-pcode',function(){setTimeout(function(){$('.fixLogin-item .link-lists .link-jigou').text('账号登录');$('.fixLogin-item .link-lists .link-phone').text('手机密码登录');$('.fixLogin-item .link-lists .link-pcode').text('手机验证码登录')},200)})}setTimeout(showT,1500)})";
document.body.appendChild(script);

// http: //mhaq.caacmooc.org.cn/
// http: //mhdy.caacmooc.org.cn/
// https: //132395wcp.mh.chaoxing.com/