//武汉大学紧急通知弹框逻辑
new_element = document.createElement("style");
new_element.innerHTML = ("#globalIframe{position:fixed;left:0;top:0;right:0;bottom:0;background-color:rgba(0,0,0,.3);z-index:9999;display:none;}#globalIframe .gloabl-iframe-container{left:50%;transform:translate(-50%,-50%);}#globalIframe .iframe-box>div{height:calc(100% - 40px);padding:32px 32px 0;box-sizing:border-box;}.pop_bottom_btn{height:40px;color:#FFF;width:200px;margin:0 auto;text-align:center;line-height:40px;cursor:pointer;position:relative;top:-4px;}#globalIframe .gloabl-iframe-container .mini-iframe{display:none;}.richtext{padding-right:32px;}@media only screen and (min-width:1px) and (max-width:926px){#globalIframe .gloabl-iframe-container{display:block;width:90% !important;}}");
document.body.appendChild(new_element);

var script = document.createElement("script");
script.language = "javascript";
script.text = "$(function(){function showT(){$.get('/engine2/popuser/status',function(res){if(res.data.status==1){var bottomBtn='<div class=\"bg-theme pop_bottom_btn\">已阅读，不再提醒</div>';var timer=null;timer=setInterval(function(){if($('#globalIframe').length>0){$('#globalIframe .iframe-box>div').append(bottomBtn);$('#globalIframe').show();clearInterval(timer)}},300);$('body').on('click','.pop_bottom_btn',function(){$('#globalIframe').hide();$.get('/engine2/popuser/readed',function(res){console.log('res :>> ',res)})})}else if(res.data.status==0){$('#globalIframe').hide()}})};setTimeout(showT,2000)});";
document.body.appendChild(script);