/* 北京理工大学图书馆二级页插件 */
new_element = document.createElement("style");
new_element.innerHTML = (".page-more-container.fixd .type-left{position:fixed;top:0;z-index:999}.page-more-container.fixd .layout-content{float:right}.page-more-container .type-left{width:262px !important}.page-more-container .layout-content{width:calc(100% - 286px) !important}.more-list-style30 .list-info-box .list li{padding:15px 0;}.more-list-style30 .list-info-box .list li .img-box{height:auto}.more-list-style30 .list-info-box .list li .img-box .jqthumb{height:140px !important}.more-list-style3 .list-info-box .list > li{margin-right:20px;width:calc((100% - 20px * 3) / 4);}.more-list-style30 .list-info-box .list li .info-box .info-detail{height:auto;}.more-list-style30 .list-info-box .list li .info-box .info-title{margin-bottom:0;}.more-list-style30 .list-info-box .list li .info-box .info-content{margin-top:8px;}.page-footer-style68 .ft-container .item-links .btn-link{display:block;padding:0;border:0;text-align:left;margin-bottom:12px;height:auto;line-height:normal;width:100%;}.page-footer-style68 .ft-container .item-links .btn-link:hover{border:0;background:transparent;}@media only screen and (min-width:927px) and (max-width:1400px){body:not(.responsive-page){zoom:.7}}@media only screen and (min-width:1px) and (max-width:926px){.page-more-container .layout-content{width:100% !important;}}");
document.body.appendChild(new_element);

var script = document.createElement("script");
script.language = "javascript";
script.text = "$(function(){function showT(){bodyW=$('body').outerWidth(true);$('.page-footer-style68 .ft-container .item.box').find('div').each(function(index,item){if(index==0){$(item).find('span').html('地址：北京房山区良乡东路9号、</br><p style=\"padding-left:83px;\">海淀区中关村南大街5号</p>');}else if(index==1){$(item).find('span').html('电话：010-81382909、</br><p style=\"padding-left:83px;\">010-68913664</p>');}});if(bodyW>927){$(document).scroll(function(){var _H=$('.layout-content').offset().top,_S=$(document).scrollTop();if(_S>=Math.round(_H)){$('.page-more-container').addClass('fixd')}else if((_S<Math.round(_H))){$('.page-more-container').removeClass('fixd')}})}else if(bodyW<927){}}setTimeout(showT,1500)});";
document.body.appendChild(script);
/* 北京理工大学图书馆二级页插件 */
// https://2398uho.mh.chaoxing.com/