/* 湖南大学课程中心插件 */
new_element = document.createElement("style");
new_element.innerHTML = ("@media only screen and (min-width:927px) and (max-width:4096px){.eng-graphic-style668{position:relative;width:1378px;margin:auto;overflow:hidden;}.eng-graphic-style668 .mh-eng-tabs-set .type{width:149px !important;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;}.eng-graphic-style668 .sTempWrap{width:1300px !important;margin:0 auto;}.eng-graphic-style668 .sPrev,.eng-graphic-style668 .sNext{position:absolute;top:5px;z-index:2;display:block;width:30px;height:30px;line-height:28px;text-align:center;border-radius:6px;background:#0d2040;}.eng-graphic-style668 .sPrev{left:0;}.eng-graphic-style668 .sNext{right:2px;}.eng-graphic-style668 .sPrev.off,.eng-graphic-style668 .sNext.off{background:#ccc;cursor:default;}}");
document.body.appendChild(new_element);

var script = document.createElement("script");
script.language = "javascript";
script.text = "$(function(){var importJs={js:function(path){if(!path||path.length===0){throw new Error('参数错误');}var head=document.getElementsByTagName('head')[0];var script=document.createElement('script');script.src=path;script.type='text/javascript';head.appendChild(script)}};importJs.js('https://mh.chaoxing.com/sv2/mh1plugs/file/jquery.agilebins-v1.0.4.min.js');function showT(){$('.eng-graphic-style668').append('<a href=\"javascript:;\"class=\"sPrev\">&lt;</a><a href=\"javascript:;\"class=\"sNext\">&gt;</a>');$('.eng-graphic-style668').agilebins({autoPlay:false,loop:false,pnLoop:false,direction:'left',triggerTime:50,effect:'left',speed:350,easing:'swing',eventType:'click',scrollEl:'.mh-eng-tabs-style2',sPrev:'.sPrev',sNext:'.sNext',visNum:7,scrollNum:7,})}setTimeout(showT,1500)});";
document.body.appendChild(script);