new_element = document.createElement("style");
new_element.innerHTML = ("@media screen and (min-width:423px){html{font-size:18.09px}}@media screen and (min-width:479px){html{font-size:20.48px}}@media screen and (min-width:539px){html{font-size:23.04px}}@media screen and (min-width:639px){html{font-size:27.31px}}@media screen and (min-width:749px){html{font-size:32px}}@media screen and (min-width:843px){html{font-size:36.01px}}@media screen and (min-width:895px){html{font-size:38.22px}}");
document.body.appendChild(new_element);

var script = document.createElement("script");
script.language = "javascript";
script.text = "$(function(){setTimeout(function(){var titlemsg=document.title;var t1d=$(\".txt-title\")[0];if(t1d!=undefined){titlemsg=t1d.innerHTML}var t2d=$(\".txt-name\")[0];if(t2d!=undefined){titlemsg=t2d.innerHTML}var t3d=$(\".text-top-name\")[0];if(t3d!=undefined){titlemsg=t3d.innerHTML}var shareImg=\"http://mh.chaoxing.com/upload/portal/qyg/qyg-share.png\";var description=\"点击查看更多精彩内容！\";var sharUrl=window.location.href;$.ajax({url:\"/outer/wxck\",type:\"get\",data:{url:encodeURIComponent(sharUrl)},success:function(res){var t=res.data.t;var sign=res.data.s;wx.config({debug:false,appId:\"wxeae452dd2a272f23\",timestamp:t,nonceStr:\"Wm3WZYTPz0wzccnW\",signature:sign,jsApiList:[\"updateAppMessageShareData\",\"updateTimelineShareData\"]});wx.ready(function(){wx.updateAppMessageShareData({title:titlemsg,desc:description,link:sharUrl,imgUrl:shareImg,success:function(res){}});wx.updateTimelineShareData({title:titlemsg,desc:description,link:sharUrl,imgUrl:shareImg,success:function(res){}});wx.error(function(res){console.log(res);})})}})},1500)});";
document.body.appendChild(script);