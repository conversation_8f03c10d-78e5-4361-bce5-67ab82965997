/* 中国社会科学院大学图书馆插件 */
new_element = document.createElement("style");
new_element.innerHTML = "@media only screen and (min-width:927px) and (max-width:4096px){.item-drag-wrap .search-module .search-inp::-webkit-input-placeholder{color:#fff}.item-drag-wrap .search-module .search-inp:-ms-input-placeholder{color:#fff}.item-drag-wrap .search-module .search-inp::-ms-input-placeholder{color:#fff}.item-drag-wrap .search-module .search-inp::placeholder{color:#fff}#cxRobotChatContainer1{right:6px !important;bottom:20px !important;}.page-header-style105 .nav-container .btns-box{display:none !important;}.layui-tree .layui-tree-entry .layui-tree-main .layui-tree-iconClick{top:50%;transform:translate(-50%,-50%);}.layui-tree > .layui-tree-set > .layui-tree-entry .tree-nav-one{font-size:18px !important;font-weight:600 !important;}.page-header-style105 .header-nav-list{width:1381px;}.page-header-style105 .nav-container .item .item-list-content dl{width:calc((100% / 6) - 26px);}.page-header-style105 .nav-container .item .item-list-content dl:nth-last-of-type(1){margin:0 0 20px 0;}.page-header-style105 .nav-container{box-shadow:none;padding:0 6px;background:transparent !important;}.page-header-style105 .nav-container .item .one-nav{padding:17px 41px;}.page-header-style105 .nav-container .btns-box{display:none;}.page-header-style105 .nav-container .item .item-list-ƒcontent{width:100%;}.hoverbox1,.hoverbox2{position:relative;background:#ffffff;border:1px solid silver;box-shadow:0px 0px 10px 0px rgba(0,0,0,0.11);border-radius:4px;padding:5px;font-size:16px;text-align:center;display:none;width:350px;}.header-container .img-module-container{cursor:pointer;}.header-container .img-module-container:hover .hoverbox1{display:block;}.header-container .img-module-container:hover .hoverbox2{display:block;}.login-downlist-info{min-width:130px;}}@media only screen and (min-width:1px) and (max-width:926px){.page-header .before-login-1 span{display:none !important;}.page-header-style105 .nav-container .btns-box{display:none;}}";
document.body.appendChild(new_element);

var script = document.createElement("script");
script.language = "javascript";
script.text = "$(function(){function showT(){let obj=$('.more-type-style4 .layui-tree > .layui-tree-set');for(let i=0;i<obj.length;i++){if($(obj[i]).attr('data-id')!=9257157&&$(obj[i]).attr('data-id')!=9588793&&$(obj[i]).attr('data-id')!=9257147&&$(obj[i]).attr('data-id')!=9257149){$(obj[i]).children('.layui-tree-entry').css('cssText','background-color:rgb(153,1,6) !important; height:60px;line-height:60px;padding:0 5px; color:#ffffff !important;text-align:center; ');$(obj[i]).children('.layui-tree-entry').hover(function(){$(obj[i]).children('.layui-tree-entry').find('.tree-nav-one').css('color','#ffffff')})}}let title1='良乡分馆：进馆人数'+$('.header-container .item-drag-wrap').eq(7).find('.item-main p').text()+',座位数'+$('.header-container .item-drag-wrap').eq(4).find('.item-main p').text();let $hoverbox1=$('<div class=\"hoverbox1\"></div>');$hoverbox1.css({'position':'relative','top':'-63px'});$hoverbox1.text(title1);$('#header .img-module-container, #header .video-module-container, .header-container .img-module-container, .header-container .video-module-container').eq(0).append($hoverbox1);let title2='望京分馆：进馆人数'+$('.header-container .item-drag-wrap').eq(6).find('.item-main p').text()+',座位数'+$('.header-container .item-drag-wrap').eq(5).find('.item-main p').text();let $hoverbox2=$('<div class=\"hoverbox2\"></div>');$hoverbox2.text(title2);$('#header .img-module-container, #header .video-module-container, .header-container .img-module-container, .header-container .video-module-container').eq(1).append($hoverbox2)}setTimeout(showT,1500)});";
document.body.appendChild(script);

var script = document.createElement("script");
script.language = "javascript";
script.text = "$(function(){function showT(){var bodyW=$('body').outerWidth(true);if(bodyW>927){$('.login-downlist-info li:first-child a').text('我的图书馆')}}setTimeout(showT,1800)});";
document.body.appendChild(script);