/* 幕课西部行信息资源平台插件 */
new_element = document.createElement("style");
new_element.innerHTML = (".more-list-style1 .list-info-box .list li .right-text{display:none}@media only screen and(min-width:927px)and(max-width:4096px){body{background:#f9fafc url(/sv2/mh1plugs/file/4qd6thxv_bg.jpeg)no-repeat center bottom 82px;background-size:100%auto}.more-type-style57.box.select-box.select-options{padding:4px 16px;border-radius:32px;color:#333;border:1px solid#ccc}.more-type-style57.box.search-wrap input{background:#fff}.more-type-style57.box.search-wrap button{width:54px;background-color:#2E4399}.search-style-57{padding:24px;border-radius:8px}.more-list-style3{padding:0}.more-list-style3.list-info-box.list>li{width:calc((100%-26px*3)/4);min-height:288px;margin-top:0;margin-bottom:24px;margin-right:23px;border:solid 1px#E9E9E9;border-radius:8px}.more-list-style3.list-info-box.list>li.img-box{height:170px;margin:0 auto}.more-list-style3.list-info-box.list>li.btm-box{padding:8px 16px;box-sizing:border-box;text-align:left}.more-list-style3.list-info-box.list>li.title{height:48px;line-height:24px;font-weight:600;margin-bottom:8px}.more-list-style5.list-info-box.list li{padding:4px 0 16px 0;border-bottom:1px solid#ececec}.more-list-style5.list-info-box.list li.img-box{display:none}.more-list-style5.list-info-box.list li.name{font-weight:600;text-indent:24px;background:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACQAAAAkCAYAAADhAJiYAAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAFcSURBVHgB7Zi/SsNQFIfPuWmdCz6AURzc7AM4KGrFrd20RsRVpMkbWJ/AK75AhlLddBNrCg7uOjqI5gWEzJXcYxIQkhq9dbhphvst4fzuCfm4IX84ACUD88K2M6gjMTtaNEEBBOiHODq94ts+yIR2nbumQcY1FAFCq8c3brLRGPu2905jO7O0OAvrKwswfHyDl9cPkCGEOOxfNNy8Ncv2KFUGFTTmXb4WfAcs03x8v0qKbtMv1EbwaaYDBlOGAdaydcnQQjKUC7WPBvX/9CsV2ut4B6zKnqzO8GTSc5QJJW97BDcpkLqTSlVAAciMZUZ0RpkwkYL4w1G8EJCTe9lICiTox16GFpKhhWRoIRlaSIYWkqGFZGghGeUWEhUKYMpkhGag6keHwqSi31y/xzcf0tmP6YfleM2os5BxTIhh65Jv/T2Oidlxbk1DGF1ENgcKiHeGUJz3eeMZys4XB2RdRtQVufAAAAAASUVORK5CYII=)no-repeat left center;background-size:18px 18px}.more-list-style5.list-info-box.list li.hint{color:#333;line-height:20px!important;margin-top:0}.more-list-style5.list-info-box.list li.hint span{color:#999}.page-header.after-login.user-name{max-width:120px}}");
document.body.appendChild(new_element);

var script = document.createElement("script");
script.language = "javascript";
script.text = "$(function(){function showT(){$('.more-type-style57 .box .search-wrap input').attr('placeholder','课程名称/学校名称')}setTimeout(showT,1800)});";
document.body.appendChild(script);
