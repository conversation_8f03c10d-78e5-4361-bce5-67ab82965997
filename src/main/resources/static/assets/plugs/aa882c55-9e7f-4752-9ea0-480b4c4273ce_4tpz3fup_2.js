/* 连云港市博物馆 二级页面 插件 */
new_element = document.createElement("style");
new_element.innerHTML = (".banner-img{position:absolute;top:0;left:0;right:0;height:600px;width:100%;}.banner-img .js-crop-img{width:100%;height:600px;overflow:hidden;}.banner-img img{width:100%;height:100%;}#graphic-detail-vue,#middle-container{margin-top:470px;}.more-type-style14 .top-list{border:none;}.more-type-style14{padding-top:0;}.page-footer-style64 .bottom-box ul li{margin-right:24px;}@media only screen and (min-width:927px) and (max-width:4096px){}@media only screen and (min-width:1px) and (max-width:926px){.banner-img{height:200px;}.banner-img .js-crop-img{height:200px;}#graphic-detail-vue,#middle-container{margin-top:170px;}.more-type-style14{padding-top:14px;}}");
document.body.appendChild(new_element);

var script = document.createElement("script");
script.language = "javascript";
script.text = "$(function(){function showT(){var imgHtml='';var txt=$('.page-header-style88 .nav-container > .item.current').find('.one-t').text();if(typeof typeVue!==\"undefined\"||txt!=''){switch(txt){case'概况':imgHtml=\"<div class='banner-img'>\\n    <div class='js-crop-img'>\\n        <img src='/sv2/mh1plugs/file/lyg_gk.jpg' alt=''>\\n    </div>\\n</div>\";break;case'资讯':imgHtml=\"<div class='banner-img'>\\n    <div class='js-crop-img'>\\n        <img src='/sv2/mh1plugs/file/lyg_zx.jpg' alt=''>\\n    </div>\\n</div>\";break;case'展览':imgHtml=\"<div class='banner-img'>\\n    <div class='js-crop-img'>\\n        <img src='/sv2/mh1plugs/file/lyg_zl.jpg' alt=''>\\n    </div>\\n</div>\";break;case'典藏':imgHtml=\"<div class='banner-img'>\\n    <div class='js-crop-img'>\\n        <img src='/sv2/mh1plugs/file/lyg_dz.jpg' alt=''>\\n    </div>\\n</div>\";break;case'研究':imgHtml=\"<div class='banner-img'>\\n    <div class='js-crop-img'>\\n        <img src='/sv2/mh1plugs/file/lyg_yj.jpg' alt=''>\\n    </div>\\n</div>\";break;case'社教':imgHtml=\"<div class='banner-img'>\\n    <div class='js-crop-img'>\\n        <img src='/sv2/mh1plugs/file/lyg_sj.jpg' alt=''>\\n    </div>\\n</div>\";break;case'服务':imgHtml=\"<div class='banner-img'>\\n    <div class='js-crop-img'>\\n        <img src='/sv2/mh1plugs/file/lyg_fw.jpg' alt=''>\\n    </div>\\n</div>\";break;case'彦涵美术馆':imgHtml=\"<div class='banner-img'>\\n    <div class='js-crop-img'>\\n        <img src='/sv2/mh1plugs/file/lyg_ms.jpg' alt=''>\\n    </div>\\n</div>\";break;default:imgHtml='';$('#middle-container').css('margin-top','24px');$('#graphic-detail-vue').css('margin-top','24px')}if($('#middle-container').length>0){$('#middle-container').before(imgHtml);cropImgFun('.banner-img')}else if($('#graphic-detail-vue').length>0){$('#graphic-detail-vue').before(imgHtml);loadScript(\"//static.mh.chaoxing.com/assets/lib/jqthumb.min.js?v=20240509\",function(){cropImgFun('.banner-img')})}}else{imgHtml=\"<div class='banner-img'>\\n    <div class='js-crop-img'>\\n        <img src='/sv2/mh1plugs/file/lyg_detail.jpg' alt=''>\\n    </div>\\n</div>\";$('#graphic-detail-vue').before(imgHtml);loadScript(\"//static.mh.chaoxing.com/assets/lib/jqthumb.min.js?v=20240509\",function(){cropImgFun('.banner-img')})}}function loadScript(url,callback){var script=document.createElement(\"script\");script.type=\"text/javascript\";script.src=url;script.defer=false;script.async=false;document.getElementsByTagName(\"head\")[0].appendChild(script);if(script.readyState){script.onreadystatechange=function(){if(script.readyState==\"loaded\"||script.readyState==\"complete\"){script.onreadystatechange=null;callback()}}}else{script.onended=callback()}}setTimeout(showT,1500)});";
document.body.appendChild(script);