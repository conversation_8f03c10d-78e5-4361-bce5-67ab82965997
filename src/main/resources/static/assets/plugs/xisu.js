/* 西安外国语大学图书馆镜像插件 */
new_element = document.createElement("style");
new_element.innerHTML = ("@media only screen and (min-width:1px) and (max-width:926px){.new-eng-text-style9 .eng-tabs-info{padding:0;}.new-eng-text-style9 .eng-tabs-info li{margin-bottom:0px !important;}.layout-content:not(:last-child){margin-bottom:10px !important;}.eng-search-style3 .search-content{padding:12px 16px;}}@media only screen and (min-width:927px) and (max-width:4096px){.page-header-style136 .nav-container .item{padding:8px 22px 1px;}.page-header-style136 .nav-container .item .item-list-only{width:272px;}.page-header-style136 .nav-container .item .item-list-only .item-list-content .dl .three-list a{text-align:left;}.page-header-style128 .header-nav-list .childTwo .nav-down-list{width:172px;padding:0 20px !important;box-sizing:border-box;text-align:left;}.page-header-style128 .header-nav-list .txt-list .li-second{width:116px;}.page-header-style128 .header-nav-list .three-list a{overflow:hidden;text-overflow:ellipsis;white-space:nowrap;}}");
document.body.appendChild(new_element);