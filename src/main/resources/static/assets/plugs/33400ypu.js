/* 北京服装学院图书馆插件 */
new_element = document.createElement("style");
new_element.innerHTML = (".eng-search-style52 .down-info .right-content-item1{width:70%;}.eng-search-style52 .down-info .right-content-item3{display:none;}.eng-graphic-style568 .cur-eng-tabs li{height:45px;line-height:45px;}.eng-graphic-style568 .swiper-slide .item .btm-box .hint{font-size:12px;}.page-header .after-login{z-index:9992}.eng-graphic-style567,.eng-graphic-style567 .list{padding-top:0;}.eng-search-style52 .nav-list .item:nth-of-type(3) .item-list .li-second{float:right;margin:0 0 20px 30px;}@media only screen and (min-width:1px) and (max-width:926px){.eng-search-style52 .down-info .right-content-item1{width:100% !important;}.page-header-style124 .header-top{background:rgb(105,167,148) !important;}.page-header-style124 .obj-name,.page-header-style124 .menu-status{color:#fff;}.page-header-style124 .nav-container{background:#fff !important;}.page-header-style124 .nav-container .item .one-nav{border-bottom:1px solid;}.page-header-style10000{display:none;}.eng-search-style52 .ipt-box .ipt{width:46% !important;}.grid-stack .grid-stack-item[data-app-id='1799821'] .eng-graphic-style566{display:none;}}@media only screen and (min-width:927px){#firstSection{min-height:100vh !important;}.jn-slide-style1 .swiper-container,.jn-slide-style1 .swiper-wrapper,.jn-slide-style1 .swiper-slide{height:100vh !important;}}@media only screen and (min-width:927px) and (max-width:1600px){body{zoom:0.75;}#bgs .bg-box{background-size:100% auto;}.jn-slide-style1 .swiper-container,.jn-slide-style1 .swiper-wrapper,.jn-slide-style1 .swiper-slide{width:100% !important;min-height:870px;}}@media only screen and (min-width:1601px) and (max-width:1919px){body{zoom:0.9;}#bgs .bg-box{background-size:100% auto;}.jn-slide-style1 .swiper-container,.jn-slide-style1 .swiper-wrapper,.jn-slide-style1 .swiper-slide{width:100% !important;min-height:905px;}}");
document.body.appendChild(new_element);

var script = document.createElement("script");
script.language = "javascript";
script.text = "$(function(){function showT(){bodyW=$('body').outerWidth(true);if(bodyW<927){$('.grid-stack-item').each(function(){if($(this).attr('data-app-id')==1800984){$(this).attr('id','clone1')};if($(this).attr('data-app-id')==1799821){$(this).attr('style','height:340px');$(this).clone().prependTo('#clone1');$(this).attr('style','display:none!important')}})}$('.eng-search-style52 .top-item .down-box .tip').html('搜索资源')}setTimeout(showT,2000)});";
document.body.appendChild(script);
/* 北京服装学院图书馆插件 */

