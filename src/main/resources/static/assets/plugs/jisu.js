/* 吉林外国语大学插件 */
var script = document.createElement("script");
script.language = "javascript";
script.text = "$(function(){function showT(){$('#header .nav-container').clone().prependTo('.eng-search-style29');$('#header .nav-container').remove();$('#header .page-header').removeClass('bg-theme');$('.grid-stack-item').each(function(){if($(this).attr('data-app-id')=='layout_0'){$(this).find('.eng-search-style29').addClass('page-header-style52');$(this).parents('.grid-wrap').css({'margin-top':'-70px','z-index':'221'});$(this).find('.eng-search-style29 .nav-container').css({'margin-top':'0','position':'absolute','top':'0','right':'0','z-index':'10000'});$(this).find('.eng-search-style29 .item-list').css('left','-296px')}});$('.page-header-style52').on('click','.nav-container .item .one-nav',function(e){e.stopPropagation();$(this).toggleClass('active');$(this).parents('.item').siblings().find('.one-nav').removeClass('active');$(this).parents('.item').siblings().find('.item-list').hide();$(this).parents('.item').find('.item-list').slideToggle()});$('.page-header-style52 .nav-container .item-list-content').click(function(e){e.stopPropagation()});$(document).click(function(){$('.page-header-style52 .nav-container .item .item-list').slideUp();$('.nav-container .item .one-nav').removeClass('active');$('.grid-stack-item').each(function(){if($(this).attr('data-app-id')==15328){$(this).css('height','350px')}})});bodyW=$('body').outerWidth(true);if(bodyW<927){$('.page-header-style52 .obj-name').attr('style','display:none!important');$('.page-header-style52 .img-logo').attr('style','display:block!important;max-width: 70%;max-height: 80%;top: 50%;left: 50%;transform: translate(-50%, -50%);');$('.grid-stack-item').each(function(){if($(this).attr('data-app-id')=='layout_0'){$(this).parents('.grid-wrap').css({'margin-top':'0'})};if($(this).attr('data-app-id')=='layout_2'){$(this).css({'background':'#005aae'})}})}}setTimeout(showT,3000)});";
document.body.appendChild(script);
/* 吉林外国语大学插件 */
// https://133227af.mh.chaoxing.com/