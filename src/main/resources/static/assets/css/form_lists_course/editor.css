@charset "UTF-8";
body {
  background-color: rgb(242, 244, 247);
}

.inlineMiddle {
  display: inline-block;
  vertical-align: middle;
}

.font500 {
  font-weight: 500;
}

#pageTopWrap,
#editor,
#index {
  min-width: 1200px;
}

#mh-header {
  position: relative;
}

/*标题的文字加粗*/
.title-t > b {
  font-weight: bold;
}

.fs15 {
  font-size: 15px;
}

/*按钮的样式*/
.btn-fff-s {
  width: 44px;
  height: 24px;
  text-align: center;
  border-radius: 2px;
  color: #3D82F2;
  font-size: 14px;
  border: 1px solid;
  cursor: pointer;
  background-color: #fff;
}

.btn-blue-s {
  width: 44px;
  height: 24px;
  text-align: center;
  background: #3d82f2;
  border-radius: 2px;
  color: #fff;
  font-size: 14px;
  cursor: pointer;
}

.btn-blue-s:disabled {
  background-color: #ccc;
  color: #fff;
  cursor: default;
}

.sortable-ghost {
  opacity: 0;
}

#items-1 {
  width: 100%;
  margin: 0 auto;
  min-height: 600px;
  padding: 20px 0;
  word-break: break-all;
}
#items-1.list-dash-border {
  border: 2px dotted #ccc;
}

.list-group {
  margin: 20px;
}
.list-group .list-group-item {
  position: relative;
  margin: 0 auto;
  margin-bottom: 20px;
  min-height: 80px;
}
.list-group .list-group-item .top-btns {
  position: absolute;
  display: none;
  top: -30px;
  right: 10px;
  z-index: 100;
  padding-bottom: 10px;
  height: 33px;
  line-height: 33px;
  font-size: 12px;
  color: #666;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
.list-group .list-group-item .top-btns .top-btn-groups {
  padding: 0 16px;
  background: #fff;
  -webkit-box-shadow: 0 0 5px 0 rgba(0, 0, 0, 0.3);
  box-shadow: 0 0 5px 0 rgba(0, 0, 0, 0.3);
  border-radius: 4px;
}
.list-group .list-group-item .top-btns .top-btn-groups i {
  margin-right: 4px;
}
.list-group .list-group-item .top-btns .top-btn-groups .set {
  cursor: pointer;
}
.list-group .list-group-item .module-box {
  width: 1200px;
  margin: 0 auto;
}
.list-group .list-group-item .padding50 {
  padding: 30px 46px;
}
.list-group .list-group-item:hover .top-btns {
  display: block;
}
.list-group .list-group-item.list-shadow {
  background-color: white;
  -webkit-box-shadow: 0 1px 12px #EDEEF0;
          box-shadow: 0 1px 12px #EDEEF0;
}
.list-group .list-group-item.list-course-section .module-box {
  padding-top: 30px;
}
.list-group .list-group-item.list-course-comment {
  background: #F8F9FC;
}
.list-group .list-group-item.list-course-comment .module-box {
  padding-top: 50px;
}
.list-group .list-group-item .item-engine-title {
  position: relative;
  padding: 0;
  line-height: 31px;
  overflow: hidden;
  margin-bottom: 25px;
  text-align: center;
}
.list-group .list-group-item .item-engine-title .title-t span {
  display: block;
  font-size: 22px;
  color: #131B26;
}
.list-group .list-group-item .item-engine-title .title-icon {
  height: 4px;
  width: 26px;
  margin: 5px auto;
}
.list-group .list-group-item .item-engine-title .title-bg {
  display: inline-block;
  background-repeat: no-repeat;
  background-image: url(../../images/form_lists/titlebg.png);
  background-position: top left;
  height: 36px;
  width: 710px;
  margin-top: 2px;
  float: right;
}
.list-group .list-group-item .table-content {
  padding: 10px;
}

button {
  margin: 40px 20px;
  float: right;
}

.sp-container button {
  margin: 8px 0 0;
}

.setListDialog .middle {
  min-height: 200px;
  max-height: 600px;
  overflow-y: auto;
}
.setListDialog .middle .item-engine-title .title-t {
  font-size: 14px;
}
.setListDialog .middle .item-engine-title .title-t .title-icon {
  width: 5px;
  height: 12px;
  margin-right: 6px;
}
.setListDialog .middle .item-engine-title .title-t span {
  display: inline-block;
  vertical-align: middle;
}
.setListDialog .item-set-box .show-lists {
  margin-bottom: 20px;
}
.setListDialog .item-set-box .show-lists label {
  margin: 10px;
  display: inline-block;
  line-height: 20px;
}
.setListDialog .item-set-box .show-lists label span, .setListDialog .item-set-box .show-lists label input {
  display: inline-block;
  vertical-align: middle;
}
.setListDialog .item-set-box .sort-box .list-title {
  margin: 20px 0;
}
.setListDialog .item-set-box .sort-box ul li {
  line-height: 30px;
  overflow: hidden;
  border-bottom: 1px solid #f5f5f5;
}
.setListDialog .item-set-box .sort-box ul li:last-child {
  border-bottom: 0;
}
.setListDialog .item-set-box .sort-box ul li .list-name {
  max-width: 80%;
  overflow: hidden;
  -ms-text-overflow: ellipsis;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.setListDialog .item-set-box .sort-box ul li .list-icon {
  cursor: pointer;
  margin-top: 8px;
}
.setListDialog .item-set-box .sort-box .selected-list-box .number-box {
  width: 10%;
  float: left;
}
.setListDialog .item-set-box .sort-box .selected-list-box .list-box {
  width: 90%;
  float: left;
}

.dataSourceDialog .dialog-content .middle {
  max-height: 300px;
  overflow-y: auto;
}
.dataSourceDialog .dialog-content .middle .show-lists {
  padding-top: 12px;
}
.dataSourceDialog .dialog-content .middle .show-lists .radio-box {
  padding-right: 8px;
  margin-bottom: 12px;
  display: inline-block;
}
.dataSourceDialog .dialog-content .middle .show-lists .radio-box input {
  margin: 0 8px;
}

.text-box {
  position: relative;
  font-size: 0;
  padding: 30px 0;
}
.text-box .left-box {
  width: 545px;
  height: 322px;
  display: inline-block;
  vertical-align: top;
}
.text-box .text-avatar {
  width: 545px;
  height: 322px;
  border: 1px solid #E8E8E8;
  border-radius: 6px;
  overflow: hidden;
  -webkit-box-sizing: border-box;
     -moz-box-sizing: border-box;
          box-sizing: border-box;
}
.text-box .text-avatar img {
  width: 100%;
  height: 100%;
}
.text-box .text-right {
  width: calc(100% - 565px);
  display: inline-block;
  vertical-align: top;
  padding: 4px 0 0 20px;
  -webkit-box-sizing: border-box;
     -moz-box-sizing: border-box;
          box-sizing: border-box;
}
.text-box .text-right .text-title {
  position: relative;
  overflow: hidden;
}
.text-box .text-right .text-top-name {
  font-size: 24px;
  font-family: PingFangSC, PingFangSC-Medium;
  line-height: 34px;
  width: calc(100% - 40px);
  float: left;
}
.text-box .text-right .collect-box {
  margin-top: 8px;
  color: #ACB4BF;
  cursor: pointer;
}
.text-box .text-right .collect-box.course-collected i {
  color: #EDAF69;
}
.text-box .text-right .text-top-info {
  margin-top: 20px;
  overflow: hidden;
  line-height: 22px;
  background: #F5F7FA;
  padding: 20px;
  -webkit-box-sizing: border-box;
     -moz-box-sizing: border-box;
          box-sizing: border-box;
}
.text-box .text-right .text-top-info .text-info-title {
  color: #646873;
}
.text-box .text-right .text-top-info .text-info-content {
  color: #474C59;
  margin-top: 14px;
}
.text-box .course-bottom-box {
  margin-top: 19px;
}
.text-box .course-bottom-box .visit-number-box .visit-number-title {
  color: #8A8B99;
}
.text-box .course-bottom-box .visit-number-box .visit-number-text {
  color: #FAAF43;
  margin-left: 10px;
}
.text-box .course-bottom-box .into-course {
  width: 130px;
  height: 44px;
  background: -webkit-linear-gradient(127.78deg, #5A33FF -34.58%, #6CC7FF 132%);
  background: linear-gradient(322.22deg, #5A33FF -34.58%, #6CC7FF 132%);
  -webkit-box-shadow: 0px 2px 9px rgba(103, 161, 255, 0.495782);
          box-shadow: 0px 2px 9px rgba(103, 161, 255, 0.495782);
  border-radius: 27px;
  color: #fff;
  text-align: center;
  line-height: 44px;
  display: inline-block;
}

.section-list-box {
  width: 680px;
  margin: 0 auto;
}
.section-list-box .list {
  border-bottom: 1px solid #F2F2F2;
  cursor: pointer;
}
.section-list-box .list .list-title {
  padding: 17px 0 16px 0;
  display: block;
}
.section-list-box .list .list-title .list-num {
  width: 50px;
  height: 28px;
  line-height: 28px;
  text-align: center;
  background: #ECF1F7;
  border-radius: 15px;
  color: #8A8B99;
  margin-right: 10px;
}
.section-list-box .list .list-title .list-name {
  line-height: 25px;
  color: #131B26;
  max-width: calc(100% - 100px);
}
.section-list-box .list .list-title .icon-up {
  color: #CAD5E5;
  line-height: 28px;
  display: inline-block;
  padding: 0 10px;
}
.section-list-box .list .list-title .icon-up.rotate180 {
  transform: rotate(180deg);
  -ms-transform: rotate(180deg); /* IE 9 */
  -moz-transform: rotate(180deg); /* Firefox */
  -webkit-transform: rotate(180deg); /* Safari 和 Chrome */
  -o-transform: rotate(180deg); /* Opera */
}
.section-list-box .list .list-title:hover .list-name {
  color: #3A8BFF;
}
.section-list-box .list .list-title:hover .icon-up {
  color: #3A8BFF;
}
.section-list-box .list .list-second-box {
  display: none;
}
.section-list-box .list .list-second-box .list-second {
  padding: 19px 0 19px 65px;
  -webkit-box-sizing: border-box;
     -moz-box-sizing: border-box;
          box-sizing: border-box;
  line-height: 22px;
  color: #131B26;
  display: block;
}
.section-list-box .list .list-second-box .list-second:hover {
  background-color: #F0F6FF;
}
.section-list-box .list .list-second-box .list-second .list-icon {
  width: 6px;
  height: 4px;
  border-radius: 2px;
  background-color: #F28C24;
}
.section-list-box .list .list-second-box .list-second .list-num-2 {
  display: none;
}
.section-list-box .list .list-second-box .list-second .list-name-2 {
  max-width: calc(100% - 100px);
}
.section-list-box .show-all-box {
  color: #8A8B99;
  line-height: 22px;
  padding: 30px 0;
  text-align: center;
}
.section-list-box .show-all-box .show-all-btn {
  display: inline-block;
  cursor: pointer;
}

.page-top .show-name .btn-edit-form {
  font-size: 14px;
  margin-left: 10px;
}
.page-top .show-name .btn-edit-form:before {
  color: #3D82F2;
}

#formbg {
  position: fixed;
  width: 100%;
  z-index: -1;
  top: 0;
  bottom: 0;
  overflow: hidden;
}
#formbg > div {
  width: 100%;
  height: 100%;
}

.iframe-box iframe {
  width: 100%;
  min-height: 30px;
}

.lh-32 {
  line-height: 32px;
}

.form-search-module {
  position: relative;
}
.form-search-module .search-inp {
  width: 100%;
  height: 32px;
  font-size: 14px;
  color: #333;
  padding: 0 40px 0 10px;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  border: 1px solid #c1c1c1;
  border-radius: 4px;
  background-color: transparent;
}
.form-search-module .icon-search {
  position: absolute;
  top: 50%;
  right: 10px;
  font-size: 14px;
  color: #c1c1c1;
  transform: translateY(-50%);
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  cursor: pointer;
}

#mh-footer {
  position: relative;
}

.page-header .top-box .after-login {
  z-index: 9;
}

@media only screen and (min-width: 1px) and (max-width: 926px) {
  .fs14 {
    font-size: 0.875rem;
  }
  #items-1 {
    width: 100%;
  }
  #index {
    padding-bottom: 66px;
    min-width: auto;
  }
  .list-group .list-group-item {
    margin-bottom: 0;
  }
  .list-group .list-group-item .module-box {
    width: 100%;
  }
  .list-group .list-group-item .module-box .text-box {
    padding: 0 0.9375rem;
  }
  .list-group .list-group-item .module-box .text-box .left-box {
    width: 100%;
    height: 10.3125rem;
    border-radius: 0.4375rem;
    overflow: hidden;
  }
  .list-group .list-group-item .module-box .text-box .text-avatar {
    width: 100%;
    height: 100%;
  }
  .list-group .list-group-item .module-box .text-box .text-right {
    width: 100%;
    padding: 0.9375rem 0 0;
  }
  .list-group .list-group-item .module-box .text-box .text-right .text-top-name {
    font-size: 1.1875rem;
    line-height: 1.65625rem;
  }
  .list-group .list-group-item .module-box .text-box .text-right .text-sub {
    font-size: 0.875rem;
    margin-top: 0.4375rem;
  }
  .list-group .list-group-item .module-box .text-box .text-right .text-top-info {
    margin-top: 0.9375rem;
    padding: 0.9375rem;
  }
  .list-group .list-group-item .module-box .text-box .text-right .text-top-info .text-info-title {
    font-size: 0.875rem;
  }
  .list-group .list-group-item .module-box .text-box .text-right .text-top-info .text-info-content {
    font-size: 0.9375rem;
    margin-top: 0.4375rem;
  }
  .list-group .list-group-item .module-box .text-box .course-bottom-box {
    margin-top: 1.25rem;
  }
  .list-group .list-group-item .module-box .text-box .course-bottom-box .visit-number-box {
    padding-bottom: 1.09375rem;
  }
  .list-group .list-group-item .module-box .text-box .course-bottom-box .visit-number-box .visit-number-title {
    font-size: 0.875rem;
  }
  .list-group .list-group-item .module-box .text-box .course-bottom-box .visit-number-box .visit-number-text {
    font-size: 1.1875rem;
  }
  .list-group .list-group-item .module-box .text-box .course-bottom-box .into-course {
    margin: 0.625rem 0;
    width: 100%;
  }
  .list-group .list-group-item .module-box .section-list-box {
    width: 100%;
    padding: 0 0.9375rem;
    -webkit-box-sizing: border-box;
       -moz-box-sizing: border-box;
            box-sizing: border-box;
  }
  .list-group .list-group-item .module-box .section-list-box .list {
    border-bottom: 0;
  }
  .list-group .list-group-item .module-box .section-list-box .list .list-title {
    padding: 0.625rem 0;
    display: -webkit-box;
    display: -webkit-flex;
    display: -moz-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
       -moz-box-align: center;
        -ms-flex-align: center;
            align-items: center;
  }
  .list-group .list-group-item .module-box .section-list-box .list .list-title .list-num {
    width: 2.25rem;
    height: 1.46875rem;
    line-height: 1.46875rem;
  }
  .list-group .list-group-item .module-box .section-list-box .list .list-title .list-name {
    font-size: 1.0625rem;
    line-height: 1.5rem;
  }
  .list-group .list-group-item .module-box .section-list-box .list .list-title .icon-up {
    line-height: 1.5rem;
    margin-left: auto;
  }
  .list-group .list-group-item .module-box .section-list-box .list .list-second-box .list-second {
    display: -webkit-box;
    display: -webkit-flex;
    display: -moz-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
       -moz-box-align: center;
        -ms-flex-align: center;
            align-items: center;
    padding: 0.9375rem 1.25rem 0.9375rem 2.875rem;
  }
  .list-group .list-group-item .module-box .section-list-box .list .list-second-box .list-second .list-icon {
    display: none;
  }
  .list-group .list-group-item .module-box .section-list-box .list .overHidden1 {
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    white-space: unset;
  }
  .list-group .list-group-item.padding30 {
    padding: 0.9375rem;
    -webkit-box-sizing: border-box;
       -moz-box-sizing: border-box;
            box-sizing: border-box;
  }
  .list-group .list-group-item.list-shadow {
    -webkit-box-shadow: inset 0px -1px 0px #F2F2F2;
            box-shadow: inset 0px -1px 0px #F2F2F2;
  }
  .list-group .list-group-item .item-engine-title {
    margin-bottom: 0.9375rem;
  }
  .list-group .list-group-item .item-engine-title .title-t span {
    font-size: 1.1875rem;
  }
  .list-group .list-group-item .item-engine-title .title-icon {
    width: 0.8125rem;
    height: 0.125rem;
    border-radius: 0.125rem;
  }
}
@media only screen and (min-width: 1px) and (max-width: 1080px) {
  .touchmachine .fs14 {
    font-size: 0.875rem;
  }
  .touchmachine #items-1 {
    width: 100%;
  }
  .touchmachine #index {
    padding-bottom: 66px;
    min-width: auto;
  }
  .touchmachine .list-group .list-group-item {
    margin-bottom: 0;
  }
  .touchmachine .list-group .list-group-item .module-box {
    width: 100%;
  }
  .touchmachine .list-group .list-group-item .module-box .text-box {
    padding: 0 0.9375rem;
  }
  .touchmachine .list-group .list-group-item .module-box .text-box .left-box {
    width: 100%;
    height: 10.3125rem;
    border-radius: 0.4375rem;
    overflow: hidden;
  }
  .touchmachine .list-group .list-group-item .module-box .text-box .text-avatar {
    width: 100%;
    height: 100%;
  }
  .touchmachine .list-group .list-group-item .module-box .text-box .text-right {
    width: 100%;
    padding: 0.9375rem 0 0;
  }
  .touchmachine .list-group .list-group-item .module-box .text-box .text-right .text-top-name {
    font-size: 1.1875rem;
    line-height: 1.65625rem;
  }
  .touchmachine .list-group .list-group-item .module-box .text-box .text-right .text-sub {
    font-size: 0.875rem;
    margin-top: 0.4375rem;
  }
  .touchmachine .list-group .list-group-item .module-box .text-box .text-right .text-top-info {
    margin-top: 0.9375rem;
    padding: 0.9375rem;
    line-height: normal;
  }
  .touchmachine .list-group .list-group-item .module-box .text-box .text-right .text-top-info .text-info-title {
    font-size: 0.875rem;
  }
  .touchmachine .list-group .list-group-item .module-box .text-box .text-right .text-top-info .text-info-content {
    font-size: 0.9375rem;
    margin-top: 0.4375rem;
  }
  .touchmachine .list-group .list-group-item .module-box .text-box .course-bottom-box {
    margin-top: 1.25rem;
  }
  .touchmachine .list-group .list-group-item .module-box .text-box .course-bottom-box .visit-number-box {
    padding-bottom: 1.09375rem;
  }
  .touchmachine .list-group .list-group-item .module-box .text-box .course-bottom-box .visit-number-box .visit-number-title {
    font-size: 0.875rem;
  }
  .touchmachine .list-group .list-group-item .module-box .text-box .course-bottom-box .visit-number-box .visit-number-text {
    font-size: 1.1875rem;
  }
  .touchmachine .list-group .list-group-item .module-box .text-box .course-bottom-box .into-course {
    margin: 0.625rem 0;
    width: 100%;
    line-height: 2.75rem;
    height: 2.75rem;
    font-size: 1rem;
  }
  .touchmachine .list-group .list-group-item .module-box .section-list-box {
    width: 100%;
    padding: 0 0.9375rem;
    -webkit-box-sizing: border-box;
       -moz-box-sizing: border-box;
            box-sizing: border-box;
  }
  .touchmachine .list-group .list-group-item .module-box .section-list-box .list {
    border-bottom: 0;
  }
  .touchmachine .list-group .list-group-item .module-box .section-list-box .list .list-title {
    padding: 0.625rem 0;
    display: -webkit-box;
    display: -webkit-flex;
    display: -moz-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
       -moz-box-align: center;
        -ms-flex-align: center;
            align-items: center;
  }
  .touchmachine .list-group .list-group-item .module-box .section-list-box .list .list-title .list-num {
    width: 2.25rem;
    height: 1.46875rem;
    line-height: 1.46875rem;
  }
  .touchmachine .list-group .list-group-item .module-box .section-list-box .list .list-title .list-name {
    font-size: 1.0625rem;
    line-height: 1.5rem;
  }
  .touchmachine .list-group .list-group-item .module-box .section-list-box .list .list-title .icon-up {
    line-height: 1.5rem;
    margin-left: auto;
  }
  .touchmachine .list-group .list-group-item .module-box .section-list-box .list .list-second-box .list-second {
    display: -webkit-box;
    display: -webkit-flex;
    display: -moz-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
       -moz-box-align: center;
        -ms-flex-align: center;
            align-items: center;
    padding: 0.9375rem 1.25rem 0.9375rem 2.875rem;
  }
  .touchmachine .list-group .list-group-item .module-box .section-list-box .list .list-second-box .list-second .list-icon {
    display: none;
  }
  .touchmachine .list-group .list-group-item .module-box .section-list-box .list .overHidden1 {
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    white-space: unset;
  }
  .touchmachine .list-group .list-group-item.padding30 {
    padding: 0.9375rem;
    -webkit-box-sizing: border-box;
       -moz-box-sizing: border-box;
            box-sizing: border-box;
  }
  .touchmachine .list-group .list-group-item.list-shadow {
    -webkit-box-shadow: inset 0px -1px 0px #F2F2F2;
            box-shadow: inset 0px -1px 0px #F2F2F2;
  }
  .touchmachine .list-group .list-group-item .item-engine-title {
    margin-bottom: 0.9375rem;
    line-height: normal;
  }
  .touchmachine .list-group .list-group-item .item-engine-title .title-t span {
    font-size: 1.1875rem;
  }
  .touchmachine .list-group .list-group-item .item-engine-title .title-icon {
    width: 0.8125rem;
    height: 0.125rem;
    border-radius: 0.125rem;
  }
}