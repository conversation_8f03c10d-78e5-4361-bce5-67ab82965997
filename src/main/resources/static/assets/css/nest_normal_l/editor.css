@charset "UTF-8";
body {
  background-color: #F5F5F5;
  min-width: 1404px;
}

#index {
  position: relative;
}

/*sid-groups部分的样式*/
#index {
  position: relative;
}
#index #header {
  position: relative !important;
}
#index .editor-page #header {
  top: 0 !important;
}

#header,
#banner,
#footer {
  position: relative;
  z-index: 5;
}

#header:hover {
  z-index: 200;
}

.editor-page #banner:hover {
  z-index: 99;
}
.editor-page #footer:hover {
  z-index: 199;
}
.editor-page #header:hover {
  z-index: 101;
}
.editor-page #firstSection:hover {
  z-index: 101;
}

#banner.show-set {
  z-index: 99;
}

#footer {
  z-index: 101;
}

#header {
  top: 0 !important;
  left: 0;
  width: 100%;
  z-index: 1000;
}

#index #header {
  position: relative !important;
}

.editor-page #header {
  z-index: 100;
}

/*中间画布的样式*/
.container {
  position: relative;
}

.content {
  width: 1404px;
  margin: 0 auto;
}
.content.full-width {
  width: 100%;
}
.content .aid-group-wrap {
  position: relative;
  z-index: 101;
}
.content .aid-groups {
  position: relative;
  width: 1404px;
  margin: 0 auto;
  z-index: 2;
}
.content .aid-groups .grid-wrap {
  width: 100%;
}
.content .aid-groups .grid-wrap:not(:last-child) {
  margin-bottom: 12px;
}
.content .aid-groups .grid-wrap:not(:first-child) {
  margin-top: 12px;
}

.grid-wrap {
  position: relative;
  margin: 0 auto;
  width: 1404px;
}
.grid-wrap .grid-stack-item-content {
  border: 2px solid transparent;
  -webkit-box-sizing: border-box;
     -moz-box-sizing: border-box;
          box-sizing: border-box;
}
.grid-wrap.editor-grid .grid-stack-item-content {
  border: 2px dashed #ddd;
  -webkit-box-sizing: border-box;
     -moz-box-sizing: border-box;
          box-sizing: border-box;
}
.grid-wrap.editor-grid .grid-stack-item:hover .top-btns {
  display: block;
}
.grid-wrap.editor-grid .grid-stack-item:hover .grid-stack-item-content {
  border: 2px dashed #3D82F2;
}

#index .grid-stack .grid-stack-item-content {
  overflow-y: hidden;
}
#index .grid-stack .grid-stack-item-content > div {
  min-height: 100%;
}

.top-btns {
  position: absolute;
  display: none;
  top: -30px;
  right: 10px;
  z-index: 100;
  padding-bottom: 10px;
  height: 33px;
  line-height: 33px;
  font-size: 12px;
  color: #666;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
}
.top-btns .icon {
  display: inline-block;
  font-size: 16px;
  vertical-align: middle;
}
.top-btns span {
  display: inline-block;
  vertical-align: middle;
}

.top-btn-groups {
  padding: 0 16px;
  background: rgb(255, 255, 255);
  -webkit-box-shadow: 0 0 5px 0 rgba(0, 0, 0, 0.3);
          box-shadow: 0 0 5px 0 rgba(0, 0, 0, 0.3);
  border-radius: 4px;
}
.top-btn-groups > span:not(:last-child) {
  margin-right: 12px;
}
.top-btn-groups i {
  margin-right: 4px;
}
.top-btn-groups .set,
.top-btn-groups .delete {
  cursor: pointer;
}
.top-btn-groups .drag-nav {
  cursor: move;
}

/*背景列表的样式*/
.editor-page #bgs > li {
  width: 100% !important;
}
.editor-page #bgs > li:hover {
  border-bottom: 2px dashed #eee;
  -webkit-box-sizing: border-box;
     -moz-box-sizing: border-box;
          box-sizing: border-box;
}
.editor-page #bgs > li:hover .bg-height {
  display: inline-block;
}

/*编辑模块名字的弹框样式*/
.name-edit-pop .pop-content {
  width: 380px;
}
.name-edit-pop .middle {
  text-align: center;
  padding: 40px 16px;
  -webkit-box-sizing: border-box;
     -moz-box-sizing: border-box;
          box-sizing: border-box;
}
.name-edit-pop label {
  display: inline-block;
  margin-right: 8px;
  vertical-align: middle;
}
.name-edit-pop .inp-name {
  padding: 0 8px;
  width: 240px;
  height: 30px;
  border-radius: 2px;
  border: 1px solid rgb(221, 221, 221);
  -webkit-box-sizing: border-box;
     -moz-box-sizing: border-box;
          box-sizing: border-box;
}

/*第一屏的样式*/
#firstSection {
  position: relative;
}

#firstSection {
  -webkit-box-sizing: border-box;
     -moz-box-sizing: border-box;
          box-sizing: border-box;
}
#firstSection #banner {
  position: absolute;
  width: 100%;
  top: 0;
  left: 0;
}
#firstSection .grid-wrap {
  z-index: 100;
}
#firstSection .grid-wrap > .grid-wrap-border {
  border: 2px dotted #ccc;
  overflow: visible;
}
#firstSection .grid-wrap > .grid-wrap-border #grid1 {
  border: none;
}

.editor-page .grid-stack {
  min-height: 100px;
  border: 2px dotted #ccc;
}

.grid-stack {
  border: 2px dotted transparent;
}

/*右侧锚点导航的样式*/
#anchorsNav {
  position: fixed;
  right: 30px;
  top: 50%;
  transform: translateY(-50%);
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  z-index: 10000;
}
#anchorsNav:hover .general-set {
  display: block;
}
#anchorsNav.hide {
  display: none !important;
}
#anchorsNav .nav-name {
  position: absolute;
  display: none;
  right: 40px;
  top: 5px;
  font-size: 12px;
  color: #fff;
  padding: 6px 12px;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 4px;
  white-space: nowrap;
}
#anchorsNav .anchors-nav li {
  position: relative;
}
#anchorsNav .anchors-nav li:hover .nav-name {
  display: block;
}
#anchorsNav .anchors-nav li:not(:last-child) {
  margin-bottom: 20px;
}
#anchorsNav .anchors-nav li > a {
  display: inline-block;
  width: 36px;
  line-height: 36px;
  font-size: 16px;
  font-weight: 400;
  color: #333;
  text-align: center;
}
#anchorsNav .anchors-nav.right-nav1 .active a {
  color: #fff;
}
#anchorsNav .anchors-nav.right-nav2 .active {
  transform: scale(1.3);
  -webkit-transform: scale(1.3);
  -moz-transform: scale(1.3);
  -ms-transform: scale(1.3);
  -o-transform: scale(1.3);
}

/*设置按钮的样式*/
.general-set {
  position: absolute;
  display: none;
  top: -37px;
  right: 4px;
  padding-bottom: 8px;
  z-index: 10;
}
.general-set > div {
  display: block;
  padding: 8px;
  color: #666;
  font-size: 12px;
  z-index: 10;
  cursor: pointer;
  width: 76px;
  height: 33px;
  background: white;
  -webkit-box-shadow: 0 0 5px 0 rgba(0, 0, 0, 0.3);
  box-shadow: 0 0 5px 0 rgba(0, 0, 0, 0.3);
  border-radius: 4px;
  text-align: center;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
       box-sizing: border-box;
}
.general-set .icon-set {
  margin-right: 4px;
  font-size: 16px;
}
.general-set span {
  display: inline-block;
  vertical-align: middle;
}

/*快捷入口*/
#quickEntry {
  position: fixed;
  top: 50%;
  left: 0;
  z-index: 1001;
  transform: translateY(-50%);
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
}
#quickEntry .general-set {
  display: none;
  left: 0;
  right: auto;
}
#quickEntry:hover .general-set {
  display: block;
}
#quickEntry.screen-right {
  right: 0;
  left: auto;
}
#quickEntry.content-left {
  left: 50%;
  right: auto;
  margin-left: -600px;
}
#quickEntry.content-right {
  left: 50%;
  top: 558px;
  transform: none;
  -webkit-transform: none;
  -moz-transform: none;
  -ms-transform: none;
  -o-transform: none;
  right: auto;
  margin-left: 600px;
}

/*返回顶部*/
#toTop {
  position: fixed;
  bottom: 0;
  right: 149px;
  z-index: 999;
}
#toTop .general-set {
  display: none;
}
#toTop:hover .general-set {
  display: block;
}

@media only screen and (min-width: 1px) and (max-width: 926px) {
  .content .aid-groups {
    width: 100%;
  }
  .grid-wrap {
    width: 100%;
  }
  #firstSection {
    height: auto !important;
    min-height: 0 !important;
    z-index: 198;
  }
  #firstSection #banner {
    position: relative;
    height: auto !important;
  }
  #firstSection .grid-wrap {
    position: initial;
    -webkit-transform: none;
        -ms-transform: none;
            transform: none;
    max-height: none !important;
  }
  #anchorsNav {
    display: none !important;
  }
  #toTop {
    right: 0.2rem;
  }
  .content {
    width: 100%;
  }
  body {
    font-size: 14px;
    min-width: 100%;
    background-color: #ffffff;
  }
  #header {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 9999;
  }
  #pageTopWrap {
    min-width: 100%;
  }
  .w1180 {
    width: 100%;
  }
  .grid-stack > .grid-stack-item {
    height: auto !important;
    min-height: 0 !important;
    /*margin-bottom: .1rem;*/ /*每个模块之间的间距*/
  }
  .grid-stack > .grid-stack-item > .grid-stack-item-content {
    position: initial;
    border: none;
  }
  .layout-content {
    position: static !important;
  }
}
@media only screen and (min-width: 1px) and (max-width: 1080px) {
  .touchmachine .content .aid-groups {
    width: 100%;
  }
  .touchmachine .grid-wrap {
    width: 100%;
  }
  .touchmachine #firstSection {
    height: auto !important;
    min-height: 0 !important;
    z-index: 198;
  }
  .touchmachine #firstSection #banner {
    position: relative;
    height: auto !important;
  }
  .touchmachine #firstSection .grid-wrap {
    position: initial;
    -webkit-transform: none;
        -ms-transform: none;
            transform: none;
    max-height: none !important;
  }
  .touchmachine #anchorsNav {
    display: none !important;
  }
  .touchmachine #toTop {
    right: 0.2rem;
  }
  .touchmachine .content {
    width: 100%;
  }
  body.touchmachine {
    font-size: 14px;
    min-width: 100%;
    background-color: #ffffff;
  }
  .touchmachine #header {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 9999;
  }
  .touchmachine #pageTopWrap {
    min-width: 100%;
  }
  .touchmachine .w1180 {
    width: 100%;
  }
  .touchmachine .grid-stack > .grid-stack-item {
    height: auto !important;
    min-height: 0 !important;
    /*margin-bottom: .1rem;*/ /*每个模块之间的间距*/
  }
  .touchmachine .grid-stack > .grid-stack-item > .grid-stack-item-content {
    position: initial;
    border: none;
  }
  .touchmachine .layout-content {
    position: static !important;
  }
}