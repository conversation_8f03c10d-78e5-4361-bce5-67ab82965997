.admin-container {
  background: -webkit-gradient(linear, left top, left bottom, from(#E8EEFF), color-stop(42.79%, #EBF8FF), to(#F0F3FF));
  background: -webkit-linear-gradient(top, #E8EEFF 0%, #EBF8FF 42.79%, #F0F3FF 100%);
  background: linear-gradient(180deg, #E8EEFF 0%, #EBF8FF 42.79%, #F0F3FF 100%);
  height: 100vh;
}
.admin-container .homer-header {
  height: 60px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
     -moz-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
     -moz-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  background-color: #FFF;
  padding: 0 20px;
}
.admin-container .homer-header h3 {
  padding: 14px 0;
  font-size: 22px;
  font-weight: 600;
  color: rgba(0, 0, 0, 0.8);
}
.admin-container .homer-header .right-operate {
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
     -moz-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
.admin-container .homer-header .right-operate .ro-btn {
  cursor: pointer;
  margin-left: 16px;
  font-size: 13px;
  line-height: 18px;
  color: rgb(43, 103, 255);
  padding: 7px 14px;
  border-radius: 4px;
  border: 1px solid rgb(43, 103, 255);
}
.admin-container .homer-header .right-operate .link {
  color: rgba(0, 0, 0, 0.8);
  margin-left: 16px;
  font-size: 13px;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -moz-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
     -moz-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  cursor: pointer;
}
.admin-container .homer-header .right-operate .link i {
  margin-right: 4px;
}
.admin-container .homer-header .right-operate .link:hover {
  color: rgb(43, 103, 255);
}
.admin-container .home-content {
  height: calc(100vh - 60px);
  font-size: 0;
  letter-spacing: 0;
}
.admin-container .home-content .home-left-nav {
  padding: 8px;
  display: inline-block;
  vertical-align: top;
  height: 100%;
  width: 204px;
  overflow-y: auto;
  -webkit-box-sizing: border-box;
     -moz-box-sizing: border-box;
          box-sizing: border-box;
}
.admin-container .home-content .home-left-nav .nav-list .nav-item .one-nav-item {
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
     -moz-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  cursor: pointer;
  font-size: 15px;
  font-weight: 500;
  line-height: 20px;
  color: rgba(0, 0, 0, 0.8);
  padding: 10px 8px 10px 12px;
  border-radius: 8px;
  position: relative;
}
.admin-container .home-content .home-left-nav .nav-list .nav-item .one-nav-item.active2 {
  background-color: rgb(255, 255, 255);
  color: rgb(43, 103, 255);
}
.admin-container .home-content .home-left-nav .nav-list .nav-item .one-nav-item.active .arrow-right {
  -webkit-transform: rotate(0deg);
      -ms-transform: rotate(0deg);
          transform: rotate(0deg);
}
.admin-container .home-content .home-left-nav .nav-list .nav-item .one-nav-item .arrow-right {
  display: inline-block;
  width: 14px;
  height: 14px;
  margin-right: 4px;
  background: url("../../images/manage_admin/arrow-right.png") no-repeat center center;
  background-size: contain;
  position: absolute;
  right: 0;
  -webkit-transform: rotate(180deg);
      -ms-transform: rotate(180deg);
          transform: rotate(180deg);
}
.admin-container .home-content .home-left-nav .nav-list .nav-item .one-nav-item .setting-icon {
  display: inline-block;
  width: 18px;
  height: 18px;
  margin-right: 4px;
  background: url("../../images/manage_admin/backstage.png") no-repeat center center;
  background-size: contain;
}
.admin-container .home-content .home-left-nav .nav-list .nav-item .one-nav-item .setting-icon1 {
  display: inline-block;
  width: 18px;
  height: 18px;
  margin-right: 4px;
  background: url("../../images/manage_admin/backstage1.png") no-repeat center center;
  background-size: contain;
}
.admin-container .home-content .home-left-nav .nav-list .nav-item .one-nav-item .p-set {
  margin-left: auto;
  color: #2B67FF;
  font-style: normal;
  font-weight: 400;
}
.admin-container .home-content .home-left-nav .nav-list .nav-item .one-nav-item .icon-nav-item {
  font-size: 18px;
  margin-right: 4px;
}
.admin-container .home-content .home-left-nav .nav-list .nav-item .two-nav-list .two-nav-item {
  text-align: left;
  cursor: pointer;
  padding: 10px 8px 10px 34px;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.8);
  line-height: 20px;
  border-radius: 8px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
     -moz-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
.admin-container .home-content .home-left-nav .nav-list .nav-item .two-nav-list .two-nav-item.p-font {
  font-weight: 500;
  font-size: 15px;
}
.admin-container .home-content .home-left-nav .nav-list .nav-item .two-nav-list .two-nav-item:hover {
  background-color: rgba(0, 0, 0, 0.04);
}
.admin-container .home-content .home-left-nav .nav-list .nav-item .two-nav-list .two-nav-item.active {
  background-color: rgb(255, 255, 255);
  color: rgb(43, 103, 255);
}
.admin-container .home-content .home-left-nav .nav-list .nav-item .two-nav-list .two-nav-item .iconW {
  font-size: 18px;
  margin-right: 4px;
}
.admin-container .home-content .home-left-nav .nav-list .nav-item .two-nav-list .selcet-div {
  padding: 8px 12px 8px 30px;
}
.admin-container .home-content .home-left-nav .nav-list .nav-item .two-nav-list .selcet-div .el-input__inner {
  background: none;
  border: 1px solid rgba(0, 0, 0, 0.14);
}
.admin-container .home-content .home-iframe {
  padding: 8px 8px 8px 0;
  display: inline-block;
  vertical-align: top;
  width: calc(100% - 220px);
  -webkit-box-sizing: border-box;
     -moz-box-sizing: border-box;
          box-sizing: border-box;
  border-radius: 8px;
}
.admin-container .home-content .home-iframe.has-nav .iframe-content {
  height: calc(100vh - 112px);
  border-radius: 0 8px 8px 8px;
  overflow: hidden;
}
.admin-container .home-content .home-iframe .top-nav {
  width: 100%;
  overflow-x: auto;
  text-align: left;
}
.admin-container .home-content .home-iframe .top-nav .top-nav-item {
  cursor: pointer;
  position: relative;
  display: inline-block;
  vertical-align: top;
  -webkit-box-shadow: 2px 0px 8px 0px rgba(0, 0, 0, 0.06);
          box-shadow: 2px 0px 8px 0px rgba(0, 0, 0, 0.06);
  background: rgb(245, 247, 252);
  border-radius: 8px 8px 0 0;
  padding: 8px 8px 8px 11px;
  width: 160px;
  height: 32px;
  -webkit-box-sizing: border-box;
     -moz-box-sizing: border-box;
          box-sizing: border-box;
}
.admin-container .home-content .home-iframe .top-nav .top-nav-item.active {
  background: rgb(255, 255, 255);
}
.admin-container .home-content .home-iframe .top-nav .top-nav-item .nav-txt {
  display: inline-block;
  vertical-align: top;
  width: 96px;
  font-size: 14px;
  line-height: 20px;
  color: rgba(0, 0, 0, 0.6);
}
.admin-container .home-content .home-iframe .top-nav .close-btn {
  position: absolute;
  right: 8px;
  top: 50%;
  -webkit-transform: translateY(-50%);
      -ms-transform: translateY(-50%);
          transform: translateY(-50%);
  cursor: pointer;
  display: inline-block;
  vertical-align: top;
  width: 14px;
  height: 14px;
  background: url("../../images/manage_admin/close-small.png") no-repeat center center;
  background-size: contain;
}
.admin-container .home-content .home-iframe .top-nav .el-tabs {
  background: none;
}
.admin-container .home-content .home-iframe .top-nav .el-tabs .el-tabs__nav-wrap {
  margin-bottom: 0;
}
.admin-container .home-content .home-iframe .top-nav .el-tabs .el-tabs__nav-wrap.is-scrollable {
  padding: 0;
  border-radius: 8px 8px 0 0;
}
.admin-container .home-content .home-iframe .top-nav .el-tabs .el-tabs__nav-wrap .el-tabs__nav-prev, .admin-container .home-content .home-iframe .top-nav .el-tabs .el-tabs__nav-wrap .el-tabs__nav-next {
  width: 60px;
  height: 36px;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -moz-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
     -moz-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  position: absolute;
  top: 50%;
  -webkit-transform: translateY(-50%);
      -ms-transform: translateY(-50%);
          transform: translateY(-50%);
  z-index: 10;
  background: #fff;
  border-radius: 4px;
}
.admin-container .home-content .home-iframe .top-nav .el-tabs .el-tabs__nav-wrap .el-tabs__nav-prev {
  left: 0;
  background: -webkit-gradient(linear, right top, left top, from(rgba(255, 255, 255, 0)), color-stop(58.72%, #FFFFFF), to(#FFFFFF));
  background: -webkit-linear-gradient(right, rgba(255, 255, 255, 0) 0%, #FFFFFF 58.72%, #FFFFFF 100%);
  background: linear-gradient(270deg, rgba(255, 255, 255, 0) 0%, #FFFFFF 58.72%, #FFFFFF 100%);
}
.admin-container .home-content .home-iframe .top-nav .el-tabs .el-tabs__nav-wrap .el-tabs__nav-next {
  right: 0;
  -webkit-box-pack: right;
  -webkit-justify-content: right;
     -moz-box-pack: right;
      -ms-flex-pack: right;
          justify-content: right;
  background: -webkit-gradient(linear, left top, right top, from(rgba(255, 255, 255, 0)), color-stop(58.72%, #FFFFFF), to(#FFFFFF));
  background: -webkit-linear-gradient(left, rgba(255, 255, 255, 0) 0%, #FFFFFF 58.72%, #FFFFFF 100%);
  background: linear-gradient(90deg, rgba(255, 255, 255, 0) 0%, #FFFFFF 58.72%, #FFFFFF 100%);
}
.admin-container .home-content .home-iframe .top-nav .el-tabs .el-tabs__nav-wrap .el-icon-arrow-left:before, .admin-container .home-content .home-iframe .top-nav .el-tabs .el-tabs__nav-wrap .el-icon-arrow-right:before {
  content: "";
}
.admin-container .home-content .home-iframe .top-nav .el-tabs .el-tabs__nav-wrap .el-icon-arrow-left {
  display: inline-block;
  width: 24px;
  height: 24px;
  background: url("../../images/manage_admin/triangle-left-one.png") no-repeat;
  background-size: 100%;
  border-radius: 4px;
}
.admin-container .home-content .home-iframe .top-nav .el-tabs .el-tabs__nav-wrap .el-icon-arrow-left:hover {
  background: #F5F5F5 url("../../images/manage_admin/triangle-left-one.png") no-repeat;
}
.admin-container .home-content .home-iframe .top-nav .el-tabs .el-tabs__nav-wrap .el-icon-arrow-right {
  display: inline-block;
  width: 24px;
  height: 24px;
  background: url("../../images/manage_admin/triangle-right-one.png") no-repeat;
  background-size: 100%;
  border-radius: 4px;
}
.admin-container .home-content .home-iframe .top-nav .el-tabs .el-tabs__nav-wrap .el-icon-arrow-right:hover {
  background: #F5F5F5 url("../../images/manage_admin/triangle-right-one.png") no-repeat;
}
.admin-container .home-content .home-iframe .top-nav .el-tabs .el-tabs__nav-next, .admin-container .home-content .home-iframe .top-nav .el-tabs .el-tabs__nav-prev {
  line-height: 36px;
}
.admin-container .home-content .home-iframe .top-nav .el-tabs .el-tabs__header {
  margin-bottom: 0;
}
.admin-container .home-content .home-iframe .top-nav .el-tabs .el-tabs__active-bar {
  display: none;
}
.admin-container .home-content .home-iframe .top-nav .el-tabs .el-tabs__item {
  cursor: pointer;
  position: relative;
  vertical-align: top;
  -webkit-box-shadow: 2px 0px 8px 0px rgba(0, 0, 0, 0.06);
          box-shadow: 2px 0px 8px 0px rgba(0, 0, 0, 0.06);
  background: rgb(245, 247, 252);
  border-radius: 8px 8px 0 0;
  padding: 0 8px 0 16px;
  width: 160px;
  height: 36px;
  -webkit-box-sizing: border-box;
     -moz-box-sizing: border-box;
          box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -moz-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
     -moz-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
.admin-container .home-content .home-iframe .top-nav .el-tabs .el-tabs__item .el-icon-close {
  margin-left: 4px;
}
.admin-container .home-content .home-iframe .top-nav .el-tabs .el-tabs__item .icon-exchange {
  font-size: 11px;
  margin-left: auto;
}
.admin-container .home-content .home-iframe .top-nav .el-tabs .el-tabs__item.is-active {
  color: rgba(0, 0, 0, 0.8);
  background: rgb(255, 255, 255);
}
.admin-container .home-content .home-iframe .iframe-content {
  width: 100%;
  height: calc(100vh - 76px);
}
.admin-container .home-content .home-iframe .iframe-content iframe {
  width: 100%;
  height: 100%;
}
