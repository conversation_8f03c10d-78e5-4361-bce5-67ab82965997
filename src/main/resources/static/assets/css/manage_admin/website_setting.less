.website-setting {
    background-color: #FFF;
    // border-radius: 8px;
    padding: 24px 32px;
    box-sizing: border-box;
    height: 100vh;
    text-align: left;
    .setting-form {
        .inner-title{
           font-size: 15px;
            font-weight: 600;
            margin-bottom: 20px;
        }
        .setting-form-item {
            margin-bottom: 16px;
            display: flex;
            text-align: left;
            .setting-form-item-label {
                margin-right: 24px;
                width: 127px;
                font-size: 14px;
                color: rgba(51, 51, 51, 1);
                line-height: 32px;
                display: flex;
                align-items: flex-start;
                i{
                    font-size: 14px;
                    margin-left: 4px;
                    color: rgba(0, 0, 0, 0.4);
                    cursor: pointer;
                    line-height: 32px;
                    &:hover {
                        color: #2B67FF;
                    }
                }

            }
            .setting-form-item-content {
                flex: 1;
                .website-name {
                    font-size: 14px;
                    line-height: 20px;
                    padding: 6px 12px;
                    color: rgba(0, 0, 0, 0.8);
                    border: 1px solid rgba(233, 233, 233, 1);
                    border-radius: 4px;
                    width: 500px;
                    box-sizing: border-box;
                    &:focus {
                        border: 1px solid rgba(43, 103, 255, 1);
                    }
                }
                .logo-content {
                    .logo-box {
                        margin-right: 16px;
                        display: inline-block;
                        vertical-align: top;
                        text-align: left;
                        &.type2 {
                            .logo-upload-box {
                                .logo-img {
                                    width: 160px;
                                    height: 90px;
                                    &.failure-box {
                                        padding: 10px 0;
                                        .fail-content  {
                                            p {
                                                margin-bottom: 8px;
                                            }
                                        }
                                    }
                                }
                                .right-upload-btn {
                                    .upload-btn {
                                        margin-bottom: 20px;
                                        margin-top: 10px;
                                    }
                                }
                            }
                        }
                        .logo-txt {
                            font-size: 14px;
                            line-height: 20px;
                            color: rgba(0, 0, 0, 0.8);
                        }
                        .logo-upload-box {
                            margin-top: 4px;
                            .logo-img {
                                position: relative;
                                display: inline-block;
                                vertical-align: top;
                                width: 72px;
                                height: 72px;
                                box-sizing: border-box;
                                border: 1px solid rgba(233, 233, 233, 1);
                                overflow: hidden;
                                border-radius: 4px;
                                background: url("../../images/manage_admin/transparent.png") repeat left top;
                                .imgSrc {
                                    max-width: 100%;
                                    max-height: 100%;
                                    position: absolute;
                                    top: 50%;
                                    left: 50%;
                                    transform: translate(-50%,-50%);
                                }
                                &:hover {
                                    .delete-btn {
                                        display: inline-block;
                                    }
                                }
                                &.failure-box {
                                    border: 1px solid rgba(245, 63, 63, 1);
                                    background: none;
                                    background-color: rgba(255, 78, 78, 0.08);
                                    .fail-content {
                                        padding: 6px 0;
                                        text-align: center;
                                        img {
                                            display: block;
                                            margin: 0 auto;
                                            width: 16px;
                                            height: 16px;
                                        }
                                        p {
                                            margin: 2px 0 4px;
                                            font-size: 14px;
                                            color: rgba(245, 63, 63, 1);
                                        }
                                        .retry-btn {
                                            cursor: pointer;
                                            font-size: 14px;
                                            color: rgba(43, 103, 255, 1);
                                        }
                                    }
                                }
                                .delete-btn {
                                    display: none;
                                    cursor: pointer;
                                    position: absolute;
                                    right: 4px;
                                    bottom: 4px;
                                    background: rgba(0, 0, 0, 0.6);
                                    padding: 3px;
                                    border-radius: 4px;
                                    width: 18px;
                                    height: 18px;
                                    box-sizing: border-box;
                                    span {
                                        display: inline-block;
                                        vertical-align: top;
                                        width: 12px;
                                        height: 12px;
                                        background: url("../../images/manage_admin/delete.png") no-repeat center center;
                                        background-size: contain;
                                    }
                                }
                            }
                            .right-upload-btn {
                                margin-left: 8px;
                                display: inline-block;
                                vertical-align: top;
                                text-align: left;
                                .el-upload {
                                    text-align: left;
                                }
                                p {
                                    font-size: 12px;
                                    line-height: 17px;
                                    color: rgba(0, 0, 0, 0.4);
                                    text-align: left;
                                }
                                .upload-btn {
                                    margin-top: 12px;
                                    cursor: pointer;
                                    display: inline-block;
                                    vertical-align: top;
                                    border: 1px solid rgba(43, 103, 255, 1);
                                    border-radius: 4px;
                                    font-size: 14px;
                                    line-height: 22px;
                                    padding: 0 6px;
                                    color: rgba(43, 103, 255, 1);
                                }
                            }
                        }
                    }
                }
                textarea {
                    width: 500px;
                    height: 56px;
                    resize: none;
                    border: 1px solid rgba(233, 233, 233, 1);
                    border-radius: 4px;
                    padding: 8px 12px;
                    box-sizing: border-box;
                    font-size: 14px;
                    line-height: 20px;
                    color: rgba(51, 51, 51, 1);
                    outline: none;
                    &:focus {
                        border: 1px solid rgba(43, 103, 255, 1);
                    }
                }
            }
        }
    }
    .save-btn {
        cursor: pointer;
        margin-top: 40px;
        line-height: 32px;
        border-radius: 4px;
        padding: 0 16px;
        display: inline-block;
        font-size: 14px;
        color: #FFF;
        background-color: rgba(43, 103, 255, 1);
        &:hover {
            background-color: rgba(43, 103, 255, 0.8);
        }
    }
}
.el-tooltip__popper{
    &.item-tips{
        padding: 8px 12px;
        text-align: left;
        p{
            font-size: 13px;
        }
        img{
            margin-top: 8px;
        }
    }
}
[v-cloak] {
    display: none;
}


.resource-pool-pop{
    border-radius: 8px;
    box-shadow: 0px 6px 24px 0px rgba(31, 35, 41, 0.1);

    .el-dialog__header {
        text-align: left;
        padding: 16px 54px 16px 24px;
        font-size: 16px;
        color: rgba(0, 0, 0, 0.8);
        font-weight: 600;
        border-bottom: 1px solid #E9E9E9;
    }
    .el-dialog__body{
        padding: 0;
    }
    .pop-content{
        display: flex;
        .left-box{
            width:176px;
            padding: 16px;
            box-sizing: border-box;
            border-right: 1px solid #F0F0F0;
            .tabs-box{
                &>li{
                    padding: 0 12px 0 16px;
                    height: 40px;
                    line-height: 40px;
                    box-sizing: border-box;
                    text-align: left;
                    font-size: 14px;
                    color: rgba(0, 0, 0, 0.8);
                    font-weight: 500;
                    margin-bottom: 4px;
                    cursor: pointer;
                    &.current{
                        border-radius: 8px;
                        background: #E8F2FF;
                        color: #2B67FF;
                        font-weight: 600;
                    }
                }
            }
        }
        .right-box{
            flex:1;
            height: 545px;
            overflow-y: auto;
            padding: 16px;
            box-sizing: border-box;
            background: #F7F8FA;
            .loading-div {
                width: 100%;
                height: 100%;
                position: relative;
                img{
                    position: absolute;
                    left: 50%;
                    top: 50%;
                    transform: translate(-50%, -50%);
                }
            }
            .video-resource-box{
                & > li {
                    display: inline-block;
                    vertical-align: top;
                    width: calc((100% - 32px) / 3);
                    margin-right: 16px;
                    margin-bottom: 16px;
                    cursor: pointer;

                    &:nth-of-type(3n){
                        margin-right: 0;
                    }
                    &:hover{
                        .img-box{
                            .cover-img{
                                display: none;
                            }
                            .hover-img{
                                display: block;
                            }
                        }
                        .name{
                            color: #2B67FF;
                        }

                    }
                    .img-box{
                        width: 100%;
                        height: 142px;
                        overflow: hidden;
                        border-radius: 8px;
                        .hover-img{
                            width: 100%;
                            height: 100%;
                            display: none;
                        }
                    }
                    .name{
                        font-size: 13px;
                        color: rgba(0, 0, 0, 0.8);
                        line-height: 16px;
                        margin-top: 6px;
                    }
                }
            }
        }
    }
}
