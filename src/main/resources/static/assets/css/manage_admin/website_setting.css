.website-setting {
  background-color: #FFF;
  padding: 24px 32px;
  -webkit-box-sizing: border-box;
     -moz-box-sizing: border-box;
          box-sizing: border-box;
  height: 100vh;
  text-align: left;
}
.website-setting .setting-form .inner-title {
  font-size: 15px;
  font-weight: 600;
  margin-bottom: 20px;
}
.website-setting .setting-form .setting-form-item {
  margin-bottom: 16px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-box;
  display: -ms-flexbox;
  display: flex;
  text-align: left;
}
.website-setting .setting-form .setting-form-item .setting-form-item-label {
  margin-right: 24px;
  width: 127px;
  font-size: 14px;
  color: rgb(51, 51, 51);
  line-height: 32px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -webkit-align-items: flex-start;
     -moz-box-align: start;
      -ms-flex-align: start;
          align-items: flex-start;
}
.website-setting .setting-form .setting-form-item .setting-form-item-label i {
  font-size: 14px;
  margin-left: 4px;
  color: rgba(0, 0, 0, 0.4);
  cursor: pointer;
  line-height: 32px;
}
.website-setting .setting-form .setting-form-item .setting-form-item-label i:hover {
  color: #2B67FF;
}
.website-setting .setting-form .setting-form-item .setting-form-item-content {
  -webkit-box-flex: 1;
  -webkit-flex: 1;
     -moz-box-flex: 1;
      -ms-flex: 1;
          flex: 1;
}
.website-setting .setting-form .setting-form-item .setting-form-item-content .website-name {
  font-size: 14px;
  line-height: 20px;
  padding: 6px 12px;
  color: rgba(0, 0, 0, 0.8);
  border: 1px solid rgb(233, 233, 233);
  border-radius: 4px;
  width: 500px;
  -webkit-box-sizing: border-box;
     -moz-box-sizing: border-box;
          box-sizing: border-box;
}
.website-setting .setting-form .setting-form-item .setting-form-item-content .website-name:focus {
  border: 1px solid rgb(43, 103, 255);
}
.website-setting .setting-form .setting-form-item .setting-form-item-content .logo-content .logo-box {
  margin-right: 16px;
  display: inline-block;
  vertical-align: top;
  text-align: left;
}
.website-setting .setting-form .setting-form-item .setting-form-item-content .logo-content .logo-box.type2 .logo-upload-box .logo-img {
  width: 160px;
  height: 90px;
}
.website-setting .setting-form .setting-form-item .setting-form-item-content .logo-content .logo-box.type2 .logo-upload-box .logo-img.failure-box {
  padding: 10px 0;
}
.website-setting .setting-form .setting-form-item .setting-form-item-content .logo-content .logo-box.type2 .logo-upload-box .logo-img.failure-box .fail-content p {
  margin-bottom: 8px;
}
.website-setting .setting-form .setting-form-item .setting-form-item-content .logo-content .logo-box.type2 .logo-upload-box .right-upload-btn .upload-btn {
  margin-bottom: 20px;
  margin-top: 10px;
}
.website-setting .setting-form .setting-form-item .setting-form-item-content .logo-content .logo-box .logo-txt {
  font-size: 14px;
  line-height: 20px;
  color: rgba(0, 0, 0, 0.8);
}
.website-setting .setting-form .setting-form-item .setting-form-item-content .logo-content .logo-box .logo-upload-box {
  margin-top: 4px;
}
.website-setting .setting-form .setting-form-item .setting-form-item-content .logo-content .logo-box .logo-upload-box .logo-img {
  position: relative;
  display: inline-block;
  vertical-align: top;
  width: 72px;
  height: 72px;
  -webkit-box-sizing: border-box;
     -moz-box-sizing: border-box;
          box-sizing: border-box;
  border: 1px solid rgb(233, 233, 233);
  overflow: hidden;
  border-radius: 4px;
  background: url("../../images/manage_admin/transparent.png") repeat left top;
}
.website-setting .setting-form .setting-form-item .setting-form-item-content .logo-content .logo-box .logo-upload-box .logo-img .imgSrc {
  max-width: 100%;
  max-height: 100%;
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
      -ms-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
}
.website-setting .setting-form .setting-form-item .setting-form-item-content .logo-content .logo-box .logo-upload-box .logo-img:hover .delete-btn {
  display: inline-block;
}
.website-setting .setting-form .setting-form-item .setting-form-item-content .logo-content .logo-box .logo-upload-box .logo-img.failure-box {
  border: 1px solid rgb(245, 63, 63);
  background: none;
  background-color: rgba(255, 78, 78, 0.08);
}
.website-setting .setting-form .setting-form-item .setting-form-item-content .logo-content .logo-box .logo-upload-box .logo-img.failure-box .fail-content {
  padding: 6px 0;
  text-align: center;
}
.website-setting .setting-form .setting-form-item .setting-form-item-content .logo-content .logo-box .logo-upload-box .logo-img.failure-box .fail-content img {
  display: block;
  margin: 0 auto;
  width: 16px;
  height: 16px;
}
.website-setting .setting-form .setting-form-item .setting-form-item-content .logo-content .logo-box .logo-upload-box .logo-img.failure-box .fail-content p {
  margin: 2px 0 4px;
  font-size: 14px;
  color: rgb(245, 63, 63);
}
.website-setting .setting-form .setting-form-item .setting-form-item-content .logo-content .logo-box .logo-upload-box .logo-img.failure-box .fail-content .retry-btn {
  cursor: pointer;
  font-size: 14px;
  color: rgb(43, 103, 255);
}
.website-setting .setting-form .setting-form-item .setting-form-item-content .logo-content .logo-box .logo-upload-box .logo-img .delete-btn {
  display: none;
  cursor: pointer;
  position: absolute;
  right: 4px;
  bottom: 4px;
  background: rgba(0, 0, 0, 0.6);
  padding: 3px;
  border-radius: 4px;
  width: 18px;
  height: 18px;
  -webkit-box-sizing: border-box;
     -moz-box-sizing: border-box;
          box-sizing: border-box;
}
.website-setting .setting-form .setting-form-item .setting-form-item-content .logo-content .logo-box .logo-upload-box .logo-img .delete-btn span {
  display: inline-block;
  vertical-align: top;
  width: 12px;
  height: 12px;
  background: url("../../images/manage_admin/delete.png") no-repeat center center;
  background-size: contain;
}
.website-setting .setting-form .setting-form-item .setting-form-item-content .logo-content .logo-box .logo-upload-box .right-upload-btn {
  margin-left: 8px;
  display: inline-block;
  vertical-align: top;
  text-align: left;
}
.website-setting .setting-form .setting-form-item .setting-form-item-content .logo-content .logo-box .logo-upload-box .right-upload-btn .el-upload {
  text-align: left;
}
.website-setting .setting-form .setting-form-item .setting-form-item-content .logo-content .logo-box .logo-upload-box .right-upload-btn p {
  font-size: 12px;
  line-height: 17px;
  color: rgba(0, 0, 0, 0.4);
  text-align: left;
}
.website-setting .setting-form .setting-form-item .setting-form-item-content .logo-content .logo-box .logo-upload-box .right-upload-btn .upload-btn {
  margin-top: 12px;
  cursor: pointer;
  display: inline-block;
  vertical-align: top;
  border: 1px solid rgb(43, 103, 255);
  border-radius: 4px;
  font-size: 14px;
  line-height: 22px;
  padding: 0 6px;
  color: rgb(43, 103, 255);
}
.website-setting .setting-form .setting-form-item .setting-form-item-content textarea {
  width: 500px;
  height: 56px;
  resize: none;
  border: 1px solid rgb(233, 233, 233);
  border-radius: 4px;
  padding: 8px 12px;
  -webkit-box-sizing: border-box;
     -moz-box-sizing: border-box;
          box-sizing: border-box;
  font-size: 14px;
  line-height: 20px;
  color: rgb(51, 51, 51);
  outline: none;
}
.website-setting .setting-form .setting-form-item .setting-form-item-content textarea:focus {
  border: 1px solid rgb(43, 103, 255);
}
.website-setting .save-btn {
  cursor: pointer;
  margin-top: 40px;
  line-height: 32px;
  border-radius: 4px;
  padding: 0 16px;
  display: inline-block;
  font-size: 14px;
  color: #FFF;
  background-color: rgb(43, 103, 255);
}
.website-setting .save-btn:hover {
  background-color: rgba(43, 103, 255, 0.8);
}

.el-tooltip__popper.item-tips {
  padding: 8px 12px;
  text-align: left;
}
.el-tooltip__popper.item-tips p {
  font-size: 13px;
}
.el-tooltip__popper.item-tips img {
  margin-top: 8px;
}

[v-cloak] {
  display: none;
}

.resource-pool-pop {
  border-radius: 8px;
  -webkit-box-shadow: 0px 6px 24px 0px rgba(31, 35, 41, 0.1);
          box-shadow: 0px 6px 24px 0px rgba(31, 35, 41, 0.1);
}
.resource-pool-pop .el-dialog__header {
  text-align: left;
  padding: 16px 54px 16px 24px;
  font-size: 16px;
  color: rgba(0, 0, 0, 0.8);
  font-weight: 600;
  border-bottom: 1px solid #E9E9E9;
}
.resource-pool-pop .el-dialog__body {
  padding: 0;
}
.resource-pool-pop .pop-content {
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-box;
  display: -ms-flexbox;
  display: flex;
}
.resource-pool-pop .pop-content .left-box {
  width: 176px;
  padding: 16px;
  -webkit-box-sizing: border-box;
     -moz-box-sizing: border-box;
          box-sizing: border-box;
  border-right: 1px solid #F0F0F0;
}
.resource-pool-pop .pop-content .left-box .tabs-box > li {
  padding: 0 12px 0 16px;
  height: 40px;
  line-height: 40px;
  -webkit-box-sizing: border-box;
     -moz-box-sizing: border-box;
          box-sizing: border-box;
  text-align: left;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.8);
  font-weight: 500;
  margin-bottom: 4px;
  cursor: pointer;
}
.resource-pool-pop .pop-content .left-box .tabs-box > li.current {
  border-radius: 8px;
  background: #E8F2FF;
  color: #2B67FF;
  font-weight: 600;
}
.resource-pool-pop .pop-content .right-box {
  -webkit-box-flex: 1;
  -webkit-flex: 1;
     -moz-box-flex: 1;
      -ms-flex: 1;
          flex: 1;
  height: 545px;
  overflow-y: auto;
  padding: 16px;
  -webkit-box-sizing: border-box;
     -moz-box-sizing: border-box;
          box-sizing: border-box;
  background: #F7F8FA;
}
.resource-pool-pop .pop-content .right-box .loading-div {
  width: 100%;
  height: 100%;
  position: relative;
}
.resource-pool-pop .pop-content .right-box .loading-div img {
  position: absolute;
  left: 50%;
  top: 50%;
  -webkit-transform: translate(-50%, -50%);
      -ms-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
}
.resource-pool-pop .pop-content .right-box .video-resource-box > li {
  display: inline-block;
  vertical-align: top;
  width: calc((100% - 32px) / 3);
  margin-right: 16px;
  margin-bottom: 16px;
  cursor: pointer;
}
.resource-pool-pop .pop-content .right-box .video-resource-box > li:nth-of-type(3n) {
  margin-right: 0;
}
.resource-pool-pop .pop-content .right-box .video-resource-box > li:hover .img-box .cover-img {
  display: none;
}
.resource-pool-pop .pop-content .right-box .video-resource-box > li:hover .img-box .hover-img {
  display: block;
}
.resource-pool-pop .pop-content .right-box .video-resource-box > li:hover .name {
  color: #2B67FF;
}
.resource-pool-pop .pop-content .right-box .video-resource-box > li .img-box {
  width: 100%;
  height: 142px;
  overflow: hidden;
  border-radius: 8px;
}
.resource-pool-pop .pop-content .right-box .video-resource-box > li .img-box .hover-img {
  width: 100%;
  height: 100%;
  display: none;
}
.resource-pool-pop .pop-content .right-box .video-resource-box > li .name {
  font-size: 13px;
  color: rgba(0, 0, 0, 0.8);
  line-height: 16px;
  margin-top: 6px;
}