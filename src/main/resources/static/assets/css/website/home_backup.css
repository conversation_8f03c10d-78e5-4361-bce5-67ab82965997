@charset "UTF-8";
body {
  background: #F5F5F5;
}

.w-1200 {
  width: 1200px;
  margin: 0 auto;
}

.page-home .banner-box {
  width: 100%;
  position: relative;
}
.page-home .banner-box img {
  width: 100%;
}
.page-home .banner-box .top-btns {
  position: absolute;
  right: 24px;
  top: 20px;
}
.page-home .banner-box .top-btns .btn {
  display: inline-block;
  height: 24px;
  line-height: 24px;
  font-size: 16px;
  color: #FFFFFF;
  padding: 0 12px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  margin-right: 20px;
}
.page-home .banner-box .btn-back-old {
  font-size: 12px;
  color: #FFFFFF;
  line-height: 24px;
}
.page-home .banner-box .version-box {
  position: absolute;
  right: 24px;
  bottom: 12px;
  font-size: 12px;
  color: #ffffff;
}
.page-home .banner-box .version-box .btn {
  display: inline-block;
  height: 18px;
  line-height: 18px;
  font-size: 12px;
  color: #FFFFFF;
  padding: 0 6px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  margin-left: 8px;
}
.page-home .ui-btn-add {
  display: inline-block;
  border: 2px solid #3D82F2;
  width: 170px;
  height: 76px;
  line-height: 76px;
  text-align: center;
  border-radius: 2px;
  font-size: 40px;
  color: #3D82F2;
}
.page-home .add-tip {
  font-size: 14px;
  color: #333333;
  margin-left: 16px;
  font-weight: 400;
  vertical-align: bottom;
}
.page-home .temp-list-box {
  width: 1200px;
  margin: 0 auto 50px;
}
.page-home .temp-list-box .btns {
  text-align: left;
  padding: 24px 0;
  position: relative;
}
.page-home .temp-list-box .btns .ui-txt-blue {
  font-size: 16px;
  color: #3D82F2;
  position: absolute;
  bottom: 24px;
  right: 0;
}
.page-home .temp-list-box .btns .recycleBin {
  font-size: 16px;
  color: #333333;
  position: absolute;
  bottom: 24px;
  right: 0;
  display: inline-block;
}
.page-home .temp-list-box .btns .recycleBin i {
  width: 24px;
  height: 24px;
  margin-right: 8px;
}
.page-home .temp-list-box .btns .recycleBin i:before {
  color: #CCCCCC;
}
.page-home .temp-list-box .btns .recycleBin:hover {
  color: #3D82F2;
}
.page-home .temp-list-box .btns .recycleBin:hover i:before {
  color: #3d82f2;
}
.page-home .temp-list-box .ui-btn-blue {
  display: inline-block;
  height: 40px;
  border: 1px solid #3D82F2;
  padding: 0 16px;
  border-radius: 2px;
  color: #3D82F2;
  font-size: 16px;
  line-height: 40px;
  margin-right: 16px;
}
.page-home .temp-list-box .list > li {
  background: #ffffff;
  margin-bottom: 24px;
  padding: 22px 24px;
  position: relative;
}
.page-home .temp-list-box .list > li:hover {
  -webkit-box-shadow: 0px 2px 12px 0px rgba(0, 0, 0, 0.09);
  box-shadow: 0px 2px 12px 0px rgba(0, 0, 0, 0.09);
}
.page-home .temp-list-box .list > li:hover .btn-delete {
  display: block;
}
.page-home .temp-list-box .list > li:hover .ui-btn-min {
  display: inline-block;
}
.page-home .temp-list-box .list .btn-delete {
  display: none;
  position: absolute;
  right: 0;
  top: 0;
  width: 30px;
  height: 30px;
  text-align: center;
  line-height: 30px;
  background: #F6F6F6;
  font-size: 20px;
  cursor: pointer;
}
.page-home .temp-list-box .list .btn-delete:before {
  color: #CCCCCC;
}
.page-home .temp-list-box .list .img-box {
  width: 320px;
  height: 200px;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  overflow: hidden;
  display: inline-block;
  vertical-align: middle;
  margin-right: 32px;
}
.page-home .temp-list-box .list .img-box img {
  width: 100%;
  vertical-align: middle;
}
.page-home .temp-list-box .list .right-btns {
  position: absolute;
  right: 24px;
  top: 50%;
  transform: translateY(-50%);
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
}
.page-home .temp-list-box .list .right-btns .btn-right-edit {
  display: block;
  width: 72px;
  height: 30px;
  line-height: 30px;
  border-radius: 2px;
  background: #EEEEEE;
  font-size: 16px;
  color: #333333;
  text-align: center;
  margin-bottom: 16px;
}
.page-home .temp-list-box .list .right-btns .btn-right-edit:last-child {
  margin-bottom: 0;
}
.page-home .temp-list-box .list .right-btns .btn-right-edit:hover {
  background: #3D82F2;
  color: #ffffff;
}
.page-home .temp-list-box .list .right-btns .btn-manage {
  background: #3D82F2;
  color: #ffffff;
}
.page-home .temp-list-box .list .right-btns .btn-manage:hover {
  background: #296BD6;
}
.page-home .temp-list-box .temp-info {
  display: inline-block;
  vertical-align: middle;
  width: 700px;
}
.page-home .temp-list-box .temp-info .name {
  display: inline-block;
  font-size: 20px;
  color: #333333;
  font-weight: 500;
  margin-right: 16px;
  vertical-align: middle;
}
.page-home .temp-list-box .temp-info .ui-btn-min {
  display: none;
  line-height: 26px;
  color: #cccccc;
  font-size: 18px;
  text-align: center;
  vertical-align: middle;
  margin-right: 8px;
  cursor: pointer;
}
.page-home .temp-list-box .temp-info .ui-btn-min:hover {
  color: #3D82F2;
}
.page-home .temp-list-box .temp-info .ui-btn-min:hover:before {
  color: #3D82F2;
}
.page-home .temp-list-box .temp-info .editing {
  display: none;
}
.page-home .temp-list-box .temp-info .btn-editing {
  font-size: 14px;
  color: #3D82F2;
  margin-left: 8px;
  cursor: pointer;
}
.page-home .temp-list-box .temp-info .border-btm {
  border-bottom: 1px solid #EEEEEE;
  padding-bottom: 5px;
}
.page-home .temp-list-box .temp-info .item-label {
  font-size: 16px;
  color: #333333;
  font-weight: 500;
  margin-right: 8px;
}
.page-home .temp-list-box .temp-info .temp-name {
  margin-bottom: 15px;
}
.page-home .temp-list-box .temp-info .temp-name .disabled {
  padding-bottom: 6px;
}
.page-home .temp-list-box .temp-info .temp-name .editing {
  width: 484px;
}
.page-home .temp-list-box .temp-info .temp-name .editing input[type=text] {
  width: 200px;
  height: 28px;
  font-size: 20px;
  color: #333333;
  border-bottom: 1px solid #EEEEEE;
  padding-bottom: 5px;
}
.page-home .temp-list-box .temp-info .temp-name .btn-editing {
  line-height: 28px;
}
.page-home .temp-list-box .temp-info .temp-dns {
  height: 30px;
}
.page-home .temp-list-box .temp-info .temp-dns .dns {
  font-size: 16px;
  color: #666666;
  margin-right: 16px;
}
.page-home .temp-list-box .temp-info .temp-dns .disabled {
  display: inline-block;
}
.page-home .temp-list-box .temp-info .temp-dns .editing {
  font-size: 16px;
  color: #666666;
}
.page-home .temp-list-box .temp-info .temp-dns .editing input[type=text] {
  font-size: 16px;
  color: #333333;
  width: 170px;
  vertical-align: top;
}
.page-home .temp-list-box .temp-info .temp-dns .editing .btn-editing {
  line-height: 22px;
}
.page-home .temp-list-box .temp-info .temp-dns .border-btm {
  display: inline-block;
  width: 270px;
}

/*删除弹框*/
.delete-temp-pop .pop-content {
  width: 348px;
}
.delete-temp-pop .pop-content .middle {
  text-align: center;
  padding: 20px 0;
}
.delete-temp-pop .pop-content .icon-delete {
  width: 90px;
  margin-bottom: 16px;
}
.delete-temp-pop .pop-content .tip {
  font-size: 14px;
  color: #333333;
}
.delete-temp-pop .pop-content .bottom-btns {
  border-top: 1px solid #EEEEEE;
}

.pop .bottom-btns {
  border-top: 1px solid #EEEEEE;
}
.pop .bottom-btns .btn {
  width: 64px;
  height: 30px;
  line-height: 30px;
  border-radius: 4px;
}
.pop .pop-ipt-list {
  text-align: left;
}
.pop .pop-ipt-list .item {
  margin-bottom: 10px;
  line-height: 30px;
  position: relative;
}
.pop .pop-ipt-list .item:last-child {
  margin-bottom: 0;
}
.pop .pop-ipt-list .item .item-label {
  display: inline-block;
  width: 120px;
  text-align: left;
  font-size: 14px;
  color: #333333;
  vertical-align: top;
}
.pop .pop-ipt-list .item .ipt-box {
  width: 320px;
  height: 30px;
  vertical-align: top;
  border: 1px solid #DDDDDD;
  border-radius: 2px;
  text-indent: 10px;
}
.pop .pop-ipt-list .link-list a {
  display: block;
  line-height: 20px;
  margin-bottom: 9px;
}
.pop .pop-ipt-list .col-yellow {
  color: #F1A135;
  display: block;
}

/*高级：绑定自有域名*/
.bind-dns-pop .pop-content {
  width: 540px;
}
.bind-dns-pop .pop-content .middle {
  padding: 17px 16px 16px;
}
.bind-dns-pop .item {
  padding-left: 130px;
}
.bind-dns-pop .item .ipt-box {
  width: 100% !important;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}
.bind-dns-pop .item-label {
  width: 130px !important;
  position: absolute;
  top: 0;
  left: 0;
}
.bind-dns-pop .select-box {
  position: relative;
  color: #dddddd;
  width: 100%;
}
.bind-dns-pop .select2-container--default .select2-selection--single {
  border-color: #dddddd;
}
.bind-dns-pop .item-li {
  margin-top: 16px;
  position: relative;
  padding-left: 76px;
}
.bind-dns-pop .item-li span {
  font-size: 14px;
  color: #333333;
  position: absolute;
  left: 0;
  width: 76px;
}
.bind-dns-pop .item-li span .col-red {
  color: #FC5B5B;
  vertical-align: top;
}
.bind-dns-pop .item-li .mini-ipt {
  width: 100%;
  height: 28px;
  border: 1px solid #DDDDDD;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  text-indent: 7px;
  font-size: 14px;
  border-radius: 2px;
  -webkit-border-radius: 2px;
  -moz-border-radius: 2px;
}
.bind-dns-pop .login-way-info {
  display: none;
}
.bind-dns-pop .login-default {
  display: block;
  margin-top: 16px;
}
.bind-dns-pop .login-default span {
  font-size: 14px;
  color: #333333;
}
.bind-dns-pop .login-default span .col-red {
  color: #FC5B5B;
  vertical-align: top;
}
.bind-dns-pop .select-box-mini {
  width: 240px;
}

/*添加*/
.add-temp-pop .pop-content {
  width: 480px;
}
.add-temp-pop .pop-content .middle {
  padding: 17px 16px 48px;
}
.add-temp-pop .pop-content .middle .item {
  margin-bottom: 16px;
}
.add-temp-pop .pop-content .middle .item:last-child {
  margin-bottom: 0;
}
.add-temp-pop .pop-content .middle .item-label {
  width: 78px;
}
.add-temp-pop .pop-content .middle .big-ipt {
  width: 360px;
}
.add-temp-pop .pop-content .middle .ipt-dns {
  width: 170px;
}

.ft-bold {
  font-weight: 500;
}

.mg-top10 {
  margin-top: 10px;
}

.select2-container--open {
  z-index: 999999;
}

.loading {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.2);
}

.loading .content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  text-align: center;
}

.loading .content .loading-gif {
  width: 150px;
}

.loading .content .loading-txt {
  margin-top: 10px;
  color: #ffffff;
  font-size: 16px;
}

.recycle-temp-pop .pop-content {
  width: 800px;
  height: 561px;
}
.recycle-temp-pop .pop-content .middle.recycleTable {
  padding: 16px 24px;
  max-height: 472px;
  overflow: auto;
}
.recycle-temp-pop .pop-content .middle.recycleTable table {
  color: #000000;
  font-size: 14px;
}
.recycle-temp-pop .pop-content .middle.recycleTable table thead tr th {
  background: #f5f5f5;
  text-align: left;
  line-height: 14px;
  padding: 16px;
  border: 1px solid #EEEEEE;
}
.recycle-temp-pop .pop-content .middle.recycleTable table tbody tr {
  background: #FFFFFF;
}
.recycle-temp-pop .pop-content .middle.recycleTable table tbody tr:hover {
  background: rgba(61, 130, 242, 0.1);
}
.recycle-temp-pop .pop-content .middle.recycleTable table tbody tr td {
  text-align: left;
  line-height: 14px;
  padding: 16px;
  border: 1px solid #EEEEEE;
}
.recycle-temp-pop .pop-content .middle.recycleTable table tbody tr td:last-child {
  width: 50px;
}
.recycle-temp-pop .pop-content .middle.recycleTable table tbody tr td i:before {
  color: #CCCCCC;
}
.recycle-temp-pop .pop-content .middle.recycleTable table tbody tr td i.icon-refresh {
  width: 12px;
  height: 12px;
  margin-right: 15px;
}
.recycle-temp-pop .pop-content .middle.recycleTable table tbody tr td i.icon-rubbish {
  width: 13px;
  height: 13px;
}
.recycle-temp-pop .pop-content .middle.recycleTable table tbody tr td .refresh {
  position: relative;
  float: left;
  cursor: pointer;
}
.recycle-temp-pop .pop-content .middle.recycleTable table tbody tr td .refresh span {
  display: none;
  position: absolute;
  background: rgba(0, 0, 0, 0.5);
  width: 34px;
  text-align: center;
  height: 22px;
  line-height: 22px;
  font-size: 12px;
  color: #FFFFFF;
  left: -10px;
  top: -28px;
}
.recycle-temp-pop .pop-content .middle.recycleTable table tbody tr td .refresh span::before {
  content: " ";
  border-left: 4px solid transparent;
  border-top: 4px solid rgba(0, 0, 0, 0.5);
  border-right: 4px solid transparent;
  border-bottom: 0 solid rgba(0, 0, 0, 0.5);
  position: absolute;
  left: 13px;
  top: 22px;
}
.recycle-temp-pop .pop-content .middle.recycleTable table tbody tr td .refresh:hover span {
  display: block;
}
.recycle-temp-pop .pop-content .middle.recycleTable table tbody tr td .delete {
  position: relative;
  float: left;
  cursor: pointer;
}
.recycle-temp-pop .pop-content .middle.recycleTable table tbody tr td .delete span {
  display: none;
  position: absolute;
  background: rgba(0, 0, 0, 0.5);
  width: 34px;
  text-align: center;
  height: 22px;
  line-height: 22px;
  font-size: 12px;
  color: #FFFFFF;
  left: -10px;
  top: -28px;
}
.recycle-temp-pop .pop-content .middle.recycleTable table tbody tr td .delete span::before {
  content: " ";
  border-left: 4px solid transparent;
  border-top: 4px solid rgba(0, 0, 0, 0.5);
  border-right: 4px solid transparent;
  border-bottom: 0 solid rgba(0, 0, 0, 0.5);
  position: absolute;
  left: 13px;
  top: 22px;
}
.recycle-temp-pop .pop-content .middle.recycleTable table tbody tr td .delete:hover span {
  display: block;
}

/*  华中科技大学大数据平台后台管理 */
.hzkd-page.page-home .temp-list-box .list {
  padding-top: 24px;
}