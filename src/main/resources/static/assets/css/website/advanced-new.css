@charset "UTF-8";
body {
  background: #F5F5F5;
}

.w-200 {
  width: 200px !important;
}

.w-1200 {
  width: 1200px;
  margin: 0 auto;
}

.header {
  background: #3D82F2;
}
.header .logo-txt {
  height: 60px;
  line-height: 60px;
  font-size: 24px;
  color: #FFFFFF;
}

/*面包屑样式*/
.breadcrumb-box {
  line-height: 54px;
}

.breadcrumb-box .item {
  font-size: 16px;
  color: rgba(0, 0, 0, 0.4);
  display: inline-block;
}

.breadcrumb-box .item.current {
  color: #3D82F2;
}

.breadcrumb-box .item.prev {
  color: #F7B528;
}

.breadcrumb-box .item:last-child .icon-up {
  display: none;
}

.breadcrumb-box .icon-up {
  font-size: 12px;
  display: inline-block;
  color: rgba(0, 0, 0, 0.4);
  margin: 0 5px;
  -webkit-transform: rotate(90deg);
  -ms-transform: rotate(90deg);
  transform: rotate(90deg);
}

.page-content {
  min-width: 1200px;
  background: #ffffff;
  min-height: 700px;
}
.page-content .temp-list {
  display: none;
  padding: 24px;
}
.page-content .tabs {
  border-bottom: 1px solid #DDDDDD;
  padding-left: 24px;
}
.page-content .tabs span {
  display: inline-block;
  padding-top: 16px;
  padding-bottom: 17px;
  font-size: 16px;
  color: #666666;
  margin-right: 16px;
  cursor: pointer;
}
.page-content .tabs .current {
  padding-bottom: 15px;
  color: #3D82F2;
  border-bottom: 2px solid #3D82F2;
}
.page-content .btn-href {
  display: inline-block;
  border: 1px solid #DDDDDD;
  width: 72px;
  height: 30px;
  line-height: 30px;
  background: #F5F5F5;
  font-size: 14px;
  color: #333333;
  margin-right: 12px;
  text-align: center;
  margin-bottom: 24px;
  outline: none;
}
.page-content .form-box {
  text-align: left;
}
.page-content .form-box .item {
  margin-bottom: 16px;
  line-height: 30px;
  position: relative;
}
.page-content .form-box .item:last-child {
  margin-bottom: 0;
}
.page-content .form-box .item .item-label {
  display: inline-block;
  width: 172px;
  text-align: left;
  font-size: 14px;
  color: #333333;
  vertical-align: top;
}
.page-content .form-box .item .item-label.flex-style {
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-box;
  display: -ms-flexbox;
  display: flex;
}
.page-content .form-box .item .ipt-box {
  width: 343px;
  height: 30px;
  vertical-align: top;
  border: 1px solid #DDDDDD;
  border-radius: 2px;
  text-indent: 8px;
}
.page-content .form-box .item .marginLR6 {
  margin: 0 6px;
  width: 300px;
}
.page-content .form-box .item .marginL20 {
  margin-left: 20px;
}
.page-content .form-box .item .col8A9099 {
  color: #8A9099;
}
.page-content .form-box .item .col474F59 {
  color: #474F59;
}
.page-content .form-box .item .ui-select-mini {
  width: 320px;
}
.page-content .form-box .item .select2-container--default .select2-selection--single {
  border-color: #dddddd;
}
.page-content .form-box .item .tip-div {
  position: relative;
}
.page-content .form-box .item .tip-div .btn-tip {
  display: inline-block;
  width: 14px;
  height: 30px;
  background: url("../../images/icon-14-info.png") center center no-repeat;
  background-size: 100%;
  cursor: pointer;
}
.page-content .form-box .item .tip-div .tip-txt {
  position: absolute;
  top: -77px;
  left: -15px;
  width: 240px;
  height: 79px;
  background: rgba(24, 30, 51, 0.8);
  border-radius: 4px;
  color: #FFFFFF;
  font-size: 12px;
  -webkit-box-sizing: border-box;
     -moz-box-sizing: border-box;
          box-sizing: border-box;
  padding: 10px 16px;
  display: none;
}
.page-content .form-box .item .tip-div .tip-txt img {
  width: 94px;
  height: 19px;
}
.page-content .form-box .item .tip-div .tip-txt .triangle {
  position: absolute;
  bottom: -10px;
  display: block;
  width: 0;
  height: 0;
  border: 6px solid;
  border-color: rgba(24, 30, 51, 0.8) transparent transparent transparent;
}
.page-content .form-box .item .tip-div:hover .btn-tip {
  background: url("../../images/icon-14-info-active.png") center center no-repeat;
  background-size: 100%;
}
.page-content .form-box .item .tip-div:hover .tip-txt {
  display: block;
}
.page-content .form-box .item .login-style-list {
  padding: 24px 20px;
  background: #F7F8FA;
  border-radius: 4px;
  margin-top: 16px;
}
.page-content .form-box .item .login-style-list ul {
  white-space: nowrap;
  overflow-x: auto;
  overflow-y: hidden;
}
.page-content .form-box .item .login-style-list ul li {
  position: relative;
  width: 180px;
  margin-right: 20px;
  cursor: pointer;
  text-align: center;
  display: inline-block;
}
.page-content .form-box .item .login-style-list ul li .img-box {
  height: 180px;
  padding: 19px 0;
  background: #F2F4F7;
  border-radius: 4px;
  -webkit-box-sizing: border-box;
     -moz-box-sizing: border-box;
          box-sizing: border-box;
  border: 1px solid #E1E5EB;
}
.page-content .form-box .item .login-style-list ul li .check-icon {
  position: absolute;
  top: 5px;
  right: 5px;
  display: inline-block;
  width: 20px;
  height: 20px;
  background: url("../../images/icon-20-gou.png") no-repeat;
  background-size: 100%;
  display: none;
}
.page-content .form-box .item .login-style-list ul li p {
  margin-top: 20px;
  color: #8A9099;
  font-size: 14px;
}
.page-content .form-box .item .login-style-list ul li.active .img-box {
  border: 2px solid #3A8BFF;
}
.page-content .form-box .item .login-style-list ul li.active .check-icon {
  display: block;
}
.page-content .form-box .item .login-style-list .mCSB_scrollTools_horizontal {
  bottom: -20px;
}
.page-content .form-box .item .login-style-list .mCSB_dragger_bar {
  height: 8px !important;
  background: #DADFE6;
}
.page-content .form-box .item .no-margin {
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-box;
  display: -ms-flexbox;
  display: flex;
  margin-left: 20px;
  position: relative;
  top: 6px;
}
.page-content .form-box .item .no-margin .layui-form-radio {
  margin: 0;
  padding: 0;
}
.page-content .form-box .item .no-margin .layui-input {
  height: 30px;
  line-height: 30px;
  margin-right: 6px;
}
.page-content .form-box .item .layui-div {
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-box;
  display: -ms-flexbox;
  display: flex;
}
.page-content .form-box .item .layui-div .layui-input {
  width: 60px;
}
.page-content .form-box .item .layui-table-box .delete-style {
  color: #3A8BFF;
  font-size: 13px;
}
.page-content .form-box .item .layui-table-box .layui-table thead tr {
  background-color: #F5F6F7;
}
.page-content .form-box .item .layui-table-box .layui-table th {
  border-color: #EBEDF0;
}
.page-content .form-box .item .layui-table-box .layui-form-checked[lay-skin=primary] i {
  border-color: #3A8BFF !important;
  background-color: #3A8BFF;
}
.page-content .form-box .item .layui-table-box .layui-form-checkbox[lay-skin=primary]:hover i {
  border-color: #3A8BFF !important;
}
.page-content .form-box .item .btn-div .btn-style {
  width: 60px;
  height: 28px;
  line-height: 28px;
  border-radius: 4px;
  border: 1px solid #91B6F2;
  font-size: 13px;
  font-weight: 400;
  color: #3E82F2;
  background: #ffffff;
  margin-right: 20px;
  cursor: pointer;
}
.page-content .form-box .item .btn-div .line {
  display: inline-block;
  position: relative;
  top: 4px;
  margin-right: 20px;
  width: 1px;
  height: 20px;
  background: #F2F2F2;
  border-radius: 1px;
}
.page-content .form-box .item .upload-div .upload-style {
  position: relative;
  width: 80px;
  height: 80px;
  background: #F2F4F7;
  border-radius: 4px;
  border: 1px dashed #DADFE6;
  text-align: center;
}
.page-content .form-box .item .upload-div .upload-style .file-button {
  position: absolute;
  top: 0;
  left: 0;
  width: 80px;
  height: 80px;
  opacity: 0;
  cursor: pointer;
}
.page-content .form-box .item .upload-div .upload-style .icon {
  display: inline-block;
  width: 25px;
  height: 24px;
  background: url("../../images/icon-24-upload.png") no-repeat;
  background-size: 100%;
  margin-top: 17px;
}
.page-content .form-box .item .upload-div .upload-style p {
  font-size: 12px;
  font-weight: 400;
  color: #3A8BFF;
  line-height: 20px;
}
.page-content .form-box .item .upload-div .upload-style .img-box {
  width: 80px;
  height: 80px;
}
.page-content .form-box .item .upload-div .file-btn-div span {
  display: inline-block;
  font-size: 14px;
  color: #3A8BFF;
  line-height: 20px;
  margin-top: 59px;
  margin-left: 14px;
  cursor: pointer;
}
.page-content .form-box .item .upload-div .file-btn-div .again {
  font-size: 14px;
  color: #3A8BFF;
  line-height: 20px;
  margin-top: 59px;
  margin-left: 14px;
  position: relative;
  cursor: pointer;
}
.page-content .form-box .item .upload-div .file-btn-div .again .file-button {
  position: absolute;
  width: 56px;
  height: 20px;
  opacity: 0;
  left: 0;
  top: 0;
  cursor: pointer;
}
.page-content .form-box .item .upload-div .file-tips {
  font-size: 12px;
  color: #8A9099;
  line-height: 20px;
  margin-left: 14px;
  margin-top: 59px;
}
.page-content .form-box .item .upload-div .txt {
  font-size: 12px;
  color: #8A9099;
  line-height: 20px;
  margin: 20px 0;
}
.page-content .form-box .item .upload-div .result-show {
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-box;
  display: -ms-flexbox;
  display: flex;
  width: 180px;
  height: 20px;
  background: #F5F7FA;
  border-radius: 2px;
  border: 1px solid #EDEDED;
  padding: 5px 34px 5px 40px;
  position: relative;
}
.page-content .form-box .item .upload-div .result-show img {
  width: 20px;
  height: 20px;
  position: absolute;
  left: 10px;
  top: 5px;
}
.page-content .form-box .item .upload-div .result-show span {
  font-size: 12px;
  color: #131B26;
  line-height: 20px;
  display: block;
}
.page-content .form-box .item .upload-div .result-show .delete-icon {
  display: inline-block;
  width: 12px;
  height: 12px;
  background: url("../../images/icon-12-delete.png") no-repeat;
  background-size: 100%;
  position: absolute;
  right: 10px;
  top: 9px;
}
.page-content .form-box .item .inline-style {
  display: inline-block;
  width: 200px;
  height: 30px;
  line-height: 30px;
  margin-top: 6px;
}
.page-content .form-box .url-open .url-bg {
  min-width: 460px;
  background: #F5F7FA;
  padding: 20px;
}
.page-content .form-box .url-open .url-bg .tips {
  font-size: 12px;
  color: #8A9099;
  margin-top: 10px;
  margin-left: 77px;
}
.page-content .form-box .url-open .url-bg .tips:last-child {
  color: #FF7D00;
}
.page-content .form-box .url-open .url-bg .tips a {
  color: #3A8BFF;
}
.page-content .form-box .link-list a {
  display: block;
  line-height: 20px;
  margin-bottom: 9px;
}
.page-content .form-box .col-yellow {
  color: #F1A135;
  margin-left: 16px;
}
.page-content .form-box .item-login-info {
  display: inline-block;
}
.page-content .form-box .login-way-info {
  display: none;
}
.page-content .form-box .login-default {
  display: block;
  margin-top: 16px;
}
.page-content .form-box .login-default span {
  font-size: 14px;
  color: #333333;
}
.page-content .form-box .login-default span .col-red {
  color: #FC5B5B;
  vertical-align: top;
}
.page-content .form-box .item-li {
  margin-top: 16px;
  position: relative;
}
.page-content .form-box .item-li span {
  font-size: 14px;
  color: #333333;
}
.page-content .form-box .item-li span .col-red {
  color: #FC5B5B;
  vertical-align: top;
}
.page-content .form-box .item-li .mini-ipt {
  width: 320px;
  height: 28px;
  border: 1px solid #DDDDDD;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  text-indent: 8px;
  font-size: 14px;
  border-radius: 2px;
  -webkit-border-radius: 2px;
  -moz-border-radius: 2px;
}
.page-content .form-box .item-reg-address {
  display: none;
  margin-top: 5px;
}
.page-content .form-box .item-checkbox {
  padding-left: 0;
  font-size: 14px;
  color: #333333;
  margin-top: 10px;
}
.page-content .form-box .item-checkbox .ipt-checkbox {
  margin-right: 3px;
  cursor: pointer;
}
.page-content .form-box .btn-save {
  width: 120px;
  height: 30px;
  border-radius: 4px;
  background: #3D82F2;
  font-size: 14px;
  color: #fff;
  border: none;
  margin: 20px 0 0 172px;
  outline: none;
  cursor: pointer;
}
.page-content .form-box .ui-select-mini .select2-container .select2-selection--single .select2-selection__rendered {
  padding-left: 8px;
}

.manager-list {
  padding-top: 0 !important;
}
.manager-list .btn-add {
  height: 30px;
  padding: 0 8px;
  line-height: 30px;
  border: 1px solid #3D82F2;
  border-radius: 2px;
  font-size: 14px;
  color: #fff;
  background-color: #3D82F2;
  margin: 16px 0;
  outline: none;
  cursor: pointer;
}
.manager-list .btn-add:hover {
  opacity: 0.8;
}
.manager-list .ui-table {
  border-color: rgba(0, 0, 0, 0.05);
  font-size: 14px;
  color: #333333;
  text-align: left;
}
.manager-list .ui-table th {
  background: #F6F5F8;
  height: 40px;
  padding: 0 20px;
  -webkit-box-sizing: border-box;
     -moz-box-sizing: border-box;
          box-sizing: border-box;
}
.manager-list .ui-table td {
  height: 50px;
  padding: 0 20px;
  word-break: break-all;
  -webkit-box-sizing: border-box;
     -moz-box-sizing: border-box;
          box-sizing: border-box;
}
.manager-list .ui-table td .icon-edit {
  font-size: 16px;
  color: #CCC9CD;
  cursor: pointer;
}
.manager-list .ui-table td .icon-rubbish {
  font-size: 16px;
  margin-left: 40px;
  cursor: pointer;
}
.manager-list .ui-table td .icon-rubbish:before {
  color: #CCC9CD;
}
.manager-list .ui-table .rule-list {
  padding: 5px 20px 0;
}
.manager-list .ui-table .rule-list .title {
  display: inline-block;
  width: 30%;
  height: 100%;
  vertical-align: top;
  line-height: 24px;
}
.manager-list .ui-table .rule-list .module {
  display: inline-block;
  width: 69%;
}
.manager-list .ui-table .rule-list span {
  display: inline-block;
  height: 24px;
  line-height: 24px;
  padding: 0 8px;
  background: #F3F3F5;
  margin-right: 8px;
  border-radius: 2px;
  margin-bottom: 5px;
}
.manager-list .ui-table .rule-list span:hover {
  background: #3D82F2;
  color: #fff;
}

/*弹框：添加管理员*/
.add-manager-pop .pop-content {
  width: 1050px;
}
.add-manager-pop .pop-info {
  padding: 0 24px 24px;
}
.add-manager-pop .search-box {
  width: 196px;
  height: 30px;
  border: 1px solid #DDDDDD;
  margin: 14px 0;
  border-radius: 2px;
  overflow: hidden;
}
.add-manager-pop .search-box .ipt-search {
  width: 164px;
  height: 100%;
  border: none;
  vertical-align: top;
  text-indent: 8px;
}
.add-manager-pop .search-box .icon-search {
  font-size: 16px;
  color: rgba(0, 0, 0, 0.4);
  float: right;
  margin: 6px 8px 0 0;
}
.add-manager-pop .table-box {
  height: 300px;
}
.add-manager-pop .ui-table {
  border-color: rgba(0, 0, 0, 0.05);
  font-size: 14px;
  color: #333333;
  text-align: left;
}
.add-manager-pop .ui-table th {
  background: #F6F5F8;
}
.add-manager-pop .ui-table th,
.add-manager-pop .ui-table td {
  height: 40px;
  padding: 0 20px;
  -webkit-box-sizing: border-box;
     -moz-box-sizing: border-box;
          box-sizing: border-box;
}
.add-manager-pop .ui-table input[type=radio] {
  font-size: 14px;
  color: red;
  cursor: pointer;
}
.add-manager-pop .bottom-btns {
  border-top: 1px solid #EEEEEE;
  padding-bottom: 16px;
}
.add-manager-pop .bottom-btns .btn {
  width: 78px;
  height: 30px;
  border-radius: 4px;
}

/*确认权限弹框*/
.sure-limits-pop .pop-content {
  width: 640px;
}
.sure-limits-pop .middle {
  padding: 24px;
  max-height: 400px;
  overflow-y: auto;
}
.sure-limits-pop .middle .item {
  position: relative;
  text-align: left;
  font-size: 14px;
  color: #333333;
  padding-left: 73px;
  margin-bottom: 14px;
}
.sure-limits-pop .middle .item .label {
  display: inline-block;
  width: 73px;
  position: absolute;
  left: 0;
  top: 0;
}
.sure-limits-pop .middle .item input[type=checkbox] {
  font-size: 14px;
  margin-right: 3px;
  vertical-align: inherit;
  cursor: pointer;
}
.sure-limits-pop .limit-list li {
  display: inline-block;
  margin: 0 16px 10px 0;
}
.sure-limits-pop .bottom-btns {
  border-top: 1px solid #EEEEEE;
  padding-bottom: 16px;
}
.sure-limits-pop .bottom-btns .btn {
  width: 78px;
  height: 30px;
  border-radius: 4px;
}

/*删除弹框*/
.delete-temp-pop .pop-content {
  width: 348px;
}
.delete-temp-pop .pop-content .middle {
  text-align: center;
  padding: 20px 0;
}
.delete-temp-pop .pop-content .icon-delete {
  width: 90px;
  margin-bottom: 16px;
}
.delete-temp-pop .pop-content .tip {
  font-size: 14px;
  color: #333333;
}
.delete-temp-pop .pop-content .bottom-btns {
  border-top: 1px solid #EEEEEE;
  padding-bottom: 16px;
}
.delete-temp-pop .pop-content .bottom-btns .btn {
  width: 64px;
  height: 30px;
  border-radius: 4px;
}

/*弹框：添加管理员2*/
.add-manager-pop-2 .pop-content {
  width: 465px;
}

.add-manager-pop-2 .middle {
  padding: 20px;
}

.add-manager-pop-3 .pop-content {
  width: 1020px;
}

.input-item {
  width: 100%;
  height: 48px;
  line-height: 48px;
}

.input-item label {
  float: left;
  width: 85px;
}

.input-item input[type=text] {
  width: 300px;
  height: 30px;
  border: solid 1px #ddd;
  border-radius: 2px;
  padding: 0 10px;
}

.input-item.model-1 input[type=text] {
  width: 400px;
}

.input-item.model-2 input[type=text] {
  width: 172px;
}

.input-item.model-1,
.input-item.model-2 {
  display: none;
}

/* 白名单 操作日志 */
.white-list .page-content,
.operation-log .page-content,
.review-content .page-content {
  width: 100%;
}

.white-list .page-content .temp-list,
.operation-log .page-content .temp-list {
  display: block;
}

.white-list .manager-list .ui-table tr th:nth-of-type(1),
.white-list .manager-list .ui-table tr th:nth-of-type(3),
.white-list .manager-list .ui-table tr td:nth-of-type(1),
.white-list .manager-list .ui-table tr td:nth-of-type(3) {
  text-align: center;
}

.white-list .forbid-tips .layui-form-item {
  margin-bottom: 0;
  padding-top: 16px;
  width: calc(100% - 80px);
}
.white-list .forbid-tips .layui-form-label {
  width: 225px;
  text-align: left;
  padding: 9px 0;
}
.white-list .forbid-tips .layui-input-inline {
  padding: 5px 0;
  width: auto;
}
.white-list .forbid-tips .layui-input-inline .layui-form-radio {
  margin-top: 0;
  margin-right: 0;
}
.white-list .forbid-tips .layui-input-inline .tip-div {
  position: relative;
  display: inline-block;
  margin: 6px 10px 0 -10px;
}
.white-list .forbid-tips .layui-input-inline .tip-div .btn-tip {
  cursor: pointer;
}
.white-list .forbid-tips .layui-input-inline .tip-div .tip-txt {
  position: absolute;
  top: 26px;
  left: -100px;
  min-width: 200px;
  line-height: 20px;
  padding: 4px 0;
  background-color: #666666;
  font-size: 12px;
  color: #ffffff;
  border-radius: 4px;
  text-align: center;
  display: none;
}
.white-list .forbid-tips .layui-input-inline .tip-div:hover .tip-txt {
  display: block;
}
.white-list .forbid-tips .tip-input {
  width: calc(100% - 400px);
  max-width: 800px;
}
.white-list .forbid-tips .tip-input input {
  width: 100%;
  height: 30px;
  background: #ffffff;
  border: 1px solid rgba(0, 0, 0, 0.14);
  border-radius: 2px;
  padding: 5px 12px;
  line-height: 20px;
  -webkit-box-sizing: border-box;
     -moz-box-sizing: border-box;
          box-sizing: border-box;
}
.white-list .forbid-tips .btn-add {
  margin-top: 20px;
}

button.btn-remove {
  height: 30px;
  padding: 0 12px;
  line-height: 30px;
  border: 1px solid #ddd;
  border-radius: 2px;
  font-size: 14px;
  color: #333;
  background: #fff;
  margin: 16px auto;
  outline: none;
  cursor: pointer;
}

button.btn-remove:hover {
  border: 1px solid #3d82f2;
  color: #3d82f2;
}

.operation-log .layui-form-item {
  clear: none;
  padding: 16px 0;
  margin-bottom: 0;
}

.operation-log .layui-form-select dl dd.layui-this {
  background-color: #3d82f2;
}

.operation-log .layui-form-label {
  padding: 5px 15px;
}

.operation-log .layui-form-select dl {
  top: 30px;
}

.layui-laydate-content td.laydate-selected {
  background-color: rgba(61, 130, 242, 0.1) !important;
}

.operation-log .layui-input {
  height: 30px;
}

.operation-log .manager-list .btn-add {
  margin: 0;
}

/* 文章审核 */
.custom-search {
  position: relative;
  width: 196px;
  margin: 10px 0;
}

.custom-search .layui-inline {
  width: 100%;
}

.custom-search .layui-input,
.custom-search .layui-btn {
  height: 30px;
  line-height: 30px;
}

.custom-search .layui-btn {
  position: absolute;
  right: 0;
  top: 0;
  z-index: 2;
  background-color: transparent;
  color: rgb(153, 153, 153);
  padding: 0 5px;
}

.custom-search .layui-btn:hover {
  color: rgb(153, 153, 153);
}

.nearlist {
  width: 100%;
  height: 300px;
  overflow-y: scroll;
  border-top: solid 1px rgba(0, 0, 0, 0.1);
  border-bottom: solid 1px rgba(0, 0, 0, 0.1);
}

.nearlist .layui-form-checked[lay-skin=primary] i,
.review-content .layui-form-checked[lay-skin=primary] i,
.add-auditor-pop .layui-form-checked[lay-skin=primary] i {
  border-color: #3d82f2 !important;
  background-color: #3d82f2;
}

.nearlist .layui-form-checkbox[lay-skin=primary]:hover i,
.review-content .layui-form-checkbox[lay-skin=primary]:hover i,
.add-auditor-pop .layui-form-checkbox[lay-skin=primary]:hover i {
  border-color: #3d82f2;
}

.review-content .page-content .temp-list {
  display: block;
}

.review-content .clear .layui-btn {
  margin: 10px 10px 10px 0;
}

.review-content .layui-btn-sm {
  height: 24px;
  line-height: 24px;
  padding: 0 8px;
  font-size: 14px;
}

.review-content .layui-btn-primary {
  border: 1px solid #C9C9C9;
  background-color: #fff;
  color: #555;
}

.review-content .js-review-button {
  height: 44px;
}

.review-content .js-review-button button {
  display: none;
}

.review-iframe {
  width: 100%;
  height: 600px;
  border: 0;
}

/* 添加审核人员弹窗 */
.add-auditor-pop .pop-content {
  width: 650px;
}
.add-auditor-pop .pop-content .middle {
  padding: 0 20px;
  height: 400px;
  overflow-y: auto;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}
.add-auditor-pop .pop-content .middle .ui-table th {
  background: #F6F5F8;
  height: 40px;
  padding: 0 0 0 20px;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  text-align: left;
}
.add-auditor-pop .pop-content .middle .ui-table td {
  height: 40px;
  padding: 0 20px;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}

.btn-bg-blue {
  border: 1px solid #3D82F2;
  padding: 0 8px;
  border-radius: 2px;
  font-size: 14px;
  color: #fff;
  background-color: #3D82F2;
  outline: none;
}

/* 模块审核设置 */
.module-check-list {
  padding-top: 0 !important;
}
.module-check-list .btn-bg-blue {
  height: 30px;
  line-height: 30px;
  margin: 10px 16px 10px 0;
  cursor: pointer;
}
.module-check-list .btn-bg-blue:hover {
  opacity: 0.8;
}
.module-check-list .ui-table {
  border-color: rgba(0, 0, 0, 0.05);
  font-size: 14px;
  color: #333333;
  text-align: left;
}
.module-check-list .ui-table th {
  background: #F6F5F8;
  height: 40px;
  padding: 0 20px;
  -webkit-box-sizing: border-box;
     -moz-box-sizing: border-box;
          box-sizing: border-box;
}
.module-check-list .ui-table td {
  height: 50px;
  padding: 0 20px;
  -webkit-box-sizing: border-box;
     -moz-box-sizing: border-box;
          box-sizing: border-box;
}
.module-check-list .ui-table td .btn-link {
  text-decoration: underline;
}
.module-check-list .ui-table .table-step li {
  display: inline-block;
  color: #3D82F2;
}
.module-check-list .ui-table .table-step li:last-child .step-arrow {
  display: none;
}
.module-check-list .layui-form-onswitch {
  border-color: #3D82F2;
  background-color: #3D82F2;
}

.layui-form-onswitch {
  border-color: #3D82F2;
  background-color: #3D82F2;
}

/* 审核流程设置 */
.check-step-page .page-content {
  padding-bottom: 30px;
  position: relative;
  overflow: hidden;
}
.check-step-page .top-btns {
  overflow: hidden;
  border-bottom: 1px solid #ddd;
}
.check-step-page .top-btns button {
  -webkit-box-sizing: border-box;
     -moz-box-sizing: border-box;
          box-sizing: border-box;
  line-height: 40px;
  height: 40px;
  margin: 10px;
  padding: 0 10px;
  cursor: pointer;
}
.check-step-page .top-btns .btn-border-blue {
  border: 1px solid #3D82F2;
  font-size: 16px;
  color: #3D82F2;
  background-color: white;
}
.check-step-page .top-btns .btn-bg-blue {
  border: 1px solid #3D82F2;
}
.check-step-page .check-step-content {
  padding: 30px 0 0 50px;
  -webkit-box-sizing: border-box;
     -moz-box-sizing: border-box;
          box-sizing: border-box;
  color: #3D82F2;
  width: 400px;
  text-align: center;
}
.check-step-page .check-step-content .step-border-blue {
  border: 1px solid #3D82F2;
  padding: 10px;
  display: inline-block;
  width: 120px;
  text-align: center;
  font-size: 18px;
  -webkit-box-sizing: border-box;
     -moz-box-sizing: border-box;
          box-sizing: border-box;
}
.check-step-page .check-step-content .next-add {
  width: 120px;
  text-align: center;
  -webkit-box-sizing: border-box;
     -moz-box-sizing: border-box;
          box-sizing: border-box;
  margin: 0 auto;
}
.check-step-page .check-step-content .next-add .next-line {
  position: relative;
  width: 2px;
  height: 40px;
  background-color: #3D82F2;
  margin: 0 auto;
}
.check-step-page .check-step-content .next-add .line-down {
  height: 40px;
}
.check-step-page .check-step-content .next-add .line-down i {
  position: absolute;
  bottom: 5px;
  left: -13px;
  -webkit-transform: rotate(-90deg);
      -ms-transform: rotate(-90deg);
          transform: rotate(-90deg);
}
.check-step-page .check-step-content .next-add .step-add {
  color: #ffffff;
  background-color: #3D82F2;
  border-radius: 50%;
  width: 26px;
  height: 26px;
  line-height: 26px;
  font-size: 14px;
  margin: 0 auto;
  cursor: pointer;
}
.check-step-page .check-step-content .new-step-lists .list-content {
  position: relative;
  display: inline-block;
}
.check-step-page .check-step-content .new-step-lists .list-content .layui-icon-close-fill {
  position: absolute;
  top: -12px;
  right: -12px;
  color: #3D82F2;
  font-size: 24px;
  opacity: 0;
  cursor: pointer;
}
.check-step-page .check-step-content .new-step-lists .list-content:hover .layui-icon-close-fill {
  opacity: 1;
}
.check-step-page .check-step-content .new-step-lists .list-content .list-border {
  border: 1px solid #ddd;
  padding: 20px 10px;
  -webkit-box-sizing: border-box;
     -moz-box-sizing: border-box;
          box-sizing: border-box;
}
.check-step-page .check-step-content .new-step-lists .list-content .list-border .list-avatar {
  width: 40px;
  height: 40px;
  line-height: 40px;
  border-radius: 50%;
  background-color: #3D82F2;
  text-align: center;
  color: #ffffff;
  display: inline-block;
  vertical-align: middle;
  margin: 0 10px;
}
.check-step-page .check-step-content .new-step-lists .list-content .list-border .list-avatar .layui-icon-friends {
  font-size: 30px;
}
.check-step-page .check-step-content .new-step-lists .list-content .list-border .list-right {
  display: inline-block;
  vertical-align: middle;
  margin: 0 10px;
}
.check-step-page .check-step-content .new-step-lists .list-content .list-border .list-right .list-name {
  color: #ffffff;
  background-color: #666;
  padding: 0 10px;
  margin-top: 10px;
  border-radius: 4px;
}

.fs0 {
  font-size: 0;
}

/* 审核人员弹窗 */
#auditorPop.pop {
  position: absolute;
  background: rgba(0, 0, 0, 0);
  left: auto;
}
#auditorPop.pop .pop-content {
  right: 0;
  left: auto;
  top: 100px;
  bottom: auto;
  -webkit-transform: unset;
      -ms-transform: unset;
          transform: unset;
  width: 260px;
  height: 600px;
}
#auditorPop.pop .pop-content .middle {
  position: absolute;
  top: 55px;
  bottom: 63px;
  overflow-y: auto;
  width: 100%;
}
#auditorPop.pop .pop-content .select-lists {
  padding-left: 30px;
}
#auditorPop.pop .pop-content .select-lists .layui-anim.layui-icon {
  font-size: 16px;
}
#auditorPop.pop .pop-content .bottom-btns {
  position: absolute;
  width: 100%;
  bottom: 0;
}

.animated {
  -webkit-animation-duration: 1s;
          animation-duration: 1s;
  -webkit-animation-fill-mode: both;
          animation-fill-mode: both;
}

@-webkit-keyframes slideInRight {
  from {
    -webkit-transform: translate3d(100%, 0, 0);
            transform: translate3d(100%, 0, 0);
    visibility: visible;
  }
  to {
    -webkit-transform: translate3d(0, 0, 0);
            transform: translate3d(0, 0, 0);
  }
}

@keyframes slideInRight {
  from {
    -webkit-transform: translate3d(100%, 0, 0);
            transform: translate3d(100%, 0, 0);
    visibility: visible;
  }
  to {
    -webkit-transform: translate3d(0, 0, 0);
            transform: translate3d(0, 0, 0);
  }
}
.slideInRight {
  -webkit-animation-name: slideInRight;
          animation-name: slideInRight;
}

@-webkit-keyframes slideOutRight {
  from {
    -webkit-transform: translate3d(0, 0, 0);
            transform: translate3d(0, 0, 0);
  }
  to {
    visibility: hidden;
    -webkit-transform: translate3d(100%, 0, 0);
            transform: translate3d(100%, 0, 0);
  }
}

@keyframes slideOutRight {
  from {
    -webkit-transform: translate3d(0, 0, 0);
            transform: translate3d(0, 0, 0);
  }
  to {
    visibility: hidden;
    -webkit-transform: translate3d(100%, 0, 0);
            transform: translate3d(100%, 0, 0);
  }
}
.slideOutRight {
  -webkit-animation-name: slideOutRight;
          animation-name: slideOutRight;
}

/* 模块数据审核 */
.tab-check-page .temp-list {
  width: 100%;
  -webkit-box-sizing: border-box;
     -moz-box-sizing: border-box;
          box-sizing: border-box;
  padding: 24px 0;
}
.tab-check-page .temp-list .content-left {
  width: 25%;
}
.tab-check-page .temp-list .content-left .custom-search {
  width: auto;
  margin: 0 20px;
}
.tab-check-page .temp-list .content-left .select-lists .check-all {
  margin: 20px;
}
.tab-check-page .temp-list .content-left .select-lists .lists .list {
  cursor: pointer;
  display: block;
  border-bottom: 1px solid #eee;
  padding: 10px 20px;
}
.tab-check-page .temp-list .content-left .select-lists .lists .list.active {
  border-color: #3D82F2;
}
.tab-check-page .temp-list .js-review-button {
  margin: 10px 20px;
}
.tab-check-page .temp-list .js-review-button .layui-btn-primary:hover {
  border-color: #3D82F2;
}
.tab-check-page .temp-list .content-center {
  width: 50%;
  padding-left: 20px;
  -webkit-box-sizing: border-box;
     -moz-box-sizing: border-box;
          box-sizing: border-box;
}
.tab-check-page .temp-list .content-center .info {
  padding: 4px;
  font-weight: 500;
  margin-bottom: 12px;
}
.tab-check-page .temp-list .content-center .info span {
  margin-right: 10px;
}
.tab-check-page .temp-list .content-center .iframe-content {
  position: relative;
  background-color: #aaa;
}
.tab-check-page .temp-list .content-center .iframe-content iframe {
  margin: 0;
  width: 100%;
  min-height: 600px;
}
.tab-check-page .temp-list .content-center .check-table {
  font-size: 14px;
  color: #333;
  line-height: 20px;
  -webkit-box-sizing: border-box;
     -moz-box-sizing: border-box;
          box-sizing: border-box;
  margin-bottom: 20px;
}
.tab-check-page .temp-list .content-center .check-table .table-list {
  overflow: hidden;
  width: 100%;
  -webkit-box-sizing: border-box;
     -moz-box-sizing: border-box;
          box-sizing: border-box;
  border-bottom: 1px solid #eee;
}
.tab-check-page .temp-list .content-center .check-table .table-list.table-head {
  background-color: #F5F5F5;
}
.tab-check-page .temp-list .content-center .check-table .table-list.table-head li {
  padding: 16px 13px;
}
.tab-check-page .temp-list .content-center .check-table .table-list li {
  border-left: 1px solid #eee;
  padding: 10px 13px;
  float: left;
  -webkit-box-sizing: border-box;
     -moz-box-sizing: border-box;
          box-sizing: border-box;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.tab-check-page .temp-list .content-center .check-table .table-list li:last-child {
  border-right: 1px solid #eee;
}
.tab-check-page .temp-list .content-center .check-table .table-list li.btn-link {
  color: #3D82F2;
  cursor: pointer;
}
.tab-check-page .temp-list .content-center .check-table .table-list .status-icon {
  display: inline-block;
  width: 6px;
  height: 6px;
  margin-right: 6px;
  border-radius: 50%;
}
.tab-check-page .temp-list .content-center .check-table .table-list .status-pass .status-icon {
  background-color: #21C32B;
}
.tab-check-page .temp-list .content-center .check-table .table-list .status-pass-no .status-icon {
  background-color: #E92222;
}
.tab-check-page .temp-list .content-center .table-list3 .table-list li.name {
  width: 24%;
}
.tab-check-page .temp-list .content-center .table-list3 .table-list li.link {
  width: 57%;
}
.tab-check-page .temp-list .content-center .table-list3 .table-list li.operate {
  width: 19%;
}
.tab-check-page .temp-list .content-center .table-list3 .table-list li.status {
  width: 19%;
}
.tab-check-page .temp-list .content-center .check-input-content .input-content {
  position: relative;
  border: 1px solid #ddd;
}
.tab-check-page .temp-list .content-center .check-input-content .input-content .input-area {
  width: 100%;
  height: 200px;
  padding: 16px 16px 30px 16px;
  -webkit-box-sizing: border-box;
     -moz-box-sizing: border-box;
          box-sizing: border-box;
}
.tab-check-page .temp-list .content-center .check-input-content .input-content .count-num {
  position: absolute;
  bottom: 0;
  right: 0;
  padding: 6px 20px;
}
.tab-check-page .temp-list .content-center .check-input-content .btns {
  padding-top: 20px;
}
.tab-check-page .temp-list .content-center .check-input-content .btns button {
  color: white;
}
.tab-check-page .temp-list .content-center .check-input-content .btns button.btn-pass {
  background-color: rgb(28, 200, 133);
}
.tab-check-page .temp-list .content-center .check-input-content .btns button.btn-pass-no {
  background-color: rgb(194, 0, 54);
}
.tab-check-page .temp-list .content-center .table-list4 .table-list li.name {
  width: 24%;
}
.tab-check-page .temp-list .content-center .table-list4 .table-list li.link {
  width: 42%;
}
.tab-check-page .temp-list .content-center .table-list4 .table-list li.status {
  width: 19%;
}
.tab-check-page .temp-list .content-center .table-list4 .table-list li.operate {
  width: 15%;
}
.tab-check-page .temp-list .content-center .check-opinion {
  line-height: 20px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 5;
  -webkit-box-orient: vertical;
}
.tab-check-page .temp-list .content-right {
  width: 25%;
}
.tab-check-page .temp-list .content-right .step-lists {
  padding: 0 20px;
}
.tab-check-page .temp-list .content-right .step-lists .step-list .list-top {
  overflow: hidden;
}
.tab-check-page .temp-list .content-right .step-lists .step-list .list-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  border: 1px solid #eee;
  margin-right: 10px;
}
.tab-check-page .temp-list .content-right .step-lists .step-list .list-avatar img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
}
.tab-check-page .temp-list .content-right .step-lists .step-list .list-info .layui-btn {
  background-color: #3D82F2;
  height: 24px;
  line-height: 24px;
  padding: 0 10px;
}
.tab-check-page .temp-list .content-right .step-lists .step-list .list-info .list-name {
  width: 90px;
  line-height: 24px;
}
.tab-check-page .temp-list .content-right .step-lists .step-list .list-info .list-time {
  margin-top: 16px;
}
.tab-check-page .temp-list .content-right .step-lists .step-list .list-line {
  min-height: 50px;
  margin-left: 30px;
  border-left: 2px solid #3D82F2;
  padding-left: 40px;
  padding-top: 4px;
  -webkit-box-sizing: border-box;
     -moz-box-sizing: border-box;
          box-sizing: border-box;
}
.tab-check-page .temp-list .content-right .step-lists .step-list:last-child .list-line {
  border-color: #ffffff;
}
.tab-check-page .temp-list .content-right .step-lists .step-list.list-disable .list-avatar img {
  opacity: 0.5;
}
.tab-check-page .temp-list .content-right .step-lists .step-list.list-disable .list-name {
  color: #ccc;
}
.tab-check-page .temp-list .content-right .step-lists .step-list.list-disable .list-status {
  display: none;
}
.tab-check-page .temp-list .content-right .step-lists .step-list.list-disable .list-time {
  display: none;
}
.tab-check-page .temp-list .content-right .step-lists .step-list.list-disable .list-line {
  border-color: #eee;
}
.tab-check-page .temp-list .check-input-content {
  padding: 20px 20px 0 20px;
}
.tab-check-page .temp-list .check-input-content .input-content {
  position: relative;
  border: 1px solid #ddd;
}
.tab-check-page .temp-list .check-input-content .input-content .input-area {
  width: 100%;
  height: 300px;
  padding: 10px 10px 30px 10px;
  -webkit-box-sizing: border-box;
     -moz-box-sizing: border-box;
          box-sizing: border-box;
}
.tab-check-page .temp-list .check-input-content .input-content .count-num {
  position: absolute;
  bottom: 0;
  right: 0;
  padding: 6px 20px;
}
.tab-check-page .temp-list .check-input-content .btns {
  padding-top: 20px;
}
.tab-check-page .temp-list .check-input-content .btns button {
  color: white;
}
.tab-check-page .temp-list .check-input-content .btns button.btn-pass {
  background-color: rgb(28, 200, 133);
}
.tab-check-page .temp-list .check-input-content .btns button.btn-pass-no {
  background-color: rgb(194, 0, 54);
  float: right;
}
.tab-check-page .temp-list.my-checked .content-left .select-lists {
  text-align: center;
  margin-top: 20px;
}
.tab-check-page .temp-list.my-checked .content-left .select-lists .list.active {
  color: #3D82F2;
}
.tab-check-page .temp-list .checked-status {
  position: absolute;
  bottom: 20px;
  right: 20px;
}
.tab-check-page .temp-list .checked-status img {
  width: 150px;
}
.tab-check-page .temp-list .checked-status.passed .status-pass-no {
  display: none;
}
.tab-check-page .temp-list .checked-status.passed-no .status-pass {
  display: none;
}
.tab-check-page .layui-form-checkbox[lay-skin=primary] {
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.tab-check-page .layui-form-checkbox[lay-skin=primary] span {
  display: block;
}
.tab-check-page .layui-form-checked[lay-skin=primary] span {
  color: #3d82f2;
}
.tab-check-page .layui-form-checked[lay-skin=primary] i {
  border-color: #3d82f2 !important;
  background-color: #3d82f2;
}
.tab-check-page .layui-form-checkbox[lay-skin=primary]:hover span {
  color: #3d82f2;
}
.tab-check-page .layui-form-checkbox[lay-skin=primary]:hover i {
  border-color: #3d82f2;
}

.flex-style {
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
      -ms-flex-wrap: wrap;
          flex-wrap: wrap;
}

.my-layer .custom-link {
  display: block;
  width: 100%;
  height: 42px;
  line-height: 50px;
  text-align: left;
}

.my-layer .layui-upload-drag .layui-icon {
  color: #0099ff;
}

.my-layer.layui-layer {
  border-radius: 6px;
}

.my-layer .layui-layer-title {
  text-align: left;
  border-radius: 6px;
  background: #fff;
  padding: 0 80px 0 32px;
}
.my-layer .custom-layer {
  text-align: center;
  padding: 16px 24px 10px 24px;
  overflow: hidden;
}
.my-layer .custom-layer .layui-upload-drag {
  width: calc(100% - 60px);
  height: 90px;
  border-radius: 4px;
}
.my-layer .custom-layer a {
  color: #0099ff;
}
.my-layer .layui-layer-btn {
  border-top: solid 1px rgb(238, 238, 238);
  text-align: center;
}