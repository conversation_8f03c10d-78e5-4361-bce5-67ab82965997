@charset "UTF-8";
body {
  background-color: #F5F5F5;
}

#index {
  position: relative;
  height: 100%;
}

/*sid-groups部分的样式*/
#contact,
#header,
#banner,
#footer {
  position: relative;
  z-index: 5;
}
#contact:hover,
#header:hover,
#banner:hover,
#footer:hover {
  z-index: 99;
}
#contact .module-set,
#header .module-set,
#banner .module-set,
#footer .module-set {
  padding-bottom: 4px;
  z-index: 999;
}
#contact .module-set > div,
#header .module-set > div,
#banner .module-set > div,
#footer .module-set > div {
  display: block;
  padding: 8px;
  color: #666;
  font-size: 12px;
  z-index: 10;
  cursor: pointer;
  min-width: 76px;
  height: 33px;
  background: rgb(255, 255, 255);
  -webkit-box-shadow: 0 0 5px 0 rgba(0, 0, 0, 0.3);
          box-shadow: 0 0 5px 0 rgba(0, 0, 0, 0.3);
  border-radius: 4px;
  text-align: center;
  -webkit-box-sizing: border-box;
     -moz-box-sizing: border-box;
          box-sizing: border-box;
}
#contact .module-set .icon-set,
#header .module-set .icon-set,
#banner .module-set .icon-set,
#footer .module-set .icon-set {
  margin-right: 4px;
  font-size: 16px;
}
#contact .module-set span,
#header .module-set span,
#banner .module-set span,
#footer .module-set span {
  display: inline-block;
  vertical-align: middle;
}

#footer {
  z-index: 10;
}

/*aid-groups部分的样式*/
.content,
.aid-groups {
  height: 100%;
}

/*中间画布的样式*/
.content .aid-groups {
  position: relative;
  z-index: 5;
}

.grid-stack {
  -webkit-box-sizing: border-box;
     -moz-box-sizing: border-box;
          box-sizing: border-box;
}
.grid-stack .grid-stack-item .grid-stack-item-content {
  overflow-y: hidden;
}
.grid-stack .grid-stack-item .grid-stack-item-content > div {
  min-height: 100%;
}

.aid-groups .grid-wrap {
  position: relative;
  overflow: hidden;
}
.aid-groups .grid-wrap .grid-stack-item-content {
  border: 2px solid transparent;
  -webkit-box-sizing: border-box;
     -moz-box-sizing: border-box;
          box-sizing: border-box;
}
.aid-groups .grid-wrap.editor-grid .grid-stack-main {
  padding-top: 36px;
  border-bottom: 2px dotted #ccc;
}
.aid-groups .grid-wrap.editor-grid .grid-stack-main:before {
  position: absolute;
  content: "";
  top: 36px;
  left: 0;
  bottom: 0;
  border-left: 2px dotted #ccc;
}
.aid-groups .grid-wrap.editor-grid .grid-stack-main:after {
  position: absolute;
  content: "";
  top: 36px;
  right: 0;
  bottom: 0;
  border-right: 2px dotted #ccc;
}
.aid-groups .grid-wrap.editor-grid .grid-stack:before {
  position: absolute;
  top: -36px;
  left: 0;
  content: "";
  border-bottom: 2px dotted #ccc;
  height: 36px;
  width: 100%;
}
.aid-groups .grid-wrap.editor-grid .grid-stack-item-content {
  border: 2px dashed #ddd;
  -webkit-box-sizing: border-box;
     -moz-box-sizing: border-box;
          box-sizing: border-box;
}
.aid-groups .grid-wrap.editor-grid .grid-stack-item:hover .top-btns {
  display: block;
}
.aid-groups .grid-wrap.editor-grid .grid-stack-item:hover .grid-stack-item-content {
  border: 2px dashed #3D82F2;
}
.aid-groups .grid-stack {
  margin: 0 auto;
  min-height: 100px;
  z-index: 10;
}
.aid-groups .top-btns {
  position: absolute;
  display: none;
  top: -30px;
  right: 10px;
  z-index: 100;
  padding-bottom: 10px;
  height: 33px;
  line-height: 33px;
  font-size: 12px;
  color: #666;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
}
.aid-groups .top-btns .icon {
  display: inline-block;
  font-size: 16px;
  vertical-align: middle;
}
.aid-groups .top-btns span {
  display: inline-block;
  vertical-align: middle;
}
.aid-groups .top-btn-groups {
  padding: 0 16px;
  background: rgb(255, 255, 255);
  -webkit-box-shadow: 0 0 5px 0 rgba(0, 0, 0, 0.3);
          box-shadow: 0 0 5px 0 rgba(0, 0, 0, 0.3);
  border-radius: 4px;
}
.aid-groups .top-btn-groups > span:not(:last-child) {
  margin-right: 12px;
}
.aid-groups .top-btn-groups i {
  margin-right: 4px;
}
.aid-groups .top-btn-groups .set,
.aid-groups .top-btn-groups .delete {
  cursor: pointer;
}
.aid-groups .top-btn-groups .drag-nav {
  cursor: move;
}

/*编辑模块名字的弹框样式*/
.name-edit-pop .pop-content {
  width: 380px;
}
.name-edit-pop .middle {
  text-align: center;
  padding: 40px 16px;
  -webkit-box-sizing: border-box;
     -moz-box-sizing: border-box;
          box-sizing: border-box;
}
.name-edit-pop label {
  display: inline-block;
  margin-right: 8px;
  vertical-align: middle;
}
.name-edit-pop .inp-name {
  padding: 0 8px;
  width: 240px;
  height: 30px;
  border-radius: 2px;
  border: 1px solid rgb(221, 221, 221);
  -webkit-box-sizing: border-box;
     -moz-box-sizing: border-box;
          box-sizing: border-box;
}

/*图书馆全屏的样式*/
#pageTopWrap {
  top: 0;
  min-width: 0;
}

#header {
  left: 0;
  width: 100%;
  background-color: transparent;
  z-index: 10;
}

#footer {
  width: 100%;
}

.ft-nav-wrap {
  position: fixed;
  top: 50%;
  right: 60px;
  z-index: 100;
  transform: translateY(-50%);
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
}
.ft-nav-wrap:hover .general-set {
  display: block;
}

#fp-nav {
  max-height: 680px;
  margin-top: 0 !important;
  overflow: hidden;
  /*分页器样式1*/
  /*分页器样式2*/
  /*分页器样式3*/
}
#fp-nav:hover .general-set {
  display: block;
}
#fp-nav .pagination-block {
  width: 72px;
  text-align: center;
}
#fp-nav .pagination-block .pagination-item-icon {
  width: 24px;
  height: 24px;
  margin-top: 2px;
}
#fp-nav .pagination-block li {
  margin: 8px 0 16px;
}
#fp-nav .pagination-block li a:hover:not(.active) {
  -webkit-box-shadow: 0 4px 6px 0 rgba(0, 0, 0, 0.16);
          box-shadow: 0 4px 6px 0 rgba(0, 0, 0, 0.16);
}
#fp-nav .pagination-block a {
  display: inline-block;
  width: 64px;
  height: 64px;
  border-radius: 8px;
  padding: 7px 0;
  -webkit-box-sizing: border-box;
     -moz-box-sizing: border-box;
          box-sizing: border-box;
  background: rgb(255, 255, 255);
  -webkit-box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.16);
          box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.16);
  text-align: center;
  cursor: pointer;
}
#fp-nav .pagination-block .active {
  -webkit-transform: scale(1.1);
  -ms-transform: scale(1.1);
  transform: scale(1.1);
}
#fp-nav .pagination-block p {
  margin-top: 5px;
  overflow: hidden;
}
#fp-nav .pagination-circle {
  overflow: visible;
  max-height: 610px;
  overflow-y: auto;
  padding: 10px 20px 10px 10px;
  margin-right: -20px;
}
#fp-nav .pagination-circle li {
  display: block;
  width: 16px;
  height: 16px;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 50%;
  margin-bottom: 16px;
}
#fp-nav .pagination-circle li a {
  display: block;
  height: 100%;
  border-radius: 50%;
}
#fp-nav .pagination-circle li:hover a:not(.active) {
  background: rgba(0, 0, 0, 0.4);
}
#fp-nav .pagination-big-circle li {
  display: block;
  text-align: center;
  border-radius: 50%;
  margin-bottom: 16px;
  width: 48px;
}
#fp-nav .pagination-big-circle li a {
  display: inline-block;
  width: 40px;
  height: 40px;
  line-height: 40px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  color: #333;
  -webkit-box-shadow: 0 0 4px 0 rgba(0, 0, 0, 0.16);
          box-shadow: 0 0 4px 0 rgba(0, 0, 0, 0.16);
}
#fp-nav .pagination-big-circle li a.active, #fp-nav .pagination-big-circle li a:hover {
  color: #fff;
}

.full-slide-bg {
  position: absolute;
  z-index: 0;
  width: 100%;
}

.full-slide {
  position: relative;
  -webkit-box-sizing: border-box;
     -moz-box-sizing: border-box;
          box-sizing: border-box;
  height: 100% !important;
  overflow: hidden;
  z-index: 1;
}
.full-slide.active {
  z-index: 99;
}
.full-slide.active .grid-stack {
  display: block !important;
}

.fp-tableCell {
  height: 100% !important;
}

.img-stretch {
  background-size: 100% 100%;
}

.img-tile {
  background-size: auto 100%;
  -webkit-background-size: auto 100%;
  background-position: center center;
  background-repeat: no-repeat;
}

.img-center {
  background-size: auto auto;
  -webkit-background-size: auto auto;
  background-position: center 0;
  background-repeat: no-repeat;
}

.grid-stack-wrap {
  height: 100%;
}

.grid-stack-main {
  margin: 20px auto;
  width: 1200px;
  background: transparent;
  z-index: 8;
  overflow: hidden;
  -webkit-box-sizing: border-box;
     -moz-box-sizing: border-box;
          box-sizing: border-box;
}

.editor-page .grid-stack-main {
  width: 1200px;
}

/*设置按钮的样式*/
.general-set {
  position: absolute;
  display: none;
  top: -37px;
  right: 4px;
  padding-bottom: 8px;
  z-index: 10;
}
.general-set > div {
  display: block;
  padding: 8px;
  color: #666;
  font-size: 12px;
  z-index: 10;
  cursor: pointer;
  width: 76px;
  height: 33px;
  background: white;
  -webkit-box-shadow: 0 0 5px 0 rgba(0, 0, 0, 0.3);
  box-shadow: 0 0 5px 0 rgba(0, 0, 0, 0.3);
  border-radius: 4px;
  text-align: center;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
       box-sizing: border-box;
}
.general-set .icon-set {
  margin-right: 4px;
  font-size: 16px;
}
.general-set span {
  display: inline-block;
  vertical-align: middle;
}

/*联系我的样式*/
#contact {
  position: fixed;
  bottom: 100px;
  left: 120px;
  z-index: 100;
}
#contact .module-set {
  left: 0;
}

/*十字架参考线*/
#fullpage {
  z-index: 5;
}
#fullpage > .swiper-wrapper {
  height: auto !important;
}
#fullpage .grid-stack-wrap {
  width: 1204px;
  margin: 0 auto;
}
#fullpage .grid-swiper-slide {
  min-height: 600px;
  background-repeat: no-repeat;
  height: 0 !important;
}
#fullpage .grid-swiper-slide.swiper-slide-visible {
  height: auto !important;
}
#fullpage:hover .general-set {
  display: block;
}
#fullpage .general-set {
  top: 0;
}
#fullpage .pagination .arrow {
  position: absolute;
  top: 50%;
  font-size: 40px;
  color: #fff;
  font-weight: bold;
}
#fullpage .pagination .arrow.disabled {
  opacity: 0.1;
}
#fullpage .pagination .arrow-left {
  left: 50%;
  margin-left: -640px;
}
#fullpage .pagination .arrow-right {
  left: 50%;
  margin-left: 640px;
}

.guides {
  position: absolute;
  display: none;
  z-index: 999;
  top: 50%;
  height: 1px;
  width: 100%;
  background: #3D82F2;
}

.editor-page .aid-groups {
  position: relative;
  z-index: 0;
}
.editor-page .aid-groups .fullpage-wrapper {
  z-index: 1;
}
.editor-page .aid-groups:hover:after {
  position: absolute;
  content: "";
  width: 1px;
  height: 100%;
  z-index: 10;
  background: #3D82F2;
  top: 0;
  left: 50%;
}
.editor-page .aid-groups:hover .guides {
  display: block;
}

@media only screen and (min-width: 1px) and (max-width: 926px) {
  html, body {
    height: auto !important;
    overflow: auto !important;
  }
  .content {
    margin-top: 50px;
  }
  .grid-stack-main {
    position: initial;
    top: 0;
    left: 0;
    transform: translate(0, 0);
    -webkit-transform: translate(0, 0);
    -moz-transform: translate(0, 0);
    -ms-transform: translate(0, 0);
    -o-transform: translate(0, 0);
    width: 100%;
    max-height: none !important;
  }
  .grid-stack-main .grid-stack {
    height: auto !important;
  }
  #fp-nav {
    display: none;
  }
  #contact {
    left: 0.2rem;
    bottom: 0.2rem;
  }
  .aid-groups,
  .full-slide,
  .fp-tableCell,
  #fullpage {
    height: auto !important;
  }
  #fullpage .grid-stack-wrap {
    width: 100%;
  }
  #fullpage {
    -webkit-transform: none !important;
    -ms-transform: none !important;
    transform: none !important;
  }
  .fp-tableCell {
    padding: 0 !important;
  }
  #footer {
    position: relative !important;
  }
  .grid-stack-item-content {
    border: none !important;
  }
}
.pop-nav .edit-page-bg .img-sizes > div {
  width: 92px;
}