var vue = new Vue({
  el: '#editor',
  data: {
    formDetail_course: true,  //表单详情--课程主页模板
    wfwfid: undefined,
    toastMessage: '',
    toastType: undefined,
    showToast: false,
    pageBg: {
      bgColor: '',  //默认背景颜色
    },
    pageId: undefined,
    webJson: undefined,
    jsonId: undefined,
    hasModefied: false,
    websiteId: "",

    baseurl: 'http://portal.chaoxing.com',
    setListDialog: false,  //设置弹窗
    checkLists: [],  //多选列表
    selectTypeList: [],     //设置弹窗字段排序
    selectTypeList1: [],     //设置弹窗字段排序
    dataSourceDialog: false,  //表单数据源选择弹窗
    dataSourceList: [],

    formId: undefined, //页面的表单id
    //多行文本的信息
    groupText:[],

    textTest: { //多行文本的测试数据
      value: '空数据'
    },
    imageTest: {
      value: '/assets/images/form_lists/test.png'
    },
    setDatas: {}, //弹窗的数据集合
    formDataIds: 0,
    pageName: '表单模板',
    ifChangeFormId: false,
    dialogRadioItem: {}, //数据弹窗暂存数据
    commentUrl: '',  //评论模块的地址
    tbPageid: '', //头底的pageid
    courselink: '',  //课程链接
    sectionlists: [
      {
        id: 1,
        name:'章节1',
        sequence: 1,
        subs: [
          {
            id: 11,
            name:'章节1-1',
            sequence: 1.1
          },
          {
            id: 12,
            name:'章节1-2',
            sequence: 1.2
          },
          {
            id: 13,
            name:'章节1-3',
            sequence: 1.3
          }
        ]
      },
      {
        id: 2,
        name:'章节2',
        sequence: 2,
        subs: [
          {
            id: 21,
            name:'章节2-1',
            sequence: 2.1
          },
          {
            id: 22,
            name:'章节2-2',
            sequence: 2.2
          },
          {
            id: 23,
            name:'章节2-3',
            sequence: 2.3
          }
        ]
      },
    ],
    sectionlists1: [],
    collectStatus: false, //课程收藏状态
    courseId: undefined,  //课程id
    courseVisit: 0,  //课程访问量
    showAll: false,//章节展示完全
      commentName: '',  //评论标题
      commentDialog: false, //评论设置弹窗
      commentTypeName: '',//评论资源类型
      resourceType: 5,  //课程
      showAppHeader: true,  //移动端是否展示头
      showAppFooter: true,  //移动端是否展示底
      dataSourceKeyword:''//数据源弹框搜索关键字
  },
  created: function () {
    this.pageId = getQueryString("pageId");
    this.jsonId = getQueryString("jsonId");
    this.websiteId = getQueryString("websiteId");

    getWfwfid(this);

    /* this.pageId = 47163;
     this.jsonId = 48458;
     this.websiteId = 38263;
     this.wfwfid = 67395;*/
    if (window.hasOwnProperty('formDataId')) {
      this.formDataIds = window['formDataId'];
    } else {
      this.formDataIds = 0;
    }

    this.getModuleJson();


  },
  mounted: function () {
    // 点击去除冒泡事件
    $("#pageTopWrap").on("click", function () {
      cancleBubble();
    })

    // 计算页面最低高度
    var screenH = document.documentElement.clientHeight;
    $('#editor').css('min-height', screenH);
    // 针对所有编辑页面，背景图片的拉伸问题
    $(window).on("resize", function () {
      var screenH = document.documentElement.clientHeight;
      $('#editor').css('min-height', screenH);
    });
    //测试假数据
    // if(this.sectionlists.length>0){
    //   this.toggleSection();
    // }
  },
  watch: {},
  methods: {
    //  数据源弹窗
    dataSourceFunc: function (id,isSearch) {
      var _this = this;
      if ( _this.dataSourceList.length > 0  && !isSearch) {
        if(id){
          for (var i = 0; i < _this.dataSourceList.length; i++) {
            if (_this.dataSourceList[i].id == id) {
              _this.dataSourceList[i].checked = true;
            } else {
              _this.dataSourceList[i].checked = false;
            }
          }
        }else{
          _this.dataSourceList[0].checked = true;
          _this.formId = _this.dataSourceList[0].id;
          _this.pageName = _this.dataSourceList[0].name;
        }

        _this.dataSourceDialog = true;
      }
      else {
        $.ajax({
          url: '/engine2/api/form',
          type: "post",
          data: {wfwfid: _this.wfwfid, w: _this.websiteId, keyword: _this.dataSourceKeyword},
          success: function (res) {
            if (res.code == 1) {
              _this.dataSourceList = res.data;
              _this.dataSourceDialog = true;
              if (!_this.formId) {
                _this.dataSourceList[0].checked = true;
                _this.formId = _this.dataSourceList[0].id;
                _this.pageName = _this.dataSourceList[0].name;
              //  获取默认的数据
                _this.getFirstData();
              } else {
                for (var i = 0; i < _this.dataSourceList.length; i++) {
                  if (_this.dataSourceList[i].id == id) {
                    _this.dataSourceList[i].checked = true;
                  }
                }
              }
            }
          }
        })
      }
    },
    selecSourceRadio: function (item) {
      var _this = this;
      for (var i = 0; i < _this.dataSourceList.length; i++) {
        _this.dataSourceList[i].checked = false;
      }
      _this.$nextTick(function () {
        item.checked = true;
        _this.dialogRadioItem = item;
        _this.ifChangeFormId = true;
      })
    },
    //关闭数据源弹窗
    closeDataSource: function () {
      var _this = this;
      if (_this.ifChangeFormId) {
        _this.formId = _this.dialogRadioItem.id;
        _this.pageName = _this.dialogRadioItem.name;
        _this.getFirstData();
      }
      _this.dataSourceDialog = false;
    },
    //获取模块JSON数据
    getModuleJson: function () {
      var _this = this;
      $.ajax({
        url: getContextPath() + "/webjson/" + _this.jsonId + "/edit-content" + getSfidFunc(),
        data: {
          wfwfid: _this.wfwfid
        },
        success: function (res) {
          if (res.code == 1) {
            if (res.data) {
              _this.webJson = JSON.parse(res.data.content);
              _this.pageBg = _this.webJson.setting.bg;
              _this.pageName = _this.webJson.setting.name;
              _this.tbPageid = _this.webJson.setting.tbPageid;
                _this.resourceType = _this.webJson.setting.resourceType || _this.resourceType;
                _this.commentTypeName = _this.webJson.setting.commentTypeName || _this.commentTypeName;
              _this.selectTypeList1 = _this.webJson.setting.myArray || _this.selectTypeList1;
                _this.showAppHeader = typeof(vue.webJson.setting.showAppHeader) == 'undefined' ? true : vue.webJson.setting.showAppHeader;
                _this.showAppFooter = typeof(vue.webJson.setting.showAppFooter) == 'undefined' ? true : vue.webJson.setting.showAppFooter;
              if (_this.webJson.setting.formId == undefined) {
                //数据源
                _this.dataSourceFunc();
              } else {
                _this.formId = _this.webJson.setting.formId;
                _this.getFirstData();
              }
              colorSet.themecolor = vue.webJson.setting.themeColor || "#3D82F2";
              colorSet.changeColor();
              _this.getHeadFoot();
            }
          }
        }
      })
    },
    //获取头底
    getHeadFoot: function(){
      var _this = this;
      $.ajax({
        url:getContextPath() + "/page/domain-page?domain=" + document.domain,
        success: function (res) {
          console.log(res)
          if(res.code == 1){
            _this.tbPageid = res.data.pageId;
            if (_this.tbPageid) {
              /*加载头底*/
              $("#mh-header").load("//mh.chaoxing.com/page/" + _this.tbPageid + "/1/top/div?hide=0&hideTitle=0&online=0",function (){
                  colorSet.themecolor = vue.webJson.setting.themeColor || "#3D82F2";
                  colorSet.changeColor();
                  setMinHeight({
                      type: 'min-height',
                      target: $("#items-1"),
                      height: $("#mh-header").height() + $("#mh-footer").height() + 40
                  })
                  setCursor("#mh-header");
              })
              $("#mh-footer").load("//mh.chaoxing.com/page/" + _this.tbPageid + "/footer/div",function (){
                  colorSet.themecolor = vue.webJson.setting.themeColor || "#3D82F2";
                  colorSet.changeColor();
                  setMinHeight({
                      type: 'min-height',
                      target: $("#items-1"),
                      height: $("#mh-header").height() + $("#mh-footer").height() + 40
                  })
                  setCursor("#mh-footer");
              })
            }
          }
        }
      })
    },
    getCoordinates: function (type) {
      var _this = this;
        if(!_this.commentTypeName){
            _this.showToastEvent('评论的资源类型不能为空', 'failure');
            _this.commentDialog = true;
            return;
        }
      _this.$nextTick(function () {
        var serialized_datas = {
          setting: {
            "type": "form_lists_course",
            "editor": "/page/form_lists_course/editor.html",
            "preview": "/page/form_lists_course/index.html",
            "bg": _this.pageBg,
            "themeColor": colorSet.themecolor,
            'formId': _this.formId,
              'myArray': _this.selectTypeList1,
            'name': _this.pageName,
            'tbPageid': _this.tbPageid,
              'resourceType': _this.resourceType,
            'commentTypeName': _this.commentTypeName,
              'showAppHeader': _this.showAppHeader,
              'showAppFooter': _this.showAppFooter
          },
        };
        var param = {
          id: _this.jsonId,
          content: undefined
        };
        param.content = JSON.stringify(serialized_datas);
        _this.savaPageJson(param, type);
      })
    },
    //调用toast
    showToastEvent: function (message, type) {
      var _this = this;
      _this.toastMessage = message;
      _this.toastType = type;
      _this.showToast = true;
      setTimeout(function () {
        _this.showToast = false;
      }, 2000)
    },
    // 更新背景
    updateBg: function (bg) {
      var _this = this;
      _this.pageBg = bg;
    },
    //点击设置
    setFunc: function () {
          var _this = this;
          if(_this.checkLists.length>0){
              _this.setListDialog = true;
              setTimeout(function () {
                  if(_this.selectTypeList.length>0){
                      _this.listSortFunc();
                  }
              },300)
          }
          else{
              $.ajax({
                  url: '/engine2/api/form-cloumn',
                  type: "post",
                  data: {wfwfid: _this.wfwfid, formId: _this.formId, w: _this.websiteId},
                  success: function (res) {
                      if (res.code == 1) {
                          var resData = res.data;
                          //组合字段
                          _this.checkLists = resData;
                          if(_this.selectTypeList1.length < 1){  //第一次打开设置弹窗或者无数据打开设置弹窗
                              _this.checkLists.forEach(function (item1,index1) {
                                  if(index1<2){
                                      item1.checked = true;
                                      item1.disabled = false;
                                      _this.selectTypeList.push(item1);
                                  }else{
                                      item1.checked = false;
                                      item1.disabled = true;
                                  }
                              })
                              _this.$nextTick(function () {
                                  _this.setListDialog = true;
                                  setTimeout(function () {
                                      if(_this.selectTypeList.length>0){
                                          _this.listSortFunc();
                                      }
                                  },300)
                              })
                          }
                          else{
                              if(_this.selectTypeList1.length===2){
                                  _this.checkLists.forEach(function (item1,index1) {
                                          item1.checked = false;
                                          item1.disabled = true;
                                  })
                              }
                              //回显数据的设置
                              for(var i=0;i<_this.checkLists.length;i++){
                                  for(var j=0; j< _this.selectTypeList1.length; j++){
                                      if(_this.checkLists[i].id == _this.selectTypeList1[j].id){
                                          _this.checkLists[i].checked = true;
                                          _this.checkLists[i].disabled = false;
                                      }
                                  }
                              }
                              _this.selectTypeList=[].concat(_this.selectTypeList1);
                              _this.$nextTick(function () {
                                  _this.setListDialog = true;
                                  setTimeout(function () {
                                      _this.listSortFunc();
                                  },300)
                              })
                          }
                      }
                  }
              })
          }
      },
      //设置弹窗-- 多选
      selectCheckbox: function (item) {
          var _this = this;
          _this.$nextTick(function () {
              if(item.checked){
                  item.checked = false;
                  _this.checkLists.forEach(function (item1) {
                      if(!item1.checked){
                          item1.disabled = false;
                      }
                  })
                  for(var i=0;i<_this.selectTypeList.length;i++){
                      if(_this.selectTypeList[i].id == item.id){
                          _this.selectTypeList.splice(i,1)
                      }
                  }
              }
              else{
                  item.checked = true;
                  _this.selectTypeList.push(item);
                  if(_this.selectTypeList.length>1){
                      _this.listSortFunc();
                  }
                  if(_this.selectTypeList.length > 1){
                      _this.checkLists.forEach(function (item2) {
                          if(!item2.checked){
                              item2.disabled = true;
                          }
                      })
                  }
              }
              _this.selectTypeList1 =  _this.selectTypeList;
          })
      },
    //排序拖拽
    listSortFunc: function () {
      var _this = this;
      var sortList = document.getElementById('formSort');
      var sortable3 = new Sortable(sortList, {
          handle: '.typehandle',
          animation: 150,
          //*********  拖拽位置发生改变的事件件 *********
          onEnd: function (evt) {
              var lists = $('#formSort').find('.sort-list');
              var arr = [];
              for(var i=0;i<lists.length;i++){
                  var id = $(lists[i]).attr('data-id');
                  _this.selectTypeList.forEach(function (item) {
                      if(item.id==id){
                          arr.push(item);
                      }
                  })
              }
              _this.selectTypeList1=[];
              _this.selectTypeList1 = arr;
              _this.selectTypeList = arr;
          }
      });
  },
    // 保存页面的JSON文件
    savaPageJson: function (param, type) {
      var _this = this;
      _this.hasModefied = false;
      param = _this.checkDataVersion(param, type);
      var actionable = _this.saveThrottle(type);
      if (!actionable) {
        _this.showToastEvent('请5秒后再次操作', 'failure');
        return
      }
      var status = 0;
      param.content = mhxssEncodeFunc(param.content);
      $.ajax({
        url: getContextPath() + "/webjson/update?status=" + status,
        async: false,
        type: 'post',
        data: param,
        success: function (res) {
          if (res.code == 1) {
            switch (type) {
              case 'save':
                _this.showToastEvent('保存成功', 'success');
                break;
              default:
                var showUrl;
                showUrl = "/page/" + _this.pageId + '/' + _this.formDataIds + "/form";
                window.open(getContextPath() + showUrl, "_blank");
            }
          } else {
            _this.showToastEvent(res.message, 'failure');
          }
        }
      })
    },
    // 防止连续点击保存，消耗性能
    saveThrottle: function (type) {
      var timestamp = Date.parse(new Date());
      var lastSaveInfo = JSON.parse(sessionStorage.getItem('saveInfo'));
      var actionable = true;
      if (type !== 'save' && type !== 'scan') {
        return actionable
      }
      if (lastSaveInfo && lastSaveInfo.type == type && timestamp - lastSaveInfo.timestamp < 5 * 1000) {
        actionable = false;
      } else {
        sessionStorage.setItem('saveInfo', JSON.stringify({
          type: type,
          timestamp: timestamp
        }));
      }
      return actionable
    },
    //设置保存获取真实数据
    formData: function () {
        var _this = this;
        _this.selectTypeList1 =  _this.selectTypeList;
        // 获取排序顺序
        $.ajax({
              url: '/engine2/api/form-datas',
              type: "post",
              data: {wfwfid: _this.wfwfid, formId: _this.formId, version: 2, w: _this.websiteId},
              success: function (res) {
                  if (res.code == 1) {
                      var resData = res.data;
                      if (resData.results[0]) {
                          _this.formDataIds = res.data.results[0].id;
                          var realData=[];
                          realData = res.data.results[0].fields;
                          _this.$nextTick(function () {
                              // 对排序的数据进行整理
                              if(_this.selectTypeList1.length>0){
                                  //根据排序顺序修改顺序
                                  var arr1 = [],arr2 = [];
                                  for (var i=0;i< _this.selectTypeList1.length;i++){
                                      arr1.push(Number(_this.selectTypeList1[i].id));
                                  }
                                  for(var j=0;j<arr1.length;j++){
                                      realData.forEach(function (item) {
                                          if(item.flag == arr1[j]){
                                              arr2[j] = item;
                                              return;
                                          }
                                      })
                                  }
                                  realData = arr2;
                              }
                              _this.$nextTick(function () {
                                  // 保存数据
                                  _this.groupText[1] = realData[0];
                                  _this.groupText[2] = realData[1];

                                  _this.$nextTick(function () {
                                      _this.closeSetDialog();
                                  })
                              })
                          })
                      }else{
                          _this.showToastEvent('字段无数据！', 'failure');
                          _this.closeSetDialog();
                      }
                  }
              }
          })
      },
    //关闭设置弹窗
    closeSetDialog: function () {
        var _this = this;
        _this.setListDialog = false;
    },
    // 保存数据前检查当前数据版本是不是最新的，避免覆盖掉中途保存的数据
    // 版本号的比较是再后端进行，提示弹窗由后端处理。
    checkDataVersion: function (param, type) {
      var _this = this;
      var data = param.content;
      var newData = JSON.parse(data);
      if (!_this.webJson.hasOwnProperty("version")) {  //不存在版本号则默认为1
        newData.version = 1;
      } else {  //有版本号则递加
        newData.version = vue.webJson.version + 1;
      }
      vue.webJson.version = newData.version;
      data = JSON.stringify(newData);
      return {
        content: data,
        id: param.id
      }
    },
    // 获取第一条子表单数据
    getFirstData: function () {
      var _this = this;
      var param = {
        wfwfid: _this.wfwfid,
        formId: _this.formId,
        pageSize: 1,
        w: _this.websiteId,
      };
      $.ajax({
        url: '/engine2/form/api/datas',
        type: "post",
        dataType: "json",
        contentType: "application/json",
        data: JSON.stringify(param),
        success: function (res) {
          if (res.code == 1) {
            if (res.data.results&&res.data.results[0] && res.data.results[0].fields) {
              var fields = res.data.results[0].fields;
              _this.groupText = [];
              //清空裁剪
              $('.text-box').find('.js-crop-img .jqthumb').remove();
              Object.keys(fields).forEach(function (item1) {
                if (fields[item1].key == '封面海报') {
                  _this.groupText[0] = {
                    key:fields[item1].key,
                    value:fields[item1].value
                  }
                  setTimeout(function () {
                    cropImgFun($('.text-box'));
                  },200)
                }
                else if (fields[item1].key == '课程名称') {
                    _this.groupText[1] = {
                        key:fields[item1].key,
                        value:fields[item1].value
                    }
                }
                else if (fields[item1].key == '授课教师') {
                    _this.groupText[2] = {
                        key:fields[item1].key,
                        value:fields[item1].value
                    }
                }
                else if (fields[item1].key == '课程简介') {
                  _this.groupText[3] = {
                    key:fields[item1].key,
                    value:fields[item1].value
                  }
                }
                else if (fields[item1].key == '选择课程') {
                  _this.courseId = fields[item1].value;
                  _this.courselink = fields[item1].url;
                }
              })
                //自定义值存在的时候
                if(_this.selectTypeList1.length > 0){
                    _this.groupText[1] = {
                        key:fields[_this.selectTypeList1[0].id].key,
                        value:fields[_this.selectTypeList1[0].id].value
                    }
                    _this.groupText[2] = {
                        key:fields[_this.selectTypeList1[1].id].key,
                        value:fields[_this.selectTypeList1[1].id].value
                    }
                }

            }
            if (res.data.results&&res.data.results[0] && res.data.results[0].id) {
              _this.formDataIds = res.data.results[0].id;
            }

            //获取评论的链接地址
              if(_this.groupText[1]&&_this.groupText[1].value){
                  _this.commentName = _this.groupText[1].value;
                  _this.getIframeAddr();
              }else{
                  _this.getIframeAddr();
              }

            _this.getSectionFunc();
            _this.getCourseVisit();
          }
        }
      })
    },

    //获取课程章节
    getSectionFunc: function (){
      var _this = this;
      // _this.courseId = 217959450;
      $.ajax({
        url: '/engine2/course/node-list?courseId='+_this.courseId,
        type: "post",
        data: {formId: _this.formId, w: _this.websiteId},
        success: function (res) {
          if(res.code === 1 && res.data.results.length>0){
            _this.sectionlists = res.data.results;
            _this.$nextTick(function () {
              _this.toggleSection();
            })
          }
        }
      })
    },
    //接口获取评论地址---第三方
    getIframeAddr: function (){
      var _this = this;
        if(!_this.commentTypeName){
            _this.commentDialog = true;
            return false;
        }
      $.ajax({
        url: '/engine2/api/wuhan/comment-normal',
        data: {
            dataId: _this.formId + '_' + _this.formDataIds,
            name: _this.commentName,
            resourceType: _this.resourceType,
            resourceTypeName: _this.commentTypeName,
            websiteId: _this.websiteId
        },
        success: function (res) {
          _this.commentUrl = res;
        }
      })
    },


    //输入头底pageid
    updatePid1: function (id) {
      this.tbPageid = id;
    },

    //切换章节
    toggleSection: function () {
     $('.section-list-box .list .list-title .icon-up').on('click', function (e) {
       e.preventDefault();
       var next = $(this).parent().next();
       var ifhide = next.hasClass('hide');
       if(ifhide){
         next.removeClass('hide');
         $(this).removeClass('rotate180');
         next.slideDown();
       }else{
         next.addClass('hide');
         $(this).addClass('rotate180');
         next.slideUp();
       }
     })
    },
    //获取课程访问量
    getCourseVisit: function () {
      var _this = this;
      $.ajax({
        url: '/engine2/course/info?courseId='+_this.courseId,
        type: "post",
        data: {formId: _this.formId, w: _this.websiteId},
        success: function (res) {
          if(res.code === 1){
            _this.courseVisit = res.data.viewtimes;
          }
        }
      })
    },
    //  章节展示全部
    showAllSection: function () {
      this.showAll = true;
    },
      //评论弹窗保存
      updateName: function (value,value1) {
          this.commentDialog = false;
          this.commentTypeName = value;
          this.resourceType = value1;
          this.getIframeAddr();
      },
      updateAppT: function (val) {
          this.showAppHeader = val;
      },
      updateAppB: function (val) {
          this.showAppFooter = val;
      },
  }
});
