// 用于首页的交互

var vue = new Vue({
    el: '#index',
    data: {
        pageId: undefined,
        jsonId: undefined,
        webJson: undefined,
        websiteId: "",
        showHeader:true,
        showFooter:true,
        showBanner:true,
        showBayWindow:false,
        activePage: undefined,
        bgTransparency: '100%', //背景透明度
        showEditBg: false,
        showEditName: false,
        activeId: '',
        curBgInfo: null,
        wfwfid: undefined,
        myModules: [],
        modeluMinWidth: 8,  //模块的几个尺寸设置
        defaultWidth: 50,
        modeluMinHeight: 3,
        defaultHeight: 30,
        toastMessage: '',
        toastType: undefined,
        showToast: false,
        defaultBg: "/assets/images/default-img.png",
        defaultColors: [  //展示的颜色列表
            '#c01111', '#ba2684', '#63065F', '#4343c5', '#2c7fcc', '#22aabe', '#19a17c', '#347939', '#dbbd34', '#ef8b20', '#e66219', '#ed480f', '#db2323', '#000000'
        ],
        pageBgs: [],
        moduleCoordinate: [], //中间模块的坐标列表
        leftNavs: {},
        leftNavSets:{},
        activePageIndex:1,
        basePlugs:[{
            jsComponent:'mCustomScrollBar',
            dependentList:""
        }],
        musicStyle: {
            show: false,
            type: 1,
            loop:false,
            online: false,
            name:'',
            url:'',
        },
    },
    created: function () {
        this.pageId = getQueryString("pageId");
        this.jsonId = getQueryString("jsonId");
        this.websiteId = getQueryString("websiteId");
        getWfwfid(this);
        // 先判断父页面是否有webjson
        if (window.hasOwnProperty('p_webjson')) {
            this.webjsonData(window['p_webjson']);
        }else{
            this.getModuleJson();
        }
    },
    mounted: function () {
        var _this = this;
        this.closePops();
        // indexPage.littleModuleClick();
        this.$nextTick(function () {
            _this.paginationHover();
        });
        $(window).on("resize",function () {
            indexPage.resizeUpdate();
        })

    },
    methods: {
        // 设置左右滚动
        initFullPage: function () {
            var mySwiper = new Swiper('#fullpage',{
                autoplay : false,//可选选项，自动滑动
                loop : false,//可选选项，开启循环
                paginationClickable: true,
                observer: true,
                observeParents: true,
                simulateTouch : false,  //禁止鼠标滑动切换内容
                onSlideChangeEnd: function(swiper){
                    if(swiper.activeIndex==0){
                        $('#gridPagination .arrow-left').addClass("disabled");
                        $('#gridPagination .arrow-right').removeClass("disabled");
                    }else if(swiper.activeIndex+1 == vue.leftNavs.length){
                        $('#gridPagination .arrow-left').removeClass("disabled");
                        $('#gridPagination .arrow-right').addClass("disabled");
                    }
                }
            });
            mySwiper.resizeFix(); //处理swiper未能全屏的问题
            $('#gridPagination .arrow-left').on('click', function(e){
                e.preventDefault();
                mySwiper.swipePrev();
                setTimeout(function () {
                    $(window).trigger("resize");
                }, 100);
            });
            $('#gridPagination .arrow-right').on('click', function(e){
                e.preventDefault();
                mySwiper.swipeNext();
                setTimeout(function () {
                    $(window).trigger("resize");
                }, 100);
            });
        },
        //关闭编辑背景弹框
        closeEditBgPop: function () {
            this.showEditBg = false;
            this.curBgInfo = null;
        },
        //点击空白区域，关闭编辑背景和颜色弹框
        closePops: function () {
            var _this = this;
            $(".pages-list").on("click", ".statu2", function () {
                cancleBubble();
            });
            $(".sp-replacer").on("click", function (e) {
                cancleBubble();
            });
            $(".sp-container").on("click", function (e) {
                cancleBubble();
            });
            $(".edit-page-bg").on("click", function () {
                cancleBubble();
                $(".sp-choose").trigger("click");
            });
            $(window).on("click", function () {
                _this.closeEditBgPop();
                $(".pages-set").slideUp(200);
                $(".pages .expanded").removeClass("expanded");
                $(".sp-choose").trigger("click");
            })
        },
        // 中间画布的初始化
        canvasInit: function () {
            var _this = this;
            var options = {
                width: 100,
                cellHeight: 10,
                verticalMargin: 0,
                itemMinWidth: this.modeluMinWidth,
                itemMinHeight: this.modeluMinHeight,
                float: false,
                handle: '.drag-nav',
                removable: '.trash',
                removeTimeout: 100,
                resizable: {autoHide: true, handles: 'n,e,s,w',disabled: true},
                acceptWidgets: '.grid-stack-item'
            };
            $('.grid-stack').gridstack(options);
            this.newWidgetCallback();
        },
        // 画布添加模块之后的回调
        newWidgetCallback: function () {
            var _this = this;
            $('.grid-stack').on("added", function (event, items) {
                _this.activeId = $(items[0].el).attr("data-app-id");
                indexPage.setLayoutSize(items);
            });
        },
        //调用toast
        showToastEvent: function (message, type) {
            var _this = this;
            this.toastMessage = message;
            this.toastType = type;
            this.showToast = true;
            setTimeout(function () {
                _this.showToast = false;
            }, 2000)
        },
        //获取模块JSON数据
        getModuleJson:function () {
            if(!this.jsonId){
                return
            }
            var _this=this;
            var baseUrl = window['isCDN']?'//static.mh.chaoxing.com':'';
            $.ajax({
                // url: getContextPath() + "/webjson/"+_this.jsonId+"/content"+getSfidFunc(),
                url: baseUrl+getContextPath() + "/webjson/" + this.jsonId + "/content-cache?w=" + _this.websiteId + getSfidFunc(1),
                data:{
                    wfwfid:this.wfwfid
                },
                success:function (res) {
                    if(res.code==1 ){
                        _this.webjsonData(res.data.content);
                    }
                }
            })
        },
        // webjson赋值
        webjsonData: function (data) {
            var vue = this;
            var content=JSON.parse(data);
            if(!content.aid){
                return
            }
            vue.webJson=content;
            vue.pageBgs =Array.isArray(vue.webJson.setting.bg)? vue.webJson.setting.bg : vue.webJson.setting.bg.multiBgs;
            vue.bgMode =  vue.webJson.setting.bg.bgMode || 2;
            indexPage.hasPlugList=vue.webJson.setting.plugs || [];
            indexPage.demandLoading();
            vue.showHeader=typeof(vue.webJson.setting.hasHeader) == 'undefined' ?true : vue.webJson.setting.hasHeader;
            vue.showFooter=typeof(vue.webJson.setting.hasFooter) == 'undefined' ?true : vue.webJson.setting.hasFooter;
            vue.showBanner=typeof(vue.webJson.setting.hasBanner) == 'undefined' ?true : vue.webJson.setting.hasBanner;
            vue.showBayWindow = typeof(vue.webJson.setting.showBayWindow) == 'undefined' ? false : vue.webJson.setting.showBayWindow;
            colorSet.themecolor=vue.webJson.setting.themeColor || "#3D82F2";
            colorSet.changeColor();
            vue.$nextTick(function () {
                vue.getLeftNavs();
                vue.getSid();
                // vue.$refs.bayWindow.init();
                if(vue.showBayWindow){
                    vue.$refs.bayWindow.init();
                }
                // 背景音乐
                if(vue.webJson.setting.musicStyle && vue.webJson.setting.musicStyle.show){
                    vue.musicStyle = vue.webJson.setting.musicStyle;
                    vue.$refs.bgMusic.init(vue.webJson.setting.musicStyle);
                }
            });
        },
        //获取左侧导航
        getLeftNavs:function () {
            var _this=this;
            if(vue.webJson.setting.hasOwnProperty('leftNavs')){
                this.leftNavSets = vue.webJson.setting.leftNavSets;
                this.leftNavs = vue.webJson.setting.leftNavs;
                // 自定义导航颜色
                if( this.leftNavSets.backgroudType===1){
                    var cssRule = [{
                        selector: '#fp-nav .bg-custom',
                        rule: 'background:' + _this.leftNavSets.backgroudColor + '!important;'
                    }];
                    updateColor.updateCSSRule(cssRule);
                }
                this.$nextTick(function (){
                    _this.canvasInit();
                    if (vue.webJson.aid.length > 0) {
                        _this.canvasContentShow();
                        // indexPage.showLayoutContent();
                    }
                    indexPage.getDatas();
                    _this.initFullPage();
                })

                return
            }
            $.ajax({
                url: '/engine2/navigation/zjjyss/div'+getSfidFunc(),
                data:{
                    "pageId":this.pageId,
                    wfwfid:this.wfwfid
                },
                success:function (res) {
                    if(res.code==1){
                        _this.leftNavSets=res.data.navigation;
                        _this.leftNavs=res.data.navigation.itemList;
                        // 自定义导航颜色
                        if( _this.leftNavSets.backgroudType===1){
                            var cssRule = [{
                                selector: '#fp-nav .bg-custom',
                                rule: 'background:' + _this.leftNavSets.backgroudColor + '!important;'
                            }];
                            updateColor.updateCSSRule(cssRule);
                        }
                        _this.$nextTick(function () {
                            _this.canvasInit();
                            _this.setModuleData();
                            _this.initFullPage();
                        });
                    }
                }
            })
        },
        //根据导航数据进行添加，删除
        setModuleData:function () {
            var newAids=[];
            var _this=this;
            var aids = _this.webJson.aid;
            this.leftNavs.forEach(function (item,index) {
                var navId=item.id;
                var isExist=false;
                for(var i=0;i<aids.length;i++){
                    var jsonId=aids[i].navId;
                    if(navId==jsonId){
                        isExist=true;
                        newAids.push(aids[i]);
                        break
                    }
                }
                if(!isExist){
                    newAids.push({
                        "page": 1,
                        "app": [],
                        "navId":navId
                    });
                }
            });
            this.webJson.aid=newAids;
            if(this.webJson.aid.length>0){
                this.canvasContentShow();
                // indexPage.showLayoutContent();
                indexPage.getDatas();
            }
        },
        //获取sid的内容
        getSid: function () {
            // if(this.showHeader || this.showFooter){
            //     indexPage.getHeadFooter(this.setGridMiddle);
            // }
            indexPage.getSidContent("/engine2/navigationcontact/div?pageId="+this.pageId,$("#contact"), '/engine2/navigationcontact/admin/content?pageId='+this.pageId);
        },

        //画布中模块位置的回显
        canvasContentShow:function () {
            var _this=this;
            $(".grid-stack").each(function (index,value) {
                if(!vue.webJson.aid[index]){
                    return
                }
                var items=vue.webJson.aid[index].app;
                var grid = $(this).data('gridstack');
                jsonSort(items,'y','x');
                items.forEach(function (node) {
                    var moduleHtml='',container;
                    if(node.baseModule) {
                        node.baseModule.forEach(function (item, index) {
                            moduleHtml += item.html;
                        });
                    }
                    container='<div data-app-id="' + node.appId + '" class="grid-stack-item"><div class="grid-stack-item-content">'+moduleHtml+'</div></div>';
                    grid.addWidget($(container),
                        node.x, node.y, node.width, node.height);
                    indexPage.reSetDrag(".grid-stack");
                    indexPage.delSet();
                    indexPage.toggleDragItem(false);
                });
            });
            // baseModule.baseModuleDragOrResize();  //基础组件的拖拽初始化
        },
        // 滚动关闭列表,这个方式是针对某个样式在当模板的操作，即滚动的时候关闭select2下拉列表
        // scrollCloseList:function () {
        //     scrollCloseSelectList();
        // },
        // 左侧导航样式3下的hover样式
        paginationHover:function () {
            $("#fp-nav").on("mouseenter",".pagination-big-circle a",function () {
                var color = $(".pagination-big-circle .active").css("background-color");
                var colorRGB=colorSet.hexToRgb(color);
                var cssRule = [{
                    selector: '.pagination-hover-style',
                    rule: 'background:rgba('+colorRGB.num+',0.5)' + '!important;'
                }];
                updateColor.updateCSSRule(cssRule);
                if(!$(this).hasClass("active")){
                    $(this).addClass("pagination-hover-style");
                }
            }).on("mouseleave",".pagination-big-circle a",function () {
                $(this).removeClass("pagination-hover-style");
            })
        }
    }
});


