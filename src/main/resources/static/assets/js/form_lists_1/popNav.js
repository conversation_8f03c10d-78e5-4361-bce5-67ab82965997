// 用于通用的顶部设置部分

var popNav = Vue.component('popNav', {
    props: ['bg', 'webjson','showheaderfooter', 'tbPageid1'],
    template: '<div class="pop-nav" id="popupNav"><div class="menus">\n' +
        '            <div class="pop-nav-content">\n' +
        '                <div class="drag-area pop-nav-top clear">\n' +
        '                    <ul class="tabs fs16 fl">\n' +
        '                        <li class="active" @click="setTabChange($event)">模块</li>\n' +
        '                        <li @click="setTabChange($event)" @click.once="openColorPanel()">设置</li>\n' +
        '                    </ul>\n' +
        '                    <a class="fr" id="scaleLeft" href="javascript:;"><span class="icon-del"></span></a>\n' +
        '                </div>\n' +
        '\n' +
        '                <div class="tab-con-wrap">\n' +
        '                    <div class="tab-con">\n' +
        '                    <ul id="leftModules">\n' +
        '                        <li id="myModules" class="modules-wrap">\n' +
        '                            <div class="modules drag-modules" id="items-2">\n' +
        '                                <div v-if="isFormDetail_need" v-for="item in baseModule1" :key="item.id"\n' +
        '                                     :data-app-id="item.id"  class="default-dom">\n' +
        '                                        <img :src="item.iconUrl">\n' +
        '                                        <p class="module-name">{{item.name}}</p>\n' +
        '                                </div>\n' +
        '                                <div v-if="isFormDetail_live" v-for="item in baseModule2" :key="item.id"\n' +
        '                                     :data-app-id="item.id"  class="default-dom">\n' +
        '                                        <img :src="item.iconUrl">\n' +
        '                                        <p class="module-name">{{item.name}}</p>\n' +
        '                                </div>\n' +
        '                                <div v-if="isFormDetail_picture" v-for="item in baseModule3" :key="item.id"\n' +
        '                                     :data-app-id="item.id"  class="default-dom">\n' +
        '                                        <img :src="item.iconUrl">\n' +
        '                                        <p class="module-name">{{item.name}}</p>\n' +
        '                                </div>\n' +
        '                                <div v-if="isFormDetail_store" v-for="item in baseModule4" :key="item.id"\n' +
        '                                     :data-app-id="item.id"  class="default-dom">\n' +
        '                                        <img :src="item.iconUrl">\n' +
        '                                        <p class="module-name">{{item.name}}</p>\n' +
        '                                </div>\n' +
        '                                <div v-if="isFormDetail_venue" v-for="item in baseModule5" :key="item.id"\n' +
        '                                     :data-app-id="item.id"  class="default-dom">\n' +
        '                                        <img :src="item.iconUrl">\n' +
        '                                        <p class="module-name">{{item.name}}</p>\n' +
        '                                </div>\n' +
        '                                <div v-if="isFormDetail_order" v-for="item in baseModule6" :key="item.id"\n' +
        '                                     :data-app-id="item.id"  class="default-dom">\n' +
        '                                        <img :src="item.iconUrl">\n' +
        '                                        <p class="module-name">{{item.name}}</p>\n' +
        '                                </div>\n' +
        '                                <div v-if="isFormList_1" v-for="item in baseModule" :key="item.id"\n' +
        '                                     :data-app-id="item.id"  class="default-dom">\n' +
        '                                        <img :src="item.iconUrl">\n' +
        '                                        <p class="module-name">{{item.name}}</p>\n' +
        '                                </div>\n' +
        '                            </div>\n' +
        '                        </li>\n' +
        '                        </ul>\n' +
        '                    </div>\n' +
        '                    <div class="tab-con" style="display: none">\n' +
        '                        <!--设置背景-->\n' +
        '                        <div class="set-bgs">\n' +
        '                            <h4 class="col333 fs14">背景设置</h4>\n' +
        '                            <ul v-if="!bgNo">\n' +
        '                                <li class="bg-set-item fs12" >\n' +
        '                                    <div class="clear">\n' +
        '                                        <div class="fl">\n' +
        '                                            <span class="bg-color" :style="{background:bg.bgColor}"></span>\n' +
        '                                            <span class="col666">背景</span>\n' +
        '                                        </div>\n' +
        '                                        <div class="icons fr colccc fs16">\n' +
        '                                            <i class="icon icon-edit" @click="showEditBgPop(bg)"></i>\n' +
        '                                            <i class="icon icon-rubbish colccc" @click="delPageColor()"></i>\n' +
        '                                        </div>\n' +
        '                                    </div>\n' +
        '                                </li>\n' +
        '                            </ul>\n' +
        '                            <div class="add-btn fs14" @click="showEditBgPop()" v-if="bgNo">\n' +
        '                                <i class="icon-add"></i>添加\n' +
        '                            </div>\n' +
        '                        </div>\n' +
        '                        <!--选择主题色-->\n' +
        '                        <div class="set-colors">\n' +
        '                            <h4 class="col333 fs14">主题色</h4>\n' +
        '                            <div class="left_color">\n' +
        '                                <ul class="colors">\n' +
        '                                    <li v-for="color in defaultColors" :style="{background:color}"\n' +
        '                                        @mouseenter="colorMouseenter(color,$event)"\n' +
        '                                        @mouseleave="colorMouseleave(color,$event)"\n' +
        '                                        @click="selectColor(color)"></li>\n' +
        '                                </ul>\n' +
        '                            </div>\n' +
        '                            <div id="pegeThemeColor" class="themeCustom clearfix">\n' +
        '                                <div class="themeCustom-title">\n' +
        '                                    <span class="fs12 col666">自定义：</span>\n' +
        '                                    <input id="full" @click="openColorPanel" style="display: none;"/>\n' +
        '                                </div>\n' +
        '                                <div class="customColor customColor-theme clearfix"></div>\n' +
        '                            </div>\n' +
        '                        </div>\n' +
        '                        <!--头底设置-->\n' +
        '                        <div class="set-topfoot" style="display:none;">\n' +
        '                            <label class="set-label col333 fs14">引用头底</label>\n' +
        '                            <input type="text" class="input-pageid" v-model="inputPageid" @blur="changePageid()">' +
        '                            <p class="col666 fs12 margint16">请填入需引用头底所在页面的pageid</p>\n' +
        '                        </div>\n' +
        '                        <!--宽度设置-->\n' +
        '                        <div class="set-pagewidth" v-if="isFormList_1">\n' +
        '                            <label class="set-label col333 fs14">页面宽度</label>\n' +
        '                            <input type="number" class="input-pagewidth" v-model="pageWidth" @blur="changePageWidth()" placeholder="页面宽度900-1400px" min="900" max="1400">' +
        '                            <p class="col666 fs12 margint16">页面宽度需要在900px-1400px之间</p>\n' +
        '                        </div>\n' +
        '                        <div v-if="isFormList_1 || isFormDetail_venue">' +
        '                            <h4 class="col333 fs14">展示头底</h4>\n' +
        '                            <div>' +
        '                                 <label class="normal-switch"><input type="checkbox" class="scrollCheckbox" id="showTBSwitch" :checked="showTBStatu" v-model="showTBStatu"> ' +
        '                                    <div class="slider"><em class="open">开</em><em class="close">关</em></div>' +
        '                                 </label>' +
        '                            </div>' +
        '                        </div>' +
        '                        <div>' +
        '                        <div>' +
        '                            <h4 class="col333 fs14">显示移动端头部</h4>\n' +
        '                            <div>' +
        '                                 <label class="normal-switch"><input type="checkbox" class="scrollCheckbox" id="showAppTop" :checked="showAppTopStatu" v-model="showAppTopStatu"> ' +
        '                                    <div class="slider"><em class="open">开</em><em class="close">关</em></div>' +
        '                                 </label>' +
        '                            </div>' +
        '                        </div>' +
        '                        <div>' +
        '                            <h4 class="col333 fs14">显示移动端底部</h4>\n' +
        '                            <div>' +
        '                                 <label class="normal-switch"><input type="checkbox" class="scrollCheckbox" id="showAppBottom" :checked="showAppBottomStatu" v-model="showAppBottomStatu"> ' +
        '                                    <div class="slider"><em class="open">开</em><em class="close">关</em></div>' +
        '                                 </label>' +
        '                            </div>' +
        '                        </div>' +
        '                        <div>' +
        '                            <h4 class="col333 fs14">开启分享功能</h4>\n' +
        '                            <div>' +
        '                                 <label class="normal-switch"><input type="checkbox" class="scrollCheckbox" id="showAppShare" :checked="showShareStatu" v-model="showShareStatu"> ' +
        '                                    <div class="slider"><em class="open">开</em><em class="close">关</em></div>' +
        '                                 </label>' +
        '                            </div>' +
        '                        </div>' +
        '                       </div>' +
        '                    </div>\n' +
        '                </div>\n' +
        '            </div>\n' +
        '        </div>\n' +
        '        <div class="suspend-icon drag-area hide"><span class="icon-pack-down"></span></div>\n' +
        '        <!--添加/修改页面背景-->\n' +
        '        <div class="edit-page-bg" v-show="showEditBg">\n' +
        '            <div class="top clear">\n' +
        '                <div class="fs14 col333 fl">{{curBgInfo?\'编辑\':\'添加\'}}</div>\n' +
        '                <i class="fr fs12 colccc icon-del" @click="closeEditBgPop"></i>\n' +
        '            </div>\n' +
        '            <div class="edit-page-con">\n' +
        '                <ul class="tabs col999 fs14">\n' +
        '                    <li class="active">颜色</li>\n' +
        '                </ul>\n' +
        '                <div class="bg-styles">\n' +
        '                    <div class="fs14 col999 bg-color-set">\n' +
        '                        <div class="bg-Custom clearfix">\n' +
        '                            <div class="bg-Custom-title">\n' +
        '                                <input id="bgColor" style="display: none;"/>\n' +
        '                            </div>\n' +
        '                        </div>\n' +
        '                        <div class="customColor customColor-theme"></div>\n' +
        '                        <div class="txt-btn in-block">宽度\n' +
        '                            <input type="text" value="100%" disabled>\n' +
        '                        </div>\n' +
        '                        <div class="txt-btn in-block" >\n' +
        '                             <span class="left">高度</span>\n' +
        '                             <input class="cur-Height" value="100%" disabled>\n' +
        '                        </div>\n' +
        '                    </div>\n' +
        '                </div>\n' +
        '            </div>\n' +
        '                <div class="edit-page-bottom">\n' +
        '                    <input class="btn btn-fff-s" type="button" value="取消" @click="closeEditBgPop">\n' +
        '                    <input class="btn btn-blue-s" type="button" value="保存" @click="saveBgSet">\n' +
        '                </div>\n' +
        '        </div>' +
        '</div>',
    data: function () {
        return {
            showLayout: true,
            showGlobal: true,
            dragging: false,
            fullScreen: "fullScreen",
            pageId: undefined,
            jsonId: undefined,
            websiteId: undefined,
            wfwfid: undefined,
            myModules: [],
            curBgInfo: null,
            defaultColors: [  //展示的颜色列表
                '#c01111', '#ba2684', '#63065F', '#4343c5', '#2c7fcc', '#22aabe', '#19a17c', '#347939', '#dbbd34', '#ef8b20', '#e66219', '#ed480f', '#db2323', '#000000'
            ],
            defaultBg: "/assets/images/default-img.png",
            bgTransparency: '100%', //背景透明度
            showEditBg: false,
            showDelPop: false,
            moduleId: undefined,
            editorIndex: 1,
            baseModule:[
                {
                    "iconUrl":"/assets/images/form_lists/icon-group.png",
                    "id":4,
                    "name":"组合字段",
                },
                {
                    "iconUrl":"/assets/images/form_lists/icon_text.png",
                    "id":0,
                    "name":"多行文本",
                },
                {
                    "iconUrl":"/assets/images/form_lists/table.png",
                    "id":1,
                    "name":"表格",
                },
                {
                    "iconUrl":"/assets/images/form_lists/icon-richtext.png",
                    "id":2,
                    "name":"富文本",
                },
                {
                    "iconUrl":"/assets/images/form_lists/icon_image.png",
                    "id":3,
                    "name":"多图列表",
                },
                {
                    "iconUrl":"/assets/images/form_lists/icon_list.png",
                    "id":5,
                    "name":"多行字段",
                },
                {
                    "iconUrl":"/assets/images/form_lists/icon_slide.png",
                    "id":6,
                    "name":"轮播图",
                },
                {
                  "iconUrl":"/assets/images/form_lists/icon-richtext.png",
                  "id":7,
                  "name":"附件",
                },
                {
                    "iconUrl":"/assets/images/form_lists/icon_slide.png",
                    "id": 8,
                    "name":"图文组合",
                },
                {
                    "iconUrl":"/assets/images/form_lists/icon-richtext.png",
                    "id": 9,
                    "name":"评论",
                },
                {
                    "iconUrl":"/assets/images/form_lists/icon_relative.png",
                    "id": 10,
                    "name":"关联表单",
                },
                {
                    "iconUrl":"/assets/images/form_lists/icon_bgimg.png",
                    "id": 11,
                    "name":"背景图文",
                },
                {
                    "iconUrl":"/assets/images/form_lists/icon_relative.png",
                    "id": 12,
                    "name":"景点简介",
                },
                {
                    "iconUrl":"/assets/images/form_lists/icon_bgimg.png",
                    "id": 13,
                    "name":"景点关联信息",
                },
                {
                    "iconUrl":"/assets/images/form_lists/icon_bgimg.png",
                    "id": 14,
                    "name":"景点购票信息",
                },
                {
                    "iconUrl":"/assets/images/form_lists/icon-group.png",
                    "id":15,
                    "name":"组合字段2",
                },
                {
                    "iconUrl":"/assets/images/form_lists/icon_slide.png",
                    "id":16,
                    "name":"课程学习",
                },
                {
                    "iconUrl":"/assets/images/form_lists/icon_slide.png",
                    "id":17,
                    "name":"名师工作室",
                },
                {
                    "iconUrl":"/assets/images/form_lists/icon_slide.png",
                    "id":18,
                    "name":"简介",
                },
                {
                    "iconUrl":"/assets/images/form_lists/icon_slide.png",
                    "id":19,
                    "name":"组合字段3",
                },
                {
                    "iconUrl":"/assets/images/form_lists/icon_slide.png",
                    "id":20,
                    "name":"多行字段2",
                },
                {
                    "iconUrl":"/assets/images/left_panel/icon-video.png",
                    "id":21,
                    "name":"视频详情",
                },
                {
                    "iconUrl":"/assets/images/left_panel/icon-video.png",
                    "id":22,
                    "name":"分集视频",
                },
                {
                    "iconUrl":"/assets/images/form_lists/icon_bgimg.png",
                    "id":23,
                    "name":"教师基础信息",
                },
                {
                    "iconUrl":"/assets/images/form_lists/icon_bgimg.png",
                    "id":24,
                    "name":"教师模块信息",
                },
                {
                    "iconUrl":"/assets/images/form_lists/icon_image.png",
                    "id":25,
                    "name":"图片瀑布流",
                },
                {
                    "iconUrl":"/assets/images/left_panel/icon-video.png",
                    "id":26,
                    "name":"视频(有封面)",
                },
                {
                    "iconUrl":"/assets/images/form_lists/icon-richtext.png",
                    "id":27,
                    "name":"附件2",
                },
                {
                    "iconUrl":"/assets/images/form_lists/icon_slide.png",
                    "id":28,
                    "name":"组合字段4",
                },{
                  "iconUrl":"/assets/images/form_lists/icon_slide.png",
                  "id":29,
                  "name":"新书推荐",
                },
            ],
            baseModule1:[
                {
                    "iconUrl":"/assets/images/form_lists/icon-group.png",
                    "id": 4,
                    "name":"组合字段",
                },
            ],
            baseModule2:[
                {
                    "iconUrl":"/assets/images/form_lists/icon-group.png",
                    "id": 4,
                    "name":"组合字段",
                },
                {
                    "iconUrl":"/assets/images/form_lists/icon_text.png",
                    "id":0,
                    "name":"多行文本",
                },
                {
                    "iconUrl":"/assets/images/left_panel/icon-video.png",
                    "id": 1,
                    "name":"视频",
                },
                {
                    "iconUrl":"/assets/images/form_lists/icon-richtext.png",
                    "id": 6,
                    "name":"iframe嵌套",
                },
                {
                    "iconUrl":"/assets/images/form_lists/icon-richtext.png",
                    "id": 2,
                    "name":"富文本",
                },
                {
                    "iconUrl":"/assets/images/left_panel/icon-video.png",
                    "id": 3,
                    "name":"花絮视频",
                },
                {
                    "iconUrl":"/assets/images/form_lists/icon-group.png",
                    "id": 5,
                    "name":"花絮看点",
                },{
                    "iconUrl":"/assets/images/left_panel/icon-video.png",
                    "id": 7,
                    "name":"多视频组件",
                },
            ],
            baseModule3:[
                {
                    "iconUrl":"/assets/images/form_lists/icon-group.png",
                    "id": 4,
                    "name":"组合字段",
                },
                {
                    "iconUrl":"/assets/images/form_lists/icon_slide.png",
                    "id":0,
                    "name":"轮播图",
                },
                {
                    "iconUrl":"/assets/images/form_lists/icon-richtext.png",
                    "id": 6,
                    "name":"iframe嵌套",
                },
                {
                    "iconUrl":"/assets/images/form_lists/icon_image.png",
                    "id":7,
                    "name":"图片瀑布流",
                },
            ],
            baseModule4:[
                {
                    "iconUrl":"/assets/images/form_lists/icon-group.png",
                    "id": 4,
                    "name":"组合字段",
                },
                {
                    "iconUrl":"/assets/images/form_lists/icon_slide.png",
                    "id":0,
                    "name":"轮播图",
                },
                {
                    "iconUrl":"/assets/images/form_lists/icon_image.png",
                    "id": 1,
                    "name":"多图列表",
                },
                {
                    "iconUrl":"/assets/images/form_lists/icon_list.png",
                    "id": 2,
                    "name":"多行字段",
                },
            ],
            baseModule5:[
                {
                    "iconUrl":"/assets/images/form_lists/icon-group.png",
                    "id": 4,
                        "name":"组合字段",
                },
                {
                    "iconUrl":"/assets/images/form_lists/icon_text.png",
                    "id":0,
                    "name":"多行文本",
                },
                {
                    "iconUrl":"/assets/images/form_lists/icon_slide.png",
                    "id": 1,
                    "name":"轮播图",
                },
                {
                    "iconUrl":"/assets/images/form_lists/icon_image.png",
                    "id": 2,
                    "name":"多图列表1",
                },
                {
                    "iconUrl":"/assets/images/form_lists/icon_image.png",
                    "id": 3,
                    "name":"多图列表2",
                },
                {
                  "iconUrl":"/assets/images/form_lists/icon_image.png",
                  "id": 9,
                  "name":"多图列表3",
                },
                {
                    "iconUrl":"/assets/images/form_lists/icon_list.png",
                    "id": 5,
                    "name":"多行字段",
                },
                {
                    "iconUrl":"/assets/images/form_lists/icon-richtext.png",
                    "id": 6,
                    "name":"iframe嵌套",
                },
                {
                    "iconUrl":"/assets/images/form_lists/icon-richtext.png",
                    "id": 7,
                    "name":"富文本",
                },
                {
                    "iconUrl":"/assets/images/form_lists/icon_image.png",
                    "id": 8,
                    "name":"场地(平铺)",
                },
                {
                    "iconUrl":"/assets/images/form_lists/icon_image.png",
                    "id": 10,
                    "name":"组合字段2",
                },
                {
                    "iconUrl":"/assets/images/form_lists/icon_image.png",
                    "id": 11,
                    "name":"组合字段3",
                },
                {
                    "iconUrl":"/assets/images/form_lists/icon_image.png",
                    "id": 12,
                    "name":"多行字段2",
                },
                {
                    "iconUrl":"/assets/images/form_lists/icon-richtext.png",
                    "id": 13,
                    "name":"附件",
                },
                {
                    "iconUrl":"/assets/images/form_lists/icon_relative.png",
                    "id": 14,
                    "name":"关联表单",
                },
                {
                    "iconUrl":"/assets/images/form_lists/icon_image.png",
                    "id": 15,
                    "name":"多图列表4",
                },
            ],
            baseModule6:[
                {
                    "iconUrl":"/assets/images/form_lists/icon-group.png",
                    "id": 4,
                    "name":"组合字段",
                },
                {
                    "iconUrl":"/assets/images/form_lists/icon-richtext.png",
                    "id": 1,
                    "name":"富文本",
                },
                {
                    "iconUrl":"/assets/images/form_lists/icon_image.png",
                    "id": 2,
                    "name":"多图列表",
                },
                {
                    "iconUrl":"/assets/images/form_lists/icon-richtext.png",
                    "id": 3,
                    "name":"评论",
                },
                {
                    "iconUrl":"/assets/images/left_panel/icon-video.png",
                    "id": 5,
                    "name":"分集视频",
                },
                {
                    "iconUrl":"/assets/images/left_panel/icon-video.png",
                    "id": 6,
                    "name":"视频",
                },
            ],
            applicationModules: [],
            showModulesMarket: false, //是否显示模块市场
            modulesList: [],
            marketIsLoading: false,
            selectedModules: [], //从市场上选中的所有应用集合

            currentBgClipStyle: {},
            showFontConvertStatu: undefined,
            bgNo: true,  //背景设置
            isFormList_1: false,
            isFormDetail_need: false,   //是否是 表单详情--需求详情模板
            isFormDetail_live: false,   //是否是 表单详情--直播详情模板
            isFormDetail_picture: false,   //是否是 表单详情--图片详情模板
            isFormDetail_store: false,   //是否是 表单详情--店铺详情模板
            isFormDetail_venue: false,  //是否是 表单详情--场馆详情模板
            isFormDetail_order: false,  //是否是 表单详情--点单详情模板
            inputPageid: '',
            pageWidth: 900, //页面宽度
            showTBStatu: undefined, //是否展示头底
            showAppTopStatu: undefined,
            showAppBottomStatu: undefined,
            showShareStatu: undefined, //是否展示分享
        }
    },
    watch: {
        webjson: {
            handler: function(val, oldVal) {
                if (val && typeof this.showTBStatu == 'undefined') {
                    this.showTBStatu = val.setting.hasOwnProperty("hasHeaderFooter")? val.setting.hasHeaderFooter:false;
                    if(this.isFormDetail_venue){
                        this.showTBStatu = val.setting.hasOwnProperty("hasHeaderFooter")? val.setting.hasHeaderFooter:true;
                    }
                }
                if (val && typeof this.showAppTopStatu == 'undefined') {
                    this.showAppTopStatu = val.setting.hasOwnProperty("showAppHeader")? val.setting.showAppHeader:false;
                }
                if (val && typeof this.showAppBottomStatu == 'undefined') {
                    this.showAppBottomStatu = val.setting.hasOwnProperty("showAppFooter")? val.setting.showAppFooter:false;
                } if (val && typeof this.showShareStatu == 'undefined') {
                  this.showShareStatu = val.setting.hasOwnProperty("showShareStatu")? val.setting.showShareStatu:false;
                }
            },
            deep: true //true 深度监听
        },
        showTBStatu:function (val){
            this.$emit('updatetb', val);
        },
        showAppTopStatu:function (val){
            this.$emit('updateappt', val);
        },
        showAppBottomStatu:function (val){
            this.$emit('updateappb', val);
        },
        showShareStatu:function (val){
            this.$emit('updateappshare', val);
        },
    },
    created: function () {
        this.pageId = getQueryString("pageId");
        this.jsonId = getQueryString("jsonId");
        this.websiteId = getQueryString("websiteId");
    },
    mounted: function () {
        this.wfwfid = this.$parent.wfwfid;
        this.closePops();
        this.scrollInit();
        this.isFormList_1 = this.$parent.isFormList_1;
        this.isFormDetail_need = this.$parent.isFormDetail_need;
        this.isFormDetail_live = this.$parent.isFormDetail_live;
        this.isFormDetail_picture = this.$parent.isFormDetail_picture;
        this.isFormDetail_store = this.$parent.isFormDetail_store;
        this.isFormDetail_venue = this.$parent.isFormDetail_venue;
        this.isFormDetail_order = this.$parent.isFormDetail_order;
    },
    methods: {
        //设置添加页面滚动条
        scrollInit: function () {
            $(".pages-set").on("click", function () {
                cancleBubble();
            })
        },
        setTabChange: function (e) {
            var thisIndex = $(e.target).index();
            $(e.target).addClass("active").siblings().removeClass("active");
            $(".pop-nav-content .tab-con").eq(thisIndex).show().siblings().hide();

            if(this.bg.bgColor !== ''){
                this.bgNo = false;
            }
            if(thisIndex === 1 && !this.inputPageid){
                this.inputPageid = this.$parent.tbPageid;
            }
            this.pageWidth = this.$parent.pagewidth;
        },
        //展示编辑背景的弹框
        showEditBgPop: function (bg) {
            var selectedColor;
            // this.getBgClipSets(bg);

            selectedColor = bg && bg.bgColor ? bg.bgColor : "#fff";

            cancleBubble();
            this.showEditBg = true;
            this.$nextTick(function () {
                var editBgPopW = $(".edit-page-bg").outerWidth();
                var editBgPopPos = $(".edit-page-bg").offset().left;

                $(".bgSetColor-spectrum").css({
                    left: editBgPopW + editBgPopPos + 20 + 'px'
                })
            });
            colorSet.bgColorInit(selectedColor);
            this.curBgInfo = bg || undefined;

        },
        //关闭编辑背景弹框
        closeEditBgPop: function () {
            this.showEditBg = false;
            this.curBgInfo = null;
        },
        //点击空白区域，关闭编辑背景和颜色弹框
        closePops: function () {
            var _this = this;
            $(".pages-list").on("click", ".statu2", function () {
                cancleBubble();
            });
            $(".sp-replacer").on("click", function (e) {
                cancleBubble();
            });
            $(".sp-container").on("click", function (e) {
                cancleBubble();
            });
            $(".edit-page-bg").on("click", function () {
                cancleBubble();
                $(".sp-choose").trigger("click");
            });
            $(window).on("click", function (event) {
                _this.closeEditBgPop();
                $(".pages-set").slideUp(200);
                $(".pages .expanded").removeClass("expanded");
                $(".sp-choose").trigger("click");
            })
        },
        //颜色列表的hover效果
        colorMouseenter: function (color, $event) {
            $($event.target).css({
                "border": "2px solid" + color
            })
        },
        colorMouseleave: function (color, $event) {
            $($event.target).css({
                "border": "2px solid #fff"
            })
        },
        //主题颜色的切换
        selectColor: function (themecolor) {
            // 将自定义颜色也跟着切换
            // $(".themeCustom .sp-preview-inner").css("background", themecolor);
            colorSet.themecolor = themecolor;
            colorSet.colorsInit();
            // if(vue.customColor && typeof vue.customColor == 'function'){
            //     vue.customColor();
            // }
        },

        //打开背景裁剪方式编辑弹框
        getBgClipSets: function (bg) {
            var styleClass = [];
            if (!bg) {
                bg = {};
            }
            if (!bg.hasOwnProperty('styleClass')) {
                bg['styleClass'] = 'bg-scale-style2 bg-repeat-style1 bg-align-style1';
            }
            styleClass = bg['styleClass'].split(" ");
            this.currentBgClipStyle = {
                scaleStyles: styleClass[0],
                repeatStyles: styleClass[1],
                alignStyles: styleClass[2]
            };
        },

        //保存背景设置
        saveBgSet: function () {
            var thisColor = '';
            if ($(".sp-preview").css("dislpay") !== 'none') {
                thisColor = $(".bg-Custom .sp-preview-inner").css("background-color");
            }
            this.curBgInfo = {
                bgColor: thisColor,
            };
            if (thisColor) {
                this.bg = this.curBgInfo;
                this.closeEditBgPop();
            } else {
                this.showToastEvent("请添加背景颜色或者图片")
            }
            vue.hasModefied = true;  //修改背景
            this.bgNo = false;
            this.$emit('updatebg', this.bg);
        },
        //删除页面背景
        delPageColor: function () {
            this.bgNo = true;
            this.bg={
                bgColor: ''
            };
            vue.hasModefied = true;  //删除背景
            this.$emit('updatebg', this.bg);
        },
        // 颜色面板初始化
        openColorPanel: function () {
            colorSet.colorsInit();
        },
        //头底的pageid
        changePageid: function () {
            this.$emit('changepid', this.inputPageid);
        },
        //修改页面宽度
        changePageWidth: function () {
            if(this.pageWidth >= 900 && this.pageWidth <= 1400){
                this.$emit('updatewidth', this.pageWidth);
            }
        }
    }
});


