// 用于首页的交互

var vue = new Vue({
    el: '#index',
    data: {
        pageId: undefined,
        jsonId: undefined,
        webJson: undefined,
        websiteId: "",
        showHeader: true,
        showFooter: true,
        showBanner: true,
        showBayWindow: false,
        showQuery: false,
        activePage: undefined,
        bgTransparency: '100%', //背景透明度
        showEditBg: false,
        showEditName: false,
        activeId: '',
        curBgInfo: null,
        wfwfid: undefined,
        myModules: [],
        modeluMinWidth: 8,  //模块的几个尺寸设置
        defaultWidth: 50,
        modeluMinHeight: 3,
        defaultHeight: 30,
        toastMessage: '',
        toastType: undefined,
        showToast: false,
        showGlobalIframe: false,
        defaultBg: "/assets/images/default-img.png",
        defaultColors: [  //展示的颜色列表
            '#c01111', '#ba2684', '#63065F', '#4343c5', '#2c7fcc', '#22aabe', '#19a17c', '#347939', '#dbbd34', '#ef8b20', '#e66219', '#ed480f', '#db2323', '#000000'
        ],
        pageBgs: [],
        moduleCoordinate: [], //中间模块的坐标列表
        leftNavs: {},
        leftNavSets: {},
        activePageIndex: 1,
        basePlugs: [{
            jsComponent: 'mCustomScrollBar',
            dependentList: ""
        }],
        musicStyle: {
            show: false,
            type: 1,
            loop: false,
            online: false,
            name: '',
            url: '',
        },
        callBackArr1:[], //非当前页面的滚动集合
    },
    created: function () {
        this.pageId = getQueryString("pageId");
        this.jsonId = getQueryString("jsonId");
        this.websiteId = getQueryString("websiteId");
        getWfwfid(this);
        // 先判断父页面是否有webjson
        if (window.hasOwnProperty('p_webjson')) {
            this.webjsonData(window['p_webjson']);
        } else {
            this.getModuleJson();
        }
    },
    mounted: function () {
        var _this = this;
        this.closePops();
        // indexPage.littleModuleClick();
        this.$nextTick(function () {
            _this.paginationHover();
        });
        $(window).on("resize", function () {
            indexPage.resizeUpdate();
            var screenWidth = document.body.clientWidth;
            var screenHeight = document.body.clientHeight;
            var gridHeight=$(".grid-stack-wrap").height(); // 100为上下的边距的和
            if(screenHeight > 780){
                if(screenWidth > 1400){
                    $(".grid-stack-main").css({
                        "max-height":gridHeight-40+'px', // 100为上下的边距的和
                    });
                }else{
                    $(".grid-stack-main").css({
                        "max-height":gridHeight/0.7-40+'px', // 100为上下的边距的和
                    });
                }
            }
        })
        window.addEventListener("onorientationchange" in window ? "orientationchange" :"resize" , function (){
            _this.checkOrientation();
        },false)
    },
    methods: {
        // 设置全屏效果
        initFullPage: function () {
            var _this = this;
            var colorType = _this.leftNavSets.backgroudType;
            if (_this.isPC()) {
                var newarr = [];
                var anchorlists = _this.leftNavSets.itemList;
                for (var i = 0; i < anchorlists.length; i++) {
                    newarr.push('grid' + anchorlists[i].id)
                }
                $('#fullpage').fullpage({
                    anchors: newarr,
                    navigation: true,
                    afterRender: function () {
                        $("#fp-nav li:not(.nav)").remove();
                        var ele = $(".pagination-color li").eq(0).find("a");
                        if (_this.leftNavSets) {
                            $(".pagination-color li").eq(0).addClass("active-li");
                            if (_this.leftNavSets.typeId !== 4) {
                                if (_this.leftNavSets.typeId === 6 || _this.leftNavSets.typeId === 8) {
                                    var className = colorType == 0 ? 'bg-theme' : 'bg-custom';
                                    $(".pagination-color li").eq(0).addClass(className);
                                } else if(_this.leftNavSets.typeId===9){
                                    var className=  colorType==0?'active':'bg-custom';
                                    ele.addClass(className);
                                }else {
                                    if(_this.leftNavSets.typeId!==1){
                                        var className = colorType == 0 ? 'bg-theme' : 'bg-custom';
                                        ele.addClass(className);
                                    }
                                }
                            }
                        }
                    },
                    onLeave: function (index, nextIndex, direction) {
                        var loadIndex;
                        loadIndex = Number(nextIndex - 1);
                        _this.lazyLoadBg(loadIndex);
                        scrollCloseSelectList();
                        var colorType = _this.leftNavSets.backgroudType;
                        var preEle = $(".pagination-color li").eq(index - 1);
                        var ele = $(".pagination-color li").eq(nextIndex - 1);
                        // 设置原点为主颜色
                        if (_this.leftNavSets) {
                            preEle.removeClass("active-li");
                            ele.addClass("active-li");
                            if (_this.leftNavSets.typeId !== 4) {
                                if (_this.leftNavSets.typeId === 6 || _this.leftNavSets.typeId === 8) {
                                    var className = colorType == 0 ? 'bg-theme' : 'bg-custom';
                                    preEle.removeClass(className);
                                    ele.addClass(className);
                                }else if(_this.leftNavSets.typeId===9){
                                    var className=  colorType==0?'active':'bg-custom';
                                    ele.addClass(className);
                                } else {
                                    if(_this.leftNavSets.typeId!==1) {
                                        var className = colorType == 0 ? 'bg-theme' : 'bg-custom';
                                        preEle.find("a").removeClass(className);
                                        ele.find("a").addClass(className);
                                    }
                                }
                            }
                        }
                        _this.activePageIndex = nextIndex;
                        _this.setGridMiddle();
                        if (_this.activePageIndex > 1) {
                            $('.ft-nav-wrap').show();
                        }
                        setTimeout(function () {
                            for(var m=0;m<_this.callBackArr1.length;m++){
                                if(_this.callBackArr1[m].indexNum == loadIndex){
                                    _this.callBackArr1[m].fn.upadteMarquee();
                                }else{
                                    _this.callBackArr1[m].fn.pauseMarquee()
                                }
                            }
                        },300)
                    },
                    afterLoad: function (anchorLink,index) {
                        $(window).trigger('resize');
                        if(index == _this.leftNavSets.itemList.length){
                        var content1 = $(this).find('.grid-stack-main');
                        $(content1).mCustomScrollbar({
                            theme: "minimal-dark"
                        });
                        $(content1).scroll(function () {
                            var end = $(content1).scrollTop();
                            if(end <= 10){
                                $('#fullpage').fullpage.setAllowScrolling(true)
                            }else{
                                $('#fullpage').fullpage.setAllowScrolling(false)
                            }
                        })
                        }
                    }
                });
            }
            _this.setGridMiddle();
            vue.getSid();
        },
        //关闭编辑背景弹框
        closeEditBgPop: function () {
            this.showEditBg = false;
            this.curBgInfo = null;
        },
        //点击空白区域，关闭编辑背景和颜色弹框
        closePops: function () {
            var _this = this;
            $(".pages-list").on("click", ".statu2", function () {
                cancleBubble();
            });
            $(".sp-replacer").on("click", function (e) {
                cancleBubble();
            });
            $(".sp-container").on("click", function (e) {
                cancleBubble();
            });
            $(".edit-page-bg").on("click", function () {
                cancleBubble();
                $(".sp-choose").trigger("click");
            });
            $(window).on("click", function () {
                _this.closeEditBgPop();
                $(".pages-set").slideUp(200);
                $(".pages .expanded").removeClass("expanded");
                $(".sp-choose").trigger("click");
            })
        },
        // 中间画布的初始化
        canvasInit: function () {
            var _this = this;
            var options = {
                width: 100,
                cellHeight: 10,
                verticalMargin: 0,
                itemMinWidth: this.modeluMinWidth,
                itemMinHeight: this.modeluMinHeight,
                float: false,
                handle: '.drag-nav',
                removable: '.trash',
                removeTimeout: 100,
                resizable: {autoHide: true, handles: 'n,e,s,w', disabled: true},
                acceptWidgets: '.grid-stack-item'
            };
            $('.grid-stack').gridstack(options);
            this.newWidgetCallback();
        },
        // 画布添加模块之后的回调
        newWidgetCallback: function () {
            var _this = this;
            $('.grid-stack').on("added", function (event, items) {
                _this.activeId = $(items[0].el).attr("data-app-id");
                indexPage.setLayoutSize(items);
            });
        },
        //调用toast
        showToastEvent: function (message, type) {
            var _this = this;
            this.toastMessage = message;
            this.toastType = type;
            this.showToast = true;
            setTimeout(function () {
                _this.showToast = false;
            }, 2000)
        },
        // 设置中间画布上下居中
        setGridMiddle: function () {
            var gridHeight;
            var headerH = this.showHeader ? $("#header").outerHeight(true) : 0;
            var footerH = this.showFooter && this.activePageIndex == this.leftNavs.length ? $("#footer").outerHeight(true) : 0;
            // $(".fp-tableCell").css({
            //     "padding-top": headerH + 'px',
            //     "padding-bottom": footerH + 'px',
            //     "box-sizing": "border-box"
            // });
            $(".full-slide-bg").css({
                top: 0,
                bottom: 0
            });
            gridHeight = $(".grid-stack-wrap").height(); // 100为上下的边距的和
            // gridHeight=$(".grid-stack-wrap").height()-headerH-footerH; // 100为上下的边距的和
            var screenWidth = document.body.clientWidth;
            if(screenWidth > 1400){
                $(".grid-stack-main").css({
                    "max-height": gridHeight - 40 + 'px', // 100为上下的边距的和
                });
            }else{
                $(".grid-stack-main").css({
                    "max-height": gridHeight/0.7 - 40 + 'px', // 100为上下的边距的和
                });
            }

        },
        //获取模块JSON数据
        getModuleJson: function () {
            var _this = this;
            if (!_this.jsonId) {
                return
            }
            var baseUrl = window['isCDN']?'//static.mh.chaoxing.com':'';
            $.ajax({
                // url: getContextPath() + "/webjson/" + _this.jsonId + "/content" + getSfidFunc(),
                url: baseUrl+getContextPath() + "/webjson/" + this.jsonId + "/content-cache?w=" + _this.websiteId + getSfidFunc(1),
                data: {
                    wfwfid: this.wfwfid
                },
                success: function (res) {
                    if (res.code == 1) {
                        if (res.data) {
                            _this.webjsonData(res.data.content);
                        }
                    }
                }
            })
        },
        // webjson赋值
        webjsonData: function (data) {
            var vue = this;
            var content = JSON.parse(data);
            if (!content.aid) {
                return
            }
            vue.webJson = content;
            vue.pageBgs =Array.isArray(vue.webJson.setting.bg)? vue.webJson.setting.bg : vue.webJson.setting.bg.multiBgs;
            indexPage.hasPlugList = vue.webJson.setting.plugs || [];
            indexPage.demandLoading();
            vue.showHeader = typeof (vue.webJson.setting.hasHeader) == 'undefined' ? true : vue.webJson.setting.hasHeader;
            vue.showFooter = typeof (vue.webJson.setting.hasFooter) == 'undefined' ? true : vue.webJson.setting.hasFooter;
            vue.showBanner = typeof (vue.webJson.setting.hasBanner) == 'undefined' ? true : vue.webJson.setting.hasBanner;
            vue.showBayWindow = typeof (vue.webJson.setting.showBayWindow) == 'undefined' ? false : vue.webJson.setting.showBayWindow;
            vue.showQuery = typeof (vue.webJson.setting.showQuery) == 'undefined' ? false : vue.webJson.setting.showQuery;
            vue.showGlobalIframe = typeof (vue.webJson.setting.showGlobalIframe) == 'undefined' ? false : vue.webJson.setting.showGlobalIframe;
            colorSet.themecolor = vue.webJson.setting.themeColor || "#3D82F2";
            colorSet.changeColor();
            vue.$nextTick(function () {
                vue.getLeftNavs();
                // vue.$refs.bayWindow.init();
                if (vue.showBayWindow) {
                    vue.$refs.bayWindow.init();
                }
                if (vue.showGlobalIframe && vue.webJson.setting.globalIframe) {
                    vue.$refs.globalIframe.init(vue.webJson.setting.globalIframe);
                }
                // 背景音乐
                if (vue.webJson.setting.musicStyle && vue.webJson.setting.musicStyle.show) {
                    vue.musicStyle = vue.webJson.setting.musicStyle;
                    vue.$refs.bgMusic.init(vue.webJson.setting.musicStyle);
                }
            });
        },
        //获取左侧导航
        getLeftNavs: function () {
            var _this = this;
            if (vue.webJson.setting.hasOwnProperty('leftNavs')) {
                this.leftNavSets = vue.webJson.setting.leftNavSets;
                this.leftNavs = vue.webJson.setting.leftNavs;
                // 自定义导航颜色
                if (this.leftNavSets.backgroudType === 1) {
                    var cssRule = [{
                        selector: '#fp-nav .bg-custom',
                        rule: 'background:' + _this.leftNavSets.backgroudColor + '!important;'
                    }];
                    updateColor.updateCSSRule(cssRule);
                }
                this.$nextTick(function () {
                    _this.canvasInit();
                    _this.initFullPage();
                        _this.getDatas({
                            "header": _this.setGridMiddle,
                            "full-banner": _this.setBannerH
                        });


                    if (vue.webJson.aid.length > 0) {
                            _this.canvasContentShow();
                        // indexPage.showLayoutContent();
                    }
                    _this.lazyLoadBg(0);
                });
                return
            }
            $.ajax({
                url: '/engine2/navigation/div' + getSfidFunc(),
                data: {
                    "pageId": this.pageId,
                    wfwfid: this.wfwfid
                },
                success: function (res) {
                    if (res.code == 1) {
                        _this.leftNavSets = res.data.navigation;
                        _this.leftNavs = res.data.navigation.itemList;
                        // 自定义导航颜色
                        if (_this.leftNavSets.backgroudType === 1) {
                            var cssRule = [{
                                selector: '#fp-nav .bg-custom',
                                rule: 'background:' + _this.leftNavSets.backgroudColor + '!important;'
                            }];
                            updateColor.updateCSSRule(cssRule);
                        }
                        _this.$nextTick(function () {
                            _this.canvasInit();
                            _this.initFullPage();
                            _this.setModuleData();
                            setTimeout(function (){
                                _this.lazyLoadBg(0);
                            },100)

                        });
                    } else {
                        console.log(res.message)
                    }
                }
            })
        },
        getDatas: function (myCallbacks) {
            var _this= this;
            indexPage.getEmbed(undefined,true);
            var ifhead = 0;
            if (vue.showHeader) {
                ifhead = vue.showFooter?0:2
            } else {
                ifhead = vue.showFooter?1:3
            }
            var baseUrl = window['isCDN']?'//static.mh.chaoxing.com':'';
            $.ajax({
                url: baseUrl+getContextPath() + "/page/" + vue.pageId + "/all-request?head=" + ifhead + '&w=' + vue.websiteId + getSfidFunc(1),
                data: {
                    wfwfid: vue.wfwfid
                },
                success: function (res) {
                    if (res.code === 1) {
                        var obj = res.data;
                        var name = obj.title || '首页';
                        sessionStorage.setItem("datas", JSON.stringify(obj));
                        setTimeout(function () {
                            _this.renderCanvasData(name,myCallbacks);
                        },300);
                    }else{
                        $.ajax({
                            url: getContextPath() + "/page/" + vue.pageId + "/all-request?head=" + ifhead + '&w=' + vue.websiteId + getSfidFunc(1),
                            data: {
                                wfwfid: vue.wfwfid
                            },
                            success: function (res1) {
                                if (res1.code === 1) {
                                    var obj = res1.data;
                                    var name = obj.title || '首页';
                                    sessionStorage.setItem("datas", JSON.stringify(obj));
                                    setTimeout(function () {
                                        _this.renderCanvasData(name,myCallbacks);
                                    },300);
                                }
                            }
                        })
                    }
                }
            })
            // 定时器---刷新天气-----start
            // 每4个小时刷新
            setInterval(function () {
                refreshWeatherDate();   //刷新天气
            }, 1000 * 60 * 60 * 4);
            // 定时器---刷新天气-----end
            indexPage.judgeMaps();
            // indexPage.getTextDataSource();

        },
        renderCanvasData:function(name,myCallbacks){
            var obj = JSON.parse(sessionStorage.getItem("datas"))
            var _this = indexPage;
            vue.dataSubtraction(obj, myCallbacks);
            indexPage.setKeyWords(name);
            var newobj = Object.getOwnPropertyNames(obj).sort(_this.englishSort);
            var footnum = 0, footeritem = {}, hasfooter = false;
            newobj.filter(function (item, index) {
                if (item == 'footer') {
                    footnum = index;
                    footeritem = item;
                    hasfooter = true;
                }
            })
            if (hasfooter) {
                newobj.splice(footnum, 1);
                newobj.push(footeritem);
            }
            _this.firstAjaxLen = newobj.length;
            for (var i = 0; i < newobj.length; i++) {
                if (newobj[i] != 'title' && obj[newobj[i]].length > 0) {
                    var container = $("div[data-app-id=" + newobj[i] + "]");
                    if (container.length > 0) {
                        var containerBox;
                        container.each(function (index, containerItem) {
                            containerBox = $(containerItem);
                            //  此处判断是否是单纯的模块，
                            if ($(containerItem).hasClass("grid-stack-item") || containerBox.find(".grid-stack-item-content").length > 0) {
                                if(containerBox.find(".grid-stack-item-content>div:not(.item-drag-wrap)").length>0){
                                     return
                                }
                                containerBox.find(".grid-stack-item-content").prepend(obj[newobj[i]]);
                            } else {
                                if(containerBox.find(">div:not(.item-drag-wrap)").length>0){
                                    return;
                            }
                                //  此处是布局的，将内容直接放在布局上面。
                                containerBox.append(obj[newobj[i]]);
                                if (newobj[i] == 'full-banner' || newobj[i] == 'header') {
                                    for (var key in myCallbacks) {
                                        var mycallback = myCallbacks[key];
                                        if (mycallback && typeof mycallback == 'function') {
                                            mycallback();
                                        }
                                    }
                                }
                                if (newobj[i] == 'header' || newobj[i] == 'footer') {
                                    setCursor("#" + newobj[i]);
                                }
                            }
                            vue.callBackFunc(container,containerBox);
                        });
                        _this.delSet();
                    }
                }
                _this.hasLoadDataNum1++;
                _this.judgeFinishLoad();
            }
            customize_lazyLoad();
            // 显示被隐藏了的嵌套元素
            indexPage.toggleDragItem(true);
            if (window.hasOwnProperty('isBeginnerGuide') && isBeginnerGuide == 1) {
                //新手引导
                newGuid(0);
            }
            // 这个是考虑其他的情况
            for (var key in myCallbacks) {
                var mycallback = myCallbacks[key];
                if (mycallback && typeof mycallback == 'function') {
                    mycallback();
                }
            }
            setPageTitle(name);
            // 判断是否开始繁简转换
            if (vue.webJson.setting.hasOwnProperty('fontStyle') && vue.webJson.setting.fontStyle) {
                convertFontStyle();
            }
        },
        // 预览数据与all-resquest接口的差集处理
        dataSubtraction: function (datas, myCallbacks, partialRefresh) {
            var _this = indexPage;
            if (!partialRefresh) {
                //  当页面需要展示头部/底部，但是接口未返回头部信息时
                if ((vue.showHeader && !datas.hasOwnProperty("header")) || (vue.showFooter && !datas.hasOwnProperty("footer")) || datas["header"] === 0 || datas["footer"] === 0) {
                    var headerCallBack = (myCallbacks && myCallbacks['header']) || undefined;
                    _this.getHeadFooter(headerCallBack, datas);
                }
                // 当页面需要展示轮播，但是接口未返回时
                if (vue.showBanner && (!datas.hasOwnProperty("full-banner") || datas["full-banner"].length === 0)) {
                    var bannerCallBack = (myCallbacks && myCallbacks['full-banner']) || undefined;
                    _this.getBanner(undefined, bannerCallBack);
                }
            }
            //  画布的下一级：模块，可见的进行操作
            $(".grid-stack-item:visible").each(function () {
                var appId = $(this).attr("data-app-id");
                if (appId.indexOf("layout") == -1) {    //不是布局模块
                    if (!datas.hasOwnProperty(appId)) {
                        vue.getDatabaseOnId(appId, $(this));
                    }
                } else {   // 布局模块的操作
                    $(this).find(".layout-content").each(function () {
                        appId = $(this).attr("data-app-id");
                        if (!datas.hasOwnProperty(appId)) {
                            vue.getDatabaseOnId(appId, $(this));
                        }
                    })
                }

            });
        },
        //根据appId获取数据
        getDatabaseOnId: function (id, ele) {
            var _this = indexPage;
            if (!id || id == "undefined" || id == "fixLogin") {
                return
            } else {
                _this.hasLoadDataNum1--;
            }
            $.ajax({
                url: getContextPath() + "/application/view/" + id + "/data" + getSfidFunc(),
                type: "get",
                data: {
                    wfwfid: vue.wfwfid,
                    websiteId: vue.websiteId,
                    pageId: vue.pageId
                },
                success: function (res) {
                    if (res.code == 1) {
                        var thisContent = res.data.app;
                        var containerBox = ele;
                        // if (!thisContent) {
                        //     return
                        // }
                        if ( thisContent && thisContent.type === 0) {
                            if (ele.find(".grid-stack-item-content").length > 0) {
                                containerBox = ele.find(".grid-stack-item-content");
                            }
                            if(containerBox.find(">div:not(.item-drag-wrap)").length>0){
                                return
                            }
                            containerBox.append(res.data.div);
                            vue.callBackFunc(containerBox,containerBox);
                        }
                        vue.$nextTick(function () {
                            colorSet.changeColor();
                        })
                        _this.delSet();
                    }
                    _this.hasLoadDataNum2++;
                    _this.judgeFinishLoad();
                },
                error: function() {
                  _this.hasLoadDataNum2++;
                  _this.judgeFinishLoad();
                }
            })
        },
        //根据导航数据进行添加，删除
        setModuleData: function () {
            var newAids = [];
            var _this = this;
            var aids = _this.webJson.aid;
            this.leftNavs.forEach(function (item, index) {
                var navId = item.id;
                var isExist = false;
                for (var i = 0; i < aids.length; i++) {
                    var jsonId = aids[i].navId;
                    if (navId == jsonId) {
                        isExist = true;
                        newAids.push(aids[i]);
                        break
                    }
                }
                if (!isExist) {
                    newAids.push({
                        "page": 1,
                        "app": [],
                        "navId": navId
                    });
                }
            });
            this.webJson.aid = newAids;
            if (this.webJson.aid.length > 0) {
                    _this.canvasContentShow();
                    _this.getDatas({
                        "header": _this.setGridMiddle,
                        "full-banner": _this.setBannerH
                    });
            }
        },
        //获取sid的内容
        getSid: function () {
            var _this = this;
            if(this.showQuery){
                if(!vue.webJson.setting.queryContent){
                    indexPage.getSidContent("/engine2/navigationcontact/div?pageId=" + this.pageId, $("#contact"), '/engine2/navigationcontact/admin/content?pageId=' + this.pageId);
                }else{
                    $('#contact').append(vue.webJson.setting.queryContent);
                    /*回调函数调用*/
                    if (window.engineInitCallback && typeof engineInitCallback == 'function') {
                        engineInitCallback("#contact");
                    }
                    window.engineInitCallback = null;
                    setTimeout(function () {
                        $('#contact .top-btns').remove();
                    },300);
                }
            }
        },
        //画布中模块位置的回显
        canvasContentShow: function () {
            var _this = this;
            var gridStack =  $(".grid-stack");
            var gridStackArr = [];
            var num=0;
            var arr = []
            gridStack.each(function (index){
                num++;
                arr.push($(this))
                if(num === 5){
                    gridStackArr.push(arr)
                    num =0;
                    arr=[];
                }else if(index === gridStack.length-1){
                    gridStackArr.push(arr)
                }
            })
            gridStackArr.forEach(function (gridArr, arrIndex){
                var _this = this;
                (function(gridArr, arrIndex){
                    setTimeout(function (){
                        var pageData = sessionStorage.getItem("datas") || "";
                        if(pageData.length>0){
                            pageData = JSON.parse(pageData)
                        }
                        gridArr.forEach(function (item,innerIndex){
                            var index = innerIndex + arrIndex*5
                            if (!vue.webJson.aid[index]) {
                                return
                            }
                            var items = vue.webJson.aid[index].app;
                            var grid = item.data('gridstack');
                            jsonSort(items, 'y', 'x');
                            items.forEach(function (node) {
                                var container,moduleHtml='';
                                if(node.baseModule) {
                                    node.baseModule.forEach(function (item, index) {
                                        moduleHtml += item.html;
                                    });
                                }
                                container='<div data-app-id="' + node.appId + '" class="grid-stack-item"><div class="grid-stack-item-content">'+moduleHtml+'</div></div>';
                                grid.addWidget($(container),
                                    node.x, node.y, node.width, node.height);
                                    setTimeout(function (){  // addWidget 渲染有个时间所以需要延时
                                        indexPage.showLayoutContent(true);
                                        indexPage.layoutContentTab();
                                        setTimeout(function (){
                                            if (!pageData) {
                                                pageData = sessionStorage.getItem("datas") || "";
                                                pageData = JSON.parse(pageData);
                                            }
                                            if(node.appId && node.appId.indexOf("layout")>-1&&pageData){
                                                var layoutContent =  $("div[data-app-id=" +  node.appId + "]").find(".layout-content");
                                                layoutContent.each(function (){
                                                    var layoutAppId = $(this).attr("data-app-id");
                                                    var content =  $(this).find(">div:not(.item-drag-wrap)")
                                                    if(content.length===0){
                                                        if (pageData[node.appId] || pageData[layoutAppId]) {
                                                            $("div[data-app-id=" +  layoutAppId + "]").prepend(pageData[layoutAppId])
                                                            vue.callBackFunc(container,$("div[data-app-id=" +  layoutAppId + "]"));
                                                        }else{ // 单独请求
                                                            vue.getDatabaseOnId(layoutAppId, $("div[data-app-id=" +  layoutAppId + "]"))
                                                        }
                                                    }
                                                })
                                                return
                                            }
                                        },30)


                                        var content =  $("div[data-app-id=" +  node.appId + "]").find(".grid-stack-item-content>div:not(.item-drag-wrap)")
                                        if(pageData && content.length==0){
                                            if( pageData[node.appId]){
                                                $("div[data-app-id=" +  node.appId + "]").find(".grid-stack-item-content").prepend(pageData[node.appId])
                                                vue.callBackFunc(container,$("div[data-app-id=" +  node.appId + "]"));
                                            }else{ // 单独请求
                                                vue.getDatabaseOnId(node.appId, $("div[data-app-id=" +  node.appId + "]"))
                                            }
                                        }
                                    },30)
                            });
                        })
                        if(pageData && arrIndex === gridStackArr.length-1){ // 画布重画完之后执行
                            setTimeout(function (){
                                colorSet.changeColor();
                                refreshWeatherDate();   //刷新天气
                                refreshIp();  //刷新Ip
                                // vue.loadNestDom();
                                indexPage.toggleDragItem(true)
                                indexPage.layoutStyleAndAnimate();
                                indexPage.judgeMaps();
                                // indexPage.getTextDataSource();
                            },35)
                        }
                    },(arrIndex*500))
                })(gridArr, arrIndex)
            })
        },
        loadNestDom:function (){
            $(".grid-stack").each(function (index, value) {
                if (!vue.webJson.aid[index]) {
                    return
                }
                var _this = $(this);
                var items = vue.webJson.aid[index].app;
                jsonSort(items, 'y', 'x');
                items.forEach(function (node,itemIndex) {
                    var moduleHtml = '';
                    if (node.baseModule) {
                        node.baseModule.forEach(function (item, index) {
                            moduleHtml += item.html;
                        });
                    }
                    _this.find(".grid-stack-item").eq(itemIndex).find(".grid-stack-item-content").append(moduleHtml);
                    indexPage.reSetDrag(".grid-stack");
                    indexPage.delSet();
                });
            });
        },
        // 左侧导航样式3下的hover样式
        paginationHover: function () {
            $("#fp-nav").on("mouseenter", ".pagination-big-circle a", function () {
                var color = $(".pagination-big-circle .active").css("background-color");
                var colorRGB = colorSet.hexToRgb(color);
                var cssRule = [{
                    selector: '.pagination-hover-style',
                    rule: 'background:rgba(' + colorRGB.num + ',0.5)' + '!important;'
                }];
                updateColor.updateCSSRule(cssRule);
                if (!$(this).hasClass("active")) {
                    $(this).addClass("pagination-hover-style");
                }
            }).on("mouseleave", ".pagination-big-circle a", function () {
                $(this).removeClass("pagination-hover-style");
            })
        },
        // 懒加载背景图
        lazyLoadBg : function (index) {
            if (typeof index === 'undefined') {
                var hash = window.location.hash;
                index = hash.length > 0 ? $(hash).index() : 0;
            }
            var obj = $("#index .full-slide").eq(index);
            var objprev,objnext;
            var arr = [];
            arr.push(obj);
            if(index > 0){
                objprev = $("#index .full-slide").eq(index-1);
                arr.push(objprev);
            }
           if(index < $("#index .full-slide").length-1){
               objnext = $("#index .full-slide").eq(index+1);
               arr.push(objnext);
           }
            arr.forEach(function (item){
                if (item.length>0&&!item.hasClass("loaded")) {
                    var thisImg = item.find(".full-slide-bg").attr("img-src")
                    item.find(".full-slide-bg").css({
                        'background-image': 'url(' + thisImg + ')'
                    })
                    item.addClass("loaded")
                }
            })
        },
        //回调函数
        callBackFunc: function (container, containerBox) {
            var _this = this;
            if (window.engineInitCallback && typeof engineInitCallback == 'function') {
                try {
                    if (containerBox.css("display") == "none") {
                        //此情况是解决横向标签 container 不显示出来，获取不到高度，造成初始化问题
                        if ($(this).parents(".grid-stack-item-content").length > 0) {
                            container.show(0).addClass("visibility");
                        }
                    }
                    cgMhLan(containerBox);
                    //   将需要回调的方法push到数组里，
                    var tempObj = engineInitCallback(containerBox);
                    if (tempObj.resizeCallback && typeof tempObj.resizeCallback === 'function') {
                        vue.callBackArr.push({
                            ele: containerBox,
                            fn: tempObj.resizeCallback,
                            obj: tempObj
                        })
                    }
                    if(tempObj.initMarquee && tempObj.pauseMarquee){
                        var index1 = containerBox.parents('.full-slide').index();
                        _this.callBackArr1.push({
                            fn: tempObj,
                            indexNum: index1
                        })
                        var ooobj = containerBox.parents('.full-slide').hasClass('active');
                        if(!ooobj){
                            tempObj.pauseMarquee();
                        }
                    }

                } catch (e) {
                    console.log(e)
                }
            }
            window.engineInitCallback = null;     //engineInitCallback这是一个全局的方法，拿到一个应用就调用，
        },
        // 计算banner 的高度
        setBannerH:function () {
            var bannerH=$("#banner").height();
            $("#banner").find('.full-banner').css({
                "height":bannerH
            })
        },

        //  判断是否是pc端，与公共方法区别在于排除掉ipad的限制了
        isPC: function () {
            var userAgentInfo = navigator.userAgent;
            var Agents = new Array("Android", "iPhone", "SymbianOS", "Windows Phone", "iPod");
            var flag = true;
            for (var v = 0; v < Agents.length; v++) {
                if (userAgentInfo.indexOf(Agents[v]) > 0) {
                    flag = false;
                    var screenWidth = document.body.clientWidth;
                    if(Agents[v] == 'Android' && screenWidth > 927){
                        flag = true;
                    }
                    break;
                }
            }
            return flag;
        },
        checkOrientation: function () {
            var u = navigator.userAgent;
            var ipad = u.indexOf('iPad') > -1 || (navigator.platform === 'MacIntel' && navigator.maxTouchPoints > 1);
            //ipad的旋转
            if(!ipad){
                return
            }
            location.reload();
        }
    }
});


