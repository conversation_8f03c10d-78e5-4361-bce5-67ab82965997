// 用于首页的交互
var refLine;
var vue = new Vue({
    el: '#index',
    data: {
        isEditPage:true,
        pageId: undefined,
        jsonId: undefined,
        webJson: undefined,
        websiteId: "",
        showHeader: true,
        showFooter: true,
        showBanner: true,
        bannerH:undefined,
        activePage: undefined,
        showEditName: false,
        activeId: '',
        curBgInfo: null,
        wfwfid: undefined,
        modeluMinWidth: 8,  //模块的几个尺寸设置
        defaultWidth: 50,
        modeluMinHeight: 3,
        defaultHeight: 30,
        cellHeight: 10,
        toastMessage: '',
        toastType: undefined,
        showToast: false,
        pageBgs: [],
        moduleCoordinate: [], //中间模块的坐标列表
        leftNavs: [],
        leftNavSets: {},
        showMao: true,
        showToTopDis: false,
        showToTop: true,
        showQuery: true,
        firstSectionEcho:false,
        dropEvent:undefined,
        showBayWindow:false,
        showGlobalIframe:false,
        // 头部，底部，轮播单独判断的
        otherGlobalModule: [{
            name: '快捷入口',
            icon: '/assets/images/quick_entry.png',
            controlName: 'showQuery',
            className: 'global-query'
        }, {
            name: '返回顶部',
            icon: '/assets/images/to_top.png',
            controlName: 'showToTop',
            className: 'global-to-top'
        }, {
            name: '锚点',
            icon: '/assets/images/mao.png',
            controlName: 'showMao',
            className: 'global-mao'
        }, {
            name: '飘窗',
            icon: '/assets/images/bay_window.png',
            controlName: 'showBayWindow',
            className: 'global-bay-window'
        },{
            name: '全局插件',
            icon: '/assets/images/left_panel/icon-iframe.png',
            controlName: 'showGlobalIframe',
            className: 'global-iframe'

        }
        ],
        hasModefied:false,
        firstAjaxLen:0,  //首次进入需要加载的ajax数量，应用的数据
        hasLoadDataNum:0, //当前已经加载的数量
        isPekingLibrary: true,   //暂时屏蔽设置为false
        musicStyle: {
            show: false,
            type: 1,
            loop:false,
            online: false,
            name:'',
            url:'',
        },
        pekingLibraryDiv: '',  //peking/library接口的返回值
    },
    created: function () {
        editorPage.pageLoadStatu();
        this.pageId = getQueryString("pageId");
        this.jsonId = getQueryString("jsonId");
        this.websiteId = getQueryString("websiteId");
        getWfwfid(this);
        this.getModuleJson();
        this.getFixedModule();

    },
    computed:{
        pageBg:function (){
            if(!this.pageBgs){
                return []
            }
            if(this.pageBgs.bgMode === 2){
                return this.pageBgs.multiBgs
            }else{
                return this.pageBgs.singleBg
            }
        },
        bgMode:function (){
            return this.pageBgs.bgMode
        }
    },
    mounted: function () {
        var _this = this;
        // this.canvasInit();
        editorPage.delEngine("#index");
        editorPage.saveBeforeSet();
        this.anchorJump();
        this.getFirstSectionHeight();
        this.$nextTick(function () {
            editorPage.bgDragInit();
            _this.setLReferenceine();
            _this.headerHover();
            _this.windowResize();
        });
    },
    methods: {
        //设置参考线this
        setLReferenceine: function () {
            refLine = new RefLine();
        },
        // 关闭编辑模块名称的弹框，以前的
        closeEditNamePop: function (items) {
            this.showEditName = false;
            removeUnScrollPage();
            if (items) {
                editorPage.cancleAddModule(items, ".grid-satck");
            }
        },
        //更新全局模块的显示
        updateModule: function (name) {
            this[name] = !this[name];
            //  更改头部的隐藏，显示，重新计算第一屏的高度
            this.$nextTick(function () {
                if (name === 'showHeader') {
                    vue.setFirstGridH();
                }
            })
        },
        // 更新背景
        updateBg: function (bg) {
            var _this = this;
            this.$forceUpdate();
            this.$nextTick(function () {
                editorPage.bgDragInit();
            });
        },
        // 中间画布的初始化
        canvasInit: function () {
            var options = {
                width: 100, //画布分成的份数，
                cellHeight: this.cellHeight,  //每个单元给的高度。
                verticalMargin: 0, //应用之间的距离
                itemMinWidth: this.modeluMinWidth,  //应用的最小宽度
                itemMinHeight: this.modeluMinHeight,
                float: false,  //模块之间是否有空白
                // handle: '.drag-nav',
                // handle: '.grid-stack-item-content>div:not(.item-drag-wrap)',
                handle: '.grid-stack-item-content .custom-draggable-handle',
                removable: '.trash',  //不用，拖到指定区域删除
                removeTimeout: 100,  //不用
                resizable: {
                    autoHide: true,
                    // handles: 'n,e,s,w'
                    handles: 'all',  //可以拖动的区域
                    grid: [ 12, 10 ]   //拖动的最小单位值
                },
                acceptWidgets: '#leftModules .grid-stack-item',  //可拖动区域
                updateHeightCallback: function () {   //更新高度的回调
                    editorPage.updateBgHeight(".aid-groups");
                }
            };
            $('.grid-stack').gridstack(options);
        },

        //  进入页面就要进行 added操作
        // 画布添加模块之后的回调
        newWidgetCallback: function () {
            var _this = this;
            $('.grid-stack').on("added", function (event, items) {
                // 清空默认数据
                if ($(items[0].el).find(".grid-stack-item-content .item-drag-wrap").length === 0) {
                    $(items[0].el).find(".grid-stack-item-content").html("");
                }
                $(items[0].el).addClass("base-container");  //包含了此类名，嵌套元素才能拖入画布
                _this.activeId = $(items[0].el).attr("data-app-id");
                editorPage.addGridHandle(items);
            });
            editorPage.moduleDragSize();
        },
        // 展示修改模块名字弹框
        openEditModuleName: function (items) {
            this.getModuleContent(this.activeId, '', items);
        },
        //获取模块内容
        getModuleContent: function (id, name, items) {
            var _this = this;
            var type = $(items[0].el).attr("data-module-type");
            var isEngine = type === "engine" ? true : false;
            var btns = '<div class="top-btns"><div class="top-btn-groups"><span class="delete"><i class="icon-rubbish icon"></i><span>删除</span></span></div></div>';
            // 判断是不是添加的自定义内容
            if ($(items[0].el).find(".top-btns").length === 0) {
                $(items[0].el).prepend(btns);
            }
            if ( type === "base" || id == 'undefined' || typeof(id)==='undefined' || id.indexOf("layout")!==-1) {
                editorPage.setSize(items);
                return;
            }
            var url = '';
            var wfidType = $(items[0].el).attr("data-app-wfidtype");
            if (wfidType == -4) {  /*将配置好的应用设置为通用应用功能*/
                url = getContextPath() + '/application/default-app/' + _this.activeId + '/drag';
            } else {
                if (isEngine) {
                    url = getContextPath() + "/application/engine/" + id + "/drag" + getSfidFunc();
                } else {
                    url = getContextPath() + "/application/edit/" + id + "/data" + getSfidFunc();
                }
            }
            $.ajax({
                // url: isEngine ? getContextPath() + "/application/engine/" + id + "/drag"+getSfidFunc() : getContextPath() + "/application/edit/" + id + "/data"+getSfidFunc(),
                url: url,
                type: (wfidType == -4) ? "post" : (isEngine ? "post" : "get"),
                data: isEngine ? {name: name, wfwfid:this.wfwfid, websiteId:this.websiteId, pageId:this.pageId} : { wfwfid:this.wfwfid, websiteId:this.websiteId, pageId:this.pageId},
                success: function (res) {
                    if (res.code == 1) {
                        var itemHeight;
                        var thisContent = res.data.app;  //应用的数据
                        _this.hasLoadDataNum++;
                        if (thisContent.id) {
                            $(items[0].el).attr("data-app-id", thisContent.id);
                        }
                        editorPage.setPlugData($(items[0].el),res.data.jsList);
                        editorPage.setSize(items);
                        if (thisContent.adminUrl && $(items[0].el).find(".top-btns .set").length === 0) {
                            var set = '<span class="set"><a class="set-btn module-set" href="' + thisContent.adminUrl + '" target="_blank"><i class="icon-set icon"></i>设置</a></span>';
                            $(items[0].el).find(".top-btns .delete").before(set);
                        }
                        if (isEngine && vue.myModules && vue.myModules.app) {
                            vue.myModules.app.push(thisContent);
                            vue.$nextTick(function () {
                                editorPage.moduleDragInit();
                            });
                        }
                        //  应用以div形式返回的情况，iframe的type是1，不过现在都是0，没有用iframe
                        //  拿一个调用一个回调方法，每次的回调方法保持最新拿到的应用,然后清空，用完即清空。举例：swiper，echart的初始化。
                        if (thisContent.type === 0) {
                            $(items[0].el).find(".grid-stack-item-content").append(res.data.div);
                            cgMhLan($(items[0].el).find(".grid-stack-item-content"));
                            if (window.engineInitCallback && typeof engineInitCallback == 'function') {
                                engineInitCallback($(items[0].el));
                            }
                            cropImgFun($(items[0].el));
                            window.engineInitCallback = null;  //变量清空
                        }
                        vue.$nextTick(function () {
                            // 应用渲染完再判断是否结束加载状态
                            editorPage.judgeFinishLoad();
                            colorSet.changeColor();
                        })
                    }
                }
            })
        },
        //获取模块JSON数据
        getModuleJson: function () {
            var _this = this;
            $.ajax({
                url: getContextPath() + "/webjson/" + this.jsonId + "/edit-content"+getSfidFunc(),
                data:{
                    wfwfid:this.wfwfid
                },
                success: function (res) {
                    if (res.code == 1) {
                        if (res.data) {
                            vue.webJson = JSON.parse(res.data.content);
                            editorPage.handleErrorBg();

                            vue.showHeader = typeof(vue.webJson.setting.hasHeader) == 'undefined' ? true : vue.webJson.setting.hasHeader;
                            vue.showFooter = typeof(vue.webJson.setting.hasFooter) == 'undefined' ? true : vue.webJson.setting.hasFooter;
                            // vue.showBanner = typeof(vue.webJson.setting.hasBanner) == 'undefined' ? true : vue.webJson.setting.hasBanner;
                            vue.showToTop = typeof(vue.webJson.setting.showToTop) == 'undefined' ? true : vue.webJson.setting.showToTop;
                            vue.showQuery = typeof(vue.webJson.setting.showQuery) == 'undefined' ? true : vue.webJson.setting.showQuery;
                            vue.showMao = typeof(vue.webJson.setting.showMao) == 'undefined' ? true : vue.webJson.setting.showMao;
                            vue.showBayWindow = typeof(vue.webJson.setting.showBayWindow) == 'undefined' ? false : vue.webJson.setting.showBayWindow;
                            vue.showGlobalIframe= typeof(vue.webJson.setting.showGlobalIframe) == 'undefined' ? false : vue.webJson.setting.showGlobalIframe;

                            colorSet.themecolor = vue.webJson.setting.themeColor || "#3D82F2";
                            colorSet.changeColor();

                            vue.$refs.bayWindowSet.init();  //飘窗内容的设置
                            vue.$refs.bayWindow.init();   //飘窗是否显示和动画效果
                            if(vue.webJson.setting.globalIframe){
                                vue.$refs.globalIframe.init(vue.webJson.setting.globalIframe);
                            }
                            // 背景音乐
                            if(vue.webJson.setting.musicStyle && vue.webJson.setting.musicStyle.show){
                                vue.musicStyle = vue.webJson.setting.musicStyle;
                                vue.$refs.bgMusic.init(vue.webJson.setting.musicStyle);
                            }
                        }
                        vue.$nextTick(function () {
                            editorPage.bgDragInit();
                            editorPage.getHeadFooter(vue.setFirstGridH);  //回调
                            vue.getLeftNavs();
                            vue.getSid();
                            editorPage.modifyGlobalModuleStatu();
                            // editorPage.globalModuleSet();
                        });
                    }
                }
            })
        },
        //获取右侧导航
        getLeftNavs: function () {
            var _this = this;
            $.ajax({
                url: '/engine2/navigation/bererwai/div'+getSfidFunc(),
                data: {
                    "pageId": this.pageId,
                    wfwfid:this.wfwfid
                },
                success: function (res) {
                    if (res.code == 1) {
                        _this.leftNavSets = res.data.navigation;
                        _this.leftNavs = res.data.navigation.itemList;
                        _this.$nextTick(function () {
                            _this.canvasInit(); //画布的初始化
                            _this.setModuleData();
                            _this.judgeScrollPos();
                            // _this.newWidgetCallback();
                            _this.echoFirstGridContent();
                            editorPage.showLayoutContent();
                            // _this.setGridMiddle();
                        });
                    }
                }
            })
        },
        //获取固定模块
        getFixedModule: function () {
            var _this = this;
            $.ajax({
                url: '/engine2/peking/library/div'+getSfidFunc(),
                data: {
                    "pageId": this.pageId,
                    wfwfid:this.wfwfid
                },
                success: function (res) {
                    if (res.code == 1) {
                        _this.pekingLibraryDiv = res.data;
                        if (res.data && res.data.shortcutDiv) {
                            var posClass;
                            switch(res.data.shortcutPosition) {
                                // 1:左侧靠边，2:右侧靠边，3:左侧靠内容，4:右侧靠内容
                                case "1":
                                    posClass="screen-left";
                                    break;
                                case "2":
                                    posClass="screen-right";
                                    break;
                                case "3":
                                    posClass="content-left";
                                    break;
                                case "4":
                                    posClass="content-right";
                                    break;
                                default:
                            }
                            $("#quickEntry").addClass(posClass);
                            $("#quickEntry").append(res.data.shortcutDiv);

                            if(res.data.shortcutPosition=="3"){
                                var marginLeft=parseFloat($("#quickEntry").width()+ parseFloat($(".grid-wrap").width())/2);
                                $("#quickEntry").css("margin-left",-marginLeft+'px' )
                            }else if(res.data.shortcutPosition=="2"){
                                $("#quickEntry .general-set").css({
                                    "right":"0px",
                                    "left":"auto"
                                })
                            }
                        }
                        if (res.data.scrollToTopDiv) {
                            $("#toTop").append(res.data.scrollToTopDiv)
                        }
                        if (window.engineInitCallback && typeof engineInitCallback == 'function') {
                            engineInitCallback("#quickEntry");
                        }
                    }
                }
            })
        },
        //根据导航数据进行添加，删除
        setModuleData: function () {
            var newAids = [];
            var _this = this;
            var aids;
            if( !_this.webJson){
                return
            }
            aids = _this.webJson.aid;  //画布的数组
            this.leftNavs.forEach(function (item, index) {
                var navId = item.id;
                var isExist = false;
                for (var i = 0; i < aids.length; i++) {
                    var jsonId = aids[i].navId;
                    if (navId == jsonId) {
                        isExist = true;
                        newAids.push(aids[i]);
                        break
                    }
                }
                if (!isExist) {
                    newAids.push({
                        "page": 1,
                        "app": [],
                        "navId": navId
                    });
                }
            });
            this.webJson.aid = newAids;
            //  页面进入加载中间画布完成之后才能取掉加载中效果，以防数据为加载完用户点击预览造成数据丢失。！！！！
            this.countAjaxNum();
            //  画布的数量 this.webJson.aid.length
            if (this.webJson.aid.length > 0) {
                this.newWidgetCallback();
                this.canvasContentShow();
                // editorPage.showLayoutContent();
            }
        },
        //计算load需要请求的接口的数量
        countAjaxNum:function () {
            var _this=this;
            this.webJson.aid.forEach(function (item) {
                item.app.forEach(function (subItem) {
                    if(subItem.appId&&subItem.appId.indexOf("layout")===-1 &&subItem.appId !=='undefined'){
                        _this.firstAjaxLen++;
                    }
                })
            });
            this.webJson.setting.layoutData.forEach(function (item) {
                item.layoutModules.forEach(function (subItem) {
                    if(subItem && subItem !=='undefined'){
                        _this.firstAjaxLen++;
                    }
                })
            });
            if(_this.firstAjaxLen===0){
                editorPage.judgeFinishLoad();
            }
        },
        //画布中模块位置的回显
        canvasContentShow: function (localHandle) {
            var _this = this;
            $(".grid-stack").each(function (index) {
                if (!_this.webJson.aid[index]) {
                    return
                }
                var grid = $(this).data('gridstack');
                var items = vue.webJson.aid[index].app;
                jsonSort(items,'y','x');
                items.forEach(function (node) {
                    var moduleHtml = '', container;
                    try {
                        if(index>0 || $("#firstSection .grid-wrap").css("max-height")!=="none" || localHandle){
                            if (node.baseModule) {
                                node.baseModule.forEach(function (item, index) {
                                    moduleHtml += item.html;
                                });
                            }
                            container = '<div data-app-id="' + node.appId + '" class="grid-stack-item"><div class="grid-stack-item-content"><div class="custom-draggable-handle"></div>' + moduleHtml + '</div></div>';
                            grid.addWidget($(container),
                                node.x, node.y, node.width, node.height);
                            // editorPage.reSetDrag(".grid-stack");
                        }
                    } catch(e){

                    };
                });
            });
            editorPage.reSetDrag(".grid-stack");
            baseModule.baseModuleDragOrResize();  //基础组件的拖拽初始化
        },
        //获取sid的内容this，     ====暂时没用，指应用之外的
        getSid: function () {
            if (this.showBanner) {
                var setName = '轮播设置';
                var isFullPage = true;
                editorPage.getBanner(setName, isFullPage, this.getFirstSectionHeight);
            }
        },
        // 获取模块的坐标
        getCoordinates: function (type) {
            var _this = this;
            editorPage.cancleEditStatu();   //取消编辑状态
            editorPage.judgeSidPlug();
            var windowSet = this.$refs.bayWindowSet.saveData;
            var globalIframeSet = this.$refs.globalIframe.setDatas;
            if(typeof(this.bannerH) == "undefined"){
                _this.getFirstSectionHeight();
            }
            var serialized_datas = {
                // sid都没有用
                "sid": {
                    "page": 0,
                    "app": []
                },
                "aid": [],
                "setting": {
                    "type": "peking_library_l",
                    "editor": "/page/peking_library_l/editor.html",
                    "preview": "/page/peking_library_l/index.html",
                    "hasHeader": _this.showHeader,
                    "hasFooter": _this.showFooter,
                    "hasBanner": _this.showBanner,
                    "showBayWindow":_this.showBayWindow,
                    "showGlobalIframe":_this.showGlobalIframe,
                    "globalIframe":globalIframeSet,
                    "bannerHeight": this.bannerH,
                    "showToTop": _this.showToTop,
                    "showQuery": _this.showQuery,
                    "showMao": _this.showMao,
                    "bg": _this.pageBgs,
                    "themeColor": colorSet.themecolor,
                    "plugs":editorPage.plugList,
                    "globalWindow":windowSet,
                    "layoutData":[],  //布局里面存的数据
                    "moduleSort": $( "#leftModules").sortable('toArray'),   //左边面板的内容拖拽之后的顺序
                    'musicStyle': _this.musicStyle,
                    "leftNavs": _this.leftNavs,
                    "leftNavSets":_this.leftNavSets,
                    'pekingLibraryDiv': _this.pekingLibraryDiv,
                },
                "embedModdle": {
                    headModule: editorPage.getModuleOutOfGrid(this.showHeader, "#header"),
                    bannModule: editorPage.getModuleOutOfGrid(this.showBanner, "#banner"),
                    footModule: editorPage.getModuleOutOfGrid(this.showFooter, "#footer")
                }
            };
            var serialized_data = {
                "page": 1,
                "app": []
            };
            var param = {
                id: vue.jsonId,
                content: undefined
            };
            // 判断当前的模板与回显的数据类型是否一致
            var isMatch = editorPage.templateAndJsonType("peking_library_l");
            if (!isMatch) {
                return
            }
            var layoutId=0;
            $(".grid-stack").each(function (index, value) {
                var serialized_data = {
                    "page": index + 1,
                    "app": [],
                    "navId": $(this).attr("data-nav-id")    //navid是画布id，即右侧的导航对应的id
                };
                $(this).find(".grid-stack-item:not(.grid-stack-placeholder)").each(function (el) {
                    var node = $(this).data('_gridstack_node');
                    var baseModule = [];
                    var layoutData={},layoutPos=[],layoutClass=[];
                    if($(this).attr("data-module-type")==="layout"){
                        var modules = [], layoutBaseModule = [];
                        var id="layout_"+layoutId;
                        layoutId++;
                        $(this).attr("data-app-id",id);
                        $(this).find(".layout-content").each(function () {
                            modules.push($(this).attr("data-app-id"));
                            layoutPos.push({
                                left: $(this).css("left"),
                                width:$(this).width()
                            });
                            layoutClass.push($(this).attr("class"));
                            var html="";
                            $(this).find(".item-drag-wrap").each(function (index) {
                                html += $(this)[0].outerHTML;
                            });
                            layoutBaseModule.push({
                                html: html
                            })
                        });
                        var type6= '';
                        if($(this).attr("data-type")==="6"){
                            type6 = $(this).find(".tab-layout").css("background");
                            if(type6==''){
                                var layoutAttr = $(this).find(".tab-layout")[0];
                                if(layoutAttr.hasAttribute('style')){
                                    type6 = $(layoutAttr).attr('style').substring(11);
                                }
                            }
                        }
                        layoutData={
                            appId:id,
                            layoutStyle:  $(this).attr("data-type"),
                            layoutModules:modules,
                            layoutPos:layoutPos,
                            layoutClass:layoutClass,
                            tabs: $(this).find(".tab-layout-tabs").prop("outerHTML"),
                            tabsLength: $(this).find(".tab-layout-tabs>li").length,
                            baseModule: layoutBaseModule,
                            layoutBg: type6  //只有在横向标签的时候保存背景
                        };
                        serialized_datas.setting.layoutData.push(layoutData);
                    }
                    $(this).find(".item-drag-wrap").each(function (index) {
                        var html = $(this)[0].outerHTML;
                        baseModule.push({
                            html: html
                        })
                    });
                    serialized_data.app.push({
                        x: node.x,
                        y: node.y,
                        width: node.width,
                        height: node.height,
                        minWidth: node.minWidth || vue.modeluMinWidth,
                        minHeight: node.minHeight || vue.modeluMinHeight,
                        appId: $(this).attr("data-app-id"),
                        baseModule: baseModule
                    });
                });
                serialized_datas.aid.push(serialized_data);
            });
            param.content = JSON.stringify(serialized_datas);
            editorPage.savaPageJson(param, type);
        },
        //调用toast
        showToastEvent: function (message, type) {
            var _this = this;
            this.toastMessage = message;
            this.toastType = type;
            this.showToast = true;
            setTimeout(function () {
                _this.showToast = false;
            }, 2000)
        },
        // 锚点跳转带动画
        anchorJump: function () {
            $("#index").on("click", "#anchorsNav a", function () {
                var _this = this;
                var className = $(this).parents(".anchors-nav").find(".active").attr("class");
                var className1 = $(this).parents().attr("class");
                if ($(this).hasClass("active")) {
                    return
                }
                else if ($(this).parent().hasClass("active")) {
                    return
                }
                if (location.pathname.replace(/^\//, '') == this.pathname.replace(/^\//, '') && location.hostname == this.hostname) {
                    var $target = $(this.hash);
                    $target = $target.length && $target || $('[name=' + this.hash.slice(1) + ']');
                    if ($target.length) {
                        var targetOffset = $target.offset().top;
                        /*头部为fixed的时候，减去头部的高度*/
                        if($("#header").css("position")==="fixed"){
                            targetOffset=targetOffset-$("#header").height();
                        }
                        $('html,body').animate({scrollTop: targetOffset}, 400, function () {
                            $(_this).parents("li").attr("class", className).siblings().attr("class", "");
                            // 新样式6
                            if(vue.leftNavSets.typeId == 6 || vue.leftNavSets.typeId == 8){
                                $(_this).parents("li").attr("class", className).siblings().attr("class", className1);
                            }
                        });
                        return false;
                    }
                }
            });
        },
        // 获取一个首屏的高度
        getFirstSectionHeight: function () {
            // var sectionH = $(window).height();
            // var headSet = $("#pageTopWrap").outerHeight(true) || 0;
            // // this.bannerH=sectionH - headSet;
            // this.bannerH=sectionH;
            // // $("#firstSection").css("min-height", sectionH - headSet);
            // $("#firstSection").css("min-height", this.bannerH );
            // $("#banner").css("height", this.bannerH);

            var bannerH=$("#banner").height();
            $("#firstSection").css("min-height",bannerH);
        },
        // 设置第一个画布的高度
        setFirstGridH: function () {
            // 用演示缓解异步问题
            setTimeout(function () {
                var headHeight = $("#header:visible").outerHeight(true);
                var firstSectionH = $("#firstSection").height();
                var h = firstSectionH - headHeight - 20 + 'px';
                $("#firstSection").css({
                    // "min-height": h,
                    "padding-top": headHeight   // 20 为间距
                });
            },500)
        },
        windowResize: function () {
            var _this = this;
            // $(window).resize(function () {
                // _this.getFirstSectionHeight();
                // _this.setFirstGridH();
            // })
        },
        // 获取首屏高度之后再进行回显
        echoFirstGridContent:function () {
            $("#grid1").each(function (index) {
                if (!vue.webJson || !vue.webJson.aid[index] || vue.firstSectionEcho) {
                    return
                }
                vue.firstSectionEcho=true;
                var grid = $(this).data('gridstack');
                var items = vue.webJson.aid[index].app;
                items.forEach(function (node) {
                    var moduleHtml = '', container;
                    try {
                            if (node.baseModule) {
                                node.baseModule.forEach(function (item, index) {
                                    moduleHtml += item.html;
                                });
                            }
                            container = '<div data-app-id="' + node.appId + '" class="grid-stack-item"><div class="grid-stack-item-content"><div class="custom-draggable-handle"></div>' + moduleHtml + '</div></div>';
                            grid.addWidget($(container), node.x, node.y, node.width, node.height);
                    } catch(e){

                    };
                });

            });
            editorPage.reSetDrag("#grid1");
            baseModule.baseModuleDragOrResize();  //基础组件的拖拽初始化
        },
        // 判断当前滚动位置
        judgeScrollPos: function () {
            var _this = this;
            var cssRule = [], scrollTop;
            var className = 'active';
            if(vue.leftNavSets.typeId!==4){
                if (vue.leftNavSets.backgroudType === 0 && (vue.leftNavSets.typeId === 1 || vue.leftNavSets.typeId === 5 || vue.leftNavSets.typeId === 6 || vue.leftNavSets.typeId === 8)) {
                    // 背景色随主题色
                    className += ' bg-theme';
                } else if (vue.leftNavSets.backgroudType === 1 && (vue.leftNavSets.typeId === 1 || vue.leftNavSets.typeId === 5)) {
                    // 自定义背景色
                    className += ' bg-custom';
                    cssRule = [{
                        selector: '#anchorsNav .bg-custom',
                        rule: 'background:' + vue.leftNavSets.backgroudColor + ';'
                    }];
                    updateColor.updateCSSRule(cssRule);
                }
            }
            $(".anchors-nav>li").eq(0).addClass(className);
            $(window).on("scroll", function () {
                var top = $(this).scrollTop();
                var windinH = $(window).height();
                var distance = top - windinH;
                _this.judgeShowToTop(distance);
                if (!_this.showMao) {
                    return
                }
                $(".grid-wrap").each(function (index) {
                    var MARGINBOTTOM = 300;  //上一个模块距离顶部还有300的时候，左侧导航激活下一个
                    var gridTop = $(this).offset().top;
                    var gridHeight = $(this).height();
                    var navObj = $(".anchors-nav>li").eq(index);
                    if (top >= gridTop - MARGINBOTTOM && top <= gridTop + gridHeight) {
                        if (!navObj.hasClass("active")) {
                            navObj.addClass(className).siblings().removeClass(className);
                        }
                    }
                })
            })
        },
        // 判断当前位置，是否显示返回顶部
        judgeShowToTop: function (top) {
            // console.log(top)
            this.showToTopDis = top > -400 ? true : false;
        },
        // 鼠标悬停在头部的时候也要显示轮播图的设置
        headerHover: function () {
            $("#index").on("mouseenter", "#header", function () {
                $("#banner").addClass("show-set");
            });
            $("#index").on("mouseleave", "#header", function () {
                $("#banner").removeClass("show-set");
            })
        },
        // 切换布局模式
        switchLayoutMode:function () {
            editorPage.switchLayoutMode();
        },
        // 更新背景音乐
        updateBgmusic: function () {
            this.$refs.bgMusic.init(this.musicStyle);
        },
    }
});


