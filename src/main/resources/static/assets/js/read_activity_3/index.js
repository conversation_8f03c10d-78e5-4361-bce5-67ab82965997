// 用于首页的交互
var refLine;
var vue = new Vue({
    el: '#index',
    data: {
        uid:'',/*后端需要暴露的参数*/
        vc3:'',/*后端需要暴露的参数*/
        _d:'',/*后端需要暴露的参数*/
        ufid:'',/*后端需要暴露的参数*/
        pageId: undefined,
        jsonId: undefined,
        webJson: undefined,
        websiteId: "",
        showHeader: true,
        showFooter: true,
        showBanner: true,
        activePage: undefined,
        showEditName: false,
        activeId: '',
        curBgInfo: null,
        wfwfid: undefined,
        modeluMinWidth: 8,  //模块的几个尺寸设置
        defaultWidth: 50,
        modeluMinHeight: 3,
        defaultHeight: 30,
        cellHeight: 10,
        toastMessage: '',
        toastType: undefined,
        showToast: false,
        pageBgs: [],
        bgMode:2,
        moduleCoordinate: [], //中间模块的坐标列表
        leftNavs: [],
        leftNavSets: {},
        showMao:true,
        showToTopDis: false,
        showToTop: true,
        showQuery: false,
        showBayWindow:false,
        showGlobalIframe:false,
        basePlugs:[],
        callBackArr: [],
        contentInfo: '',
        contentType: 1,  //1本地数据   2接口数据
        musicStyle: {
            show: false,
            type: 1,
            loop:false,
            online: false,
            name:'',
            url:'',
        },
        commentIframe: undefined,
        isReadActvity3: true,
        tbPageid: '', //头底的pageid
        showAppHeader: false,  //移动端是否展示头
        showAppFooter: false,  //移动端是否展示底
    },
    created: function () {
        this.pageId = getQueryString("pageId");
        this.jsonId = getQueryString("jsonId");
        this.websiteId = getQueryString("websiteId");
        getWfwfid(this);
        // this.getModules();
        // 先判断父页面是否有webjson
        if (window.hasOwnProperty('p_webjson')) {
            var _this = this;
            setTimeout(function () {
                _this.webjsonData(window['p_webjson']);
            },100)
        }else{
            this.getModuleJson();
        }

        window.addEventListener("message", function (event) {
            if (typeof event.data === 'string' && event.data.startsWith('javascript:') && event.origin.endsWith("chaoxing.com")) {
                var data = event.data;
                data = data.replace('javascript:','');
                eval("(" + data + ")");
            }
        });
    },
    mounted: function () {
        var _this = this;
        this.anchorJump();
        _this.getCookieUserInfo();

        // 判断是否是学习通内
        if(indexPage.isChaoxingApp()){
            $('#index #header').css('display','none !important');
            //    右上角菜单加分享
            xxtShare(this.websiteId);
        }

      //获取评论post
      window.addEventListener('message', _this.noteRequestCookie);
    },
    methods: {
        // 中间画布的初始化
        canvasInit: function () {
            var _this=this;
            var options = {
                width: 100,
                cellHeight: this.cellHeight,
                verticalMargin: 0,
                itemMinWidth: this.modeluMinWidth,
                itemMinHeight: this.modeluMinHeight,
                float: false,
                handle: '.drag-nav',
                removable: '.trash',
                removeTimeout: 100,
                resizable: {
                    autoHide: true,
                    handles: 'n,e,s,w',
                    disabled:true
                },
                acceptWidgets: '.grid-stack-item',
                updateHeightCallback: function () {
                    if(_this.showBanner){
                        indexPage.updateBgHeight(".aid-groups");
                    }else{
                        indexPage.updateBgHeight(".index_container");
                    }
                }
            };
            $('.grid-stack').gridstack(options);
            // this.newWidgetCallback();
        },
        // 画布添加模块之后的回调
        newWidgetCallback: function () {
            var _this = this;
            $('.grid-stack').on("added", function (event, items) {
                // 清空默认数据
                if ($(items[0].el).find(".grid-stack-item-content .item-drag-wrap").length === 0) {
                    $(items[0].el).find(".grid-stack-item-content").html("");
                }
                $(items[0].el).addClass("base-container");
                _this.activeId = $(items[0].el).attr("data-app-id");
                indexPage.setLayoutSize(items);
            });
        },
        //获取模块JSON数据
        getModuleJson: function () {
            var _this = this;
            if(!this.jsonId){
                return
            }
            var baseUrl = window['isCDN']?'//static.mh.chaoxing.com':'';
            $.ajax({
                url: baseUrl+getContextPath() + "/webjson/" + this.jsonId + "/content-cache?w=" + _this.websiteId + getSfidFunc(1),
                data:{
                    wfwfid:this.wfwfid
                },
                success: function (res) {
                    if (res.code == 1) {
                        if (res.data) {
                            _this.webjsonData(res.data.content);
                        }
                    }
                }
            })
        },
        // webjson赋值
        webjsonData: function (data) {
            var vue = this;
            vue.webJson = JSON.parse(data);
            vue.pageBgs =Array.isArray(vue.webJson.setting.bg)? vue.webJson.setting.bg : vue.webJson.setting.bg.bgMode===2?vue.webJson.setting.bg.multiBgs : vue.webJson.setting.bg.singleBg;
            vue.bgMode =  vue.webJson.setting.bg.bgMode || 2;
            indexPage.hasPlugList=vue.webJson.setting.plugs || [];
            indexPage.demandLoading();
            vue.tbPageid = vue.webJson.setting.tbPageid;
            vue.showHeader = typeof(vue.webJson.setting.hasHeader) == 'undefined' ? true : vue.webJson.setting.hasHeader;
            vue.showFooter = typeof(vue.webJson.setting.hasFooter) == 'undefined' ? true : vue.webJson.setting.hasFooter;
            vue.showBanner = typeof(vue.webJson.setting.hasBanner) == 'undefined' ? true : vue.webJson.setting.hasBanner;
            vue.showToTop = typeof(vue.webJson.setting.showToTop) == 'undefined' ? true : vue.webJson.setting.showToTop;
            vue.showQuery = typeof(vue.webJson.setting.showQuery) == 'undefined' ? true : vue.webJson.setting.showQuery;
            vue.showMao = typeof(vue.webJson.setting.showMao) == 'undefined' ? true : vue.webJson.setting.showMao;
            vue.showBayWindow = typeof(vue.webJson.setting.showBayWindow) == 'undefined' ? false : vue.webJson.setting.showBayWindow;
            vue.showGlobalIframe = typeof(vue.webJson.setting.showGlobalIframe) == 'undefined' ? false : vue.webJson.setting.showGlobalIframe;
            vue.showAppHeader = typeof(vue.webJson.setting.showAppHeader) == 'undefined' ? false : vue.webJson.setting.showAppHeader;
            vue.showAppFooter = typeof(vue.webJson.setting.showAppFooter) == 'undefined' ? false : vue.webJson.setting.showAppFooter;
            colorSet.themecolor = vue.webJson.setting.themeColor || "#3D82F2";
            colorSet.changeColor();

            vue.contentType = vue.webJson.editorType || vue.contentType;
            // 接口数据
            if( vue.contentType ==2){
                var pageStr = '';
                if(vue.webJson.editorUrl.indexOf("?")!=-1){
                    if(vue.webJson.editorUrl.indexOf("pageId") == -1){
                        pageStr = '&pageId=' + vue.pageId;
                    }
                }else{
                    pageStr = '?pageId=' + vue.pageId;
                }
                pageStr += '&current_pageId=' + vue.pageId;
                pageStr += '&current_websiteId=' + vue.websiteId;
                pageStr += '&current_wfwfid=' + vue.wfwfid;
                $.get({
                    url: vue.webJson.editorUrl + pageStr,
                    success: function (res) {
                        if (res.code == 1) {
                            vue.contentInfo = '';
                            vue.contentInfo = res.data;
                        }
                        vue.$nextTick(function () {
                            vue.afterIframeFunc();
                        });
                    },
                    error: function (res) {
                        vue.$nextTick(function () {
                            vue.afterIframeFunc();
                        });
                    }
                })
            }else{
                // 赋值给展示iframe富文本,如果接口有数据，直接展示接口数据。
                vue.contentInfo = decodeURIComponent(vue.webJson.editorInfo || vue.contentInfo);
                vue.$nextTick(function () {
                    vue.afterIframeFunc();
                });
            }
            vue.getHeadFoot();

        },
        //获取头底
        getHeadFoot: function(){
          var _this = this;
          $.ajax({
            url:getContextPath() + "/page/domain-page?domain=" + document.domain,
            success: function (res) {
              if(res.code == 1){
                _this.tbPageid = res.data.pageId;
                if (_this.tbPageid) {
                  /*加载头底*/
                  $("#mh-header").load("//mh.chaoxing.com/page/" + _this.tbPageid + "/1/top/div?hide=0&hideTitle=0&online=0",function (){
                    colorSet.themecolor = vue.webJson.setting.themeColor || "#3D82F2";
                    colorSet.changeColor();
                    setMinHeight({
                      type: 'min-height',
                      target: $("#items-1"),
                      height: $("#mh-header").height() + $("#mh-footer").height() + 40
                    })
                    setCursor("#mh-header");
                  })
                  $("#mh-footer").load("//mh.chaoxing.com/page/" + _this.tbPageid + "/footer/div",function (){
                    colorSet.themecolor = vue.webJson.setting.themeColor || "#3D82F2";
                    colorSet.changeColor();
                    setMinHeight({
                      type: 'min-height',
                      target: $("#items-1"),
                      height: $("#mh-header").height() + $("#mh-footer").height() + 40
                    })
                    setCursor("#mh-footer");
                  })
                  if(!_this.showAppHeader && isMobileDevice()){$("#header").hide();}
                  else if(_this.showAppHeader && isMobileDevice()){$("#header").show();}
                  if(isMobileDevice()){$("#footer").hide();}
                }
              }
            }
          })
        },
        afterIframeFunc: function(){
            var vue = this;
            vue.getLeftNavs();
            if(vue.showBayWindow){
                vue.$refs.bayWindow.init();
            }
            if(vue.showGlobalIframe  && vue.webJson.setting.globalIframe){
                vue.$refs.globalIframe.init(vue.webJson.setting.globalIframe);
            }
            // 背景音乐
            if(vue.webJson.setting.musicStyle && vue.webJson.setting.musicStyle.show){
                vue.musicStyle = vue.webJson.setting.musicStyle;
                vue.$refs.bgMusic.init(vue.webJson.setting.musicStyle);
            }
            indexPage.toggleDragItem(false);
        },
        //获取左侧导航
        getLeftNavs: function () {
            var _this = this;
            if(vue.webJson.setting.hasOwnProperty('leftNavs')){
                this.leftNavSets = vue.webJson.setting.leftNavSets;
                this.leftNavs = vue.webJson.setting.leftNavs;
                this.$nextTick(function (){
                    _this.canvasInit();
                    if (vue.webJson.aid.length > 0) {
                        _this.newWidgetCallback();
                        _this.canvasContentShow();
                    }
                    indexPage.getDatas({
                        "full-banner":_this.setBannerH
                    });
                    _this.judgeScrollPos();
                    //    判断是否有地图
                    _this.isMapFunc();

                })
                return
            }
            $.ajax({
                url: '/engine2/navigation/bererwai/div'+getSfidFunc(),
                data: {
                    "pageId": this.pageId,
                    wfwfid:this.wfwfid
                },
                success: function (res) {
                    if (res.code == 1) {
                        _this.leftNavSets = res.data.navigation;
                        _this.leftNavs = res.data.navigation.itemList;
                        _this.$nextTick(function () {
                            _this.canvasInit();
                            _this.setModuleData();
                            _this.judgeScrollPos();
                        });
                    }
                }
            })
        },
        //获取固定模块
        getFixedModule:function () {
            var _this = this;
            if (vue.webJson.setting.hasOwnProperty('pekingLibraryDiv') && vue.webJson.setting.pekingLibraryDiv) {
                _this.fixedModuleRender(vue.webJson.setting.pekingLibraryDiv);
            }else {
                $.ajax({
                    url: '/engine2/peking/library/div' + getSfidFunc(),
                    data: {
                        "pageId": this.pageId,
                        wfwfid: this.wfwfid
                    },
                    success: function (res) {
                        if (res.code == 1) {
                            _this.fixedModuleRender(res.data);
                        }
                    }
                })
            }
            //    判断是否有iframe插件
            this.hasiFramePlugin();
            //判断是否有 富文本插件
            this.hasRichtextPlugin();
            //移动端添加第一个画布的背景
            this.appFirstBgFunc();
        },
        fixedModuleRender: function(resDiv){
            if (resDiv.shortcutDiv) {
                var posClass;
                switch(resDiv.shortcutPosition) {
                        // 1:左侧靠边，2:右侧靠边，3:左侧靠内容，4:右侧靠内容
                    case "1":
                        posClass="screen-left";
                        break;
                    case "2":
                        posClass="screen-right";
                        break;
                    case "3":
                        posClass="content-left";
                        break;
                    case "4":
                        posClass="content-right";
                        break;
                    default:
                }
                $("#quickEntry").addClass(posClass);
                $("#quickEntry").append(resDiv.shortcutDiv);

                if(resDiv.shortcutPosition=="3"){
                    var marginLeft=parseFloat($("#quickEntry").width()+ parseFloat($(".grid-wrap").width())/2);
                    $("#quickEntry").css("margin-left",-marginLeft+'px' )
                }else if(resDiv.shortcutPosition=="2"){
                    $("#quickEntry .general-set").css({
                        "right":"0px",
                        "left":"auto"
                    })
                }
            }
            if(resDiv.scrollToTopDiv){
                $("#toTop").append(resDiv.scrollToTopDiv)
            }
            if (window.engineInitCallback && typeof engineInitCallback == 'function') {
                engineInitCallback("#quickEntry");
            }
        },
        //根据导航数据进行添加，删除
        setModuleData: function () {
            var newAids = [];
            var _this = this;
            var aids = _this.webJson.aid;
            this.leftNavs.forEach(function (item, index) {
                var navId = item.id;
                var isExist = false;
                for (var i = 0; i < aids.length; i++) {
                    var jsonId = aids[i].navId;
                    if (navId == jsonId) {
                        isExist = true;
                        newAids.push(aids[i]);
                        break
                    }
                }
                if (!isExist) {
                    newAids.push({
                        "page": 1,
                        "app": [],
                        "navId": navId
                    });
                }
            });
            this.webJson.aid = newAids;
            if (this.webJson.aid.length > 0) {
                this.newWidgetCallback();
                this.canvasContentShow();
                indexPage.getDatas({
                    "full-banner":this.setBannerH
                });
            }
        },
        // 计算banner 的高度
        setBannerH:function () {
            setTimeout(function () {
                var bannerH=$("#banner").height();
                $("#firstSection").css({
                    "min-height":bannerH
                })
            },1000)
        },
        //画布中模块位置的回显
        canvasContentShow: function () {
            var _this = this;
            $(".grid-stack").each(function (index) {
                if (!_this.webJson.aid[index]) {
                    return
                }
                var grid = $(this).data('gridstack');
                var items = vue.webJson.aid[index].app;
                jsonSort(items,'y','x');
                items.forEach(function (node) {
                    var moduleHtml = '', container;
                    if (node.baseModule) {
                        node.baseModule.forEach(function (item, index) {
                            moduleHtml += item.html;
                        });
                    }
                    container = '<div data-app-id="' + node.appId + '" class="grid-stack-item"><div class="grid-stack-item-content">' + moduleHtml + '</div></div>';
                    grid.addWidget($(container),
                        node.x, node.y, node.width, node.height);
                    indexPage.reSetDrag(".grid-stack");
                    indexPage.toggleDragItem(false);
                });
            });
            this.getFixedModule();
        },
        //调用toast
        showToastEvent: function (message, type) {
            var _this = this;
            this.toastMessage = message;
            this.toastType = type;
            this.showToast = true;
            setTimeout(function () {
                _this.showToast = false;
            }, 2000)
        },
        // 锚点跳转带动画
        anchorJump: function () {
            $("#index").on("click", "#anchorsNav a", function () {
                var _this=this;
                var className=$(this).parents(".anchors-nav").find(".active").attr("class");
                var className1 = $(this).parents().attr("class");
                if($(this).hasClass("active")){
                    return
                }
                if (location.pathname.replace(/^\//, '') == this.pathname.replace(/^\//, '') && location.hostname == this.hostname) {
                    var $target = $(this.hash);
                    $target = $target.length && $target || $('[name=' + this.hash.slice(1) + ']');
                    if ($target.length) {
                        var targetOffset = $target.offset().top;
                        $('html,body').animate({scrollTop: targetOffset}, 400,function () {
                            $(_this).parents("li").attr("class",className).siblings().attr("class","");
                            // 新样式6
                            if(vue.leftNavSets.typeId == 6 || vue.leftNavSets.typeId == 8){
                                $(_this).parents("li").attr("class", className).siblings().attr("class", className1);
                            }
                        });
                        return false;
                    }
                }
            });
        },
        // 判断当前滚动位置
        judgeScrollPos: function () {
            var _this = this;
            var cssRule=[];
            var className="active ";
            if(vue.leftNavSets.typeId!==4){
                if (vue.leftNavSets.backgroudType === 0 && (vue.leftNavSets.typeId === 1 || vue.leftNavSets.typeId === 5 || vue.leftNavSets.typeId === 6 || vue.leftNavSets.typeId === 8)) {
                    // 背景色随主题色
                    className += ' bg-theme';
                } else if (vue.leftNavSets.backgroudType === 1 && (vue.leftNavSets.typeId === 1 || vue.leftNavSets.typeId === 5)) {
                    // 自定义背景
                    className += ' bg-custom';
                    cssRule = [{
                        selector: '#anchorsNav .bg-custom',
                        rule: 'background:' + vue.leftNavSets.backgroudColor + ';'
                    }];
                    updateColor.updateCSSRule(cssRule);
                }
            }
            $(".anchors-nav>li").eq(0).addClass(className);
            var scrollTop = $(window).scrollTop();
            if(scrollTop<10 && vue.leftNavSets.showFirst==0){
                $("#anchorsNav").hide();
            }
            $(window).on("scroll", function () {
                var top = $(this).scrollTop();
                var windinH = $(window).height();
                var distance = top-windinH;
                _this.judgeShowToTop(distance);
                if(vue.leftNavSets.showFirst==0){
                    // if (top > windinH -400) {  // 400为第一屏在可视区域剩下的高度
                    if (top > windinH) {  // 400为第一屏在可视区域剩下的高度
                        $("#anchorsNav").show();
                    } else {
                        $("#anchorsNav").hide();
                    }
                }
                $(".grid-wrap").each(function (index) {
                    var MARGINBOTTOM = 300;  //上一个模块距离顶部还有300的时候，左侧导航激活下一个
                    var gridTop = $(this).offset().top;
                    var gridHeight = $(this).height();
                    var navObj = $(".anchors-nav>li").eq(index);
                    if (top >= gridTop - MARGINBOTTOM && top <= gridTop + gridHeight) {
                        if (!navObj.hasClass("active")) {
                            navObj.addClass(className).siblings().removeClass(className);
                        }
                    }
                })
            })
        },
        // 判断当前位置，是否显示返回顶部
        judgeShowToTop: function (top) {
            this.showToTopDis = top > - 400 ? true : false;
        },

        //    判断是否有地图
        isMapFunc:function () {
            var hasMap = $('.grid-stack .item-drag-wrap').hasClass('map-module-container');
            if(hasMap){
                // 获取地图接口
                $.ajax({
                    url:'//hd.chaoxing.com/api/activity/address?pageId='+this.pageId,
                    type:'get',
                    success: function (res) {
                        if(res.data){
                            var point={};
                            point.lng = res.data.longitude;
                            point.lat = res.data.dimension;
                            indexPage.judgeMaps(point);
                        }else{
                            indexPage.judgeMaps();//初始化百度地图
                        }
                    }
                })
            }
        },
        //判断是否有iframe插件
        hasiFramePlugin: function () {
            var _this = this;
            var hasPlugin = $('.grid-stack .item-drag-wrap').hasClass('plugin-module-container');
            if(hasPlugin){
                var length = $('.grid-stack .plugin-module-container').length;
                if(length < 1){
                    return;
                }
                $('.grid-stack .plugin-module-container').each(function (index,item) {
                    var iframe = $(item).find('.item-main>iframe').attr('src');
                    var ifAgain = $(item).find('.item-main>iframe').attr('getagain');
                      if(ifAgain=='true'){
                        /*优化嵌套文本---评论*/
                        $(item).parents('.grid-stack-item-content').find('.text-module-container').hide();
                        var str = '<div class="comment-title">评论</div>'
                        $(item).find('.item-main>iframe').before(str);
                        $(item).find('.item-main>iframe').css('background-color','white');

                          var src1 = $(item).find('.item-main>iframe').attr('againsrc');
                            $.ajax({
                              url: src1,
                              type: 'get',
                              async: false,
                              data:{
                                websiteId: _this.websiteId,
                                url: encodeURIComponent(window.location.href),
                                loginUrl: window.location.protocol+'//' + window.location.host +'/login?referer=' + window.location,
                                  resourceType: 6,
                                  resourceTypeName: '活动'
                              },
                              success: function (res) {
                                console.log('获取克隆之后的评论地址并修改res',res)
                                if(res.code == 1){
                                  $(item).find('.item-main>iframe').attr('src', '');
                                  $(item).find('.item-main>iframe').attr('src', res.data);
                                  _this.commentIframe = $(item).find('.item-main>iframe')[0];
                                  /*父页面向子页面iframe传递cookie*/
                                  setTimeout(function () {
                                    var iframe1 =  $(item).find('.item-main>iframe')[0];
                                    if(iframe1.attachEvent){
                                      iframe1.attachEvent('onload',function () {_this.transCookie(iframe1);})}
                                    else{iframe1.onload=function () {_this.transCookie(iframe1);}}
                                  },300)
                                }
                              }
                            });
                      }else{
                          var iframe1 = _this.appendQuery(iframe,'pageId',_this.pageId);
                          iframe1 = _this.appendQuery(iframe1,'wfwfid',_this.wfwfid);
                          iframe1 = _this.appendQuery(iframe1,'websiteId',_this.websiteId);
                          var iframeBox = $(item).find('.item-main>iframe');
                          /*解决iframe加参数之后不刷新问题*/
                          if(iframeBox.attachEvent){
                              iframeBox.attachEvent('onload', function () {if(iframe !== iframe1){iframeBox.attr('src',iframe1);}})
                          }else {
                              iframeBox.onload = function () {if(iframe !== iframe1){iframeBox.attr('src',iframe1);}}
                          }
                          setTimeout(function () {
                              if(iframe !== iframe1){iframeBox.attr('src',iframe1);}
                          },300)

                      }
                })
            }
        },
        appendQuery: function (url,key,value) {
            if(url.indexOf(key) > -1){
                return url;
            }
            var options = key;
            if(typeof options == 'string'){
                options={};
                options[key] = value;
            }
            options = $.param(options);
            if(url.includes('?')){
                url += '&'+options;
            }else{
                url += '?'+options;
            }
            return url;
        },
      noteRequestCookie: function (e) {
        var data = e.data;
        if (data === 'noteRequestCookie') {
          this.transCookie(this.commentIframe);
        }
      },
        // 父页面向子页面iframe传递cookie
        transCookie: function (iframe) {
            if (iframe.contentWindow && iframe.contentWindow.postMessage) {
                iframe.contentWindow.postMessage({'cookies': document.cookie }, '*');
            }
        },
        //
        hasRichtextPlugin: function () {
            var hasPlugin = $('.grid-stack .item-drag-wrap').hasClass('richtext-module-container');
            if(hasPlugin){
                var length = $('.grid-stack .richtext-module-container').length;
                if(length < 1){
                    return;
                }
                var obj =  $('.grid-stack .richtext-module-container');
                var appwidth = $(obj).find('.richtext-module').attr('appWidth');
                var isMobile = isMobileDevice();
                if(appwidth && isMobile){
                    $(obj).find('.richtext-module').css('width',appwidth + '%');
                }
            }

        },
        //第一个画布的背景移动端添加
        appFirstBgFunc: function () {
            var _this = this;
            var isMobile = isMobileDevice();
            if(isMobile){
                //多张背景图
                if(_this.pageBgs[0].img){
                    //图片背景
                    $('#firstSection').css({
                        'background-image': 'url(' + _this.pageBgs[0].img + ')',
                        'background-position': 'center center',
                        'background-size': 'cover',
                    })
                }else{
                    //颜色背景
                    $('#firstSection').css({
                        'background-color': _this.pageBgs[0].bgColor,
                    })
                }
            }
        },
        getCookieUserInfo: function () { /* 后续后话，放到活动模板里调用一次此接口，暴露公共属性使用*/
            var _this = this;
            $.ajax({
                type: "post",
                url: "/cookie/get?name=UID,vc3,_d,fid",
                async: true, //同步方法
                dataType: "json",
                success: function (data) {
                    var data = data.data;
                    if (data) {
                        _this.uid = data['UID'];
                        _this._d = data['_d'];
                        _this.vc3 = data['vc3'];
                        _this.ufid = data['fid'];
                    }
                },
            })
        }
    }
});


