/**
 * Created by admin on 2020/3/19.
 */
Vue.component("globalIframeSet", {
    template: '<div id="globalIframeSet" class="global-iframe-pop pop">' +
    '              <div class="pop-content">' +
    '                  <div class="top clear">' +
    '                       <span class="fs16 colo666 fl">全局插件设置</span>' +
    '                       <i class="icon icon-del fr" @click="closePop"></i>' +
    '                  </div>' +
    '                  <div class="middle fs14 col333">' +
    '                       <div class="set-item">' +
    '                           <span class="left-label">输入连接地址:</span>' +
    '                           <div class="in-block items">' +
    '                           <input class="select-inp" type="text" placeholder="请输入http://或https://开头的链接" v-model="setData.url"/>' +
    '                             </div>' +
    '                        </div>' +
    '                        <div class="set-item">\n' +
    '                            <span class="left-label">显示位置:</span>\n' +
    '                              <div class="in-block">' +
    '                                 <label v-for="pos in positionArr"><input type="radio" :checked="pos.id==setData.position" name="show-location" @click="changePos(pos.id)">{{pos.text}}</label>' +
    '                               </div>' +
    '                        </div>\n' +
    '                       <div class="set-item">' +
    '                           <span class="left-label">最小化文字标识:</span>' +
    '                           <div class="in-block items">' +
    '                                <input class="select-inp" type="text" placeholder="请输入最小化时的文字" v-model="setData.miniText"/>' +
    '                           </div>' +
    '                        </div>' +
    '                       <div class="set-item">' +
    '                           <span class="left-label">宽度:</span>' +
    '                           <div class="in-block items">' +
    '                                <input class="select-inp" type="number" placeholder="请输入宽度" v-model="setData.width"/>' +
    '<p class="fs12 col999 margint4">内容过宽可能覆盖其他区域内容 </p>' +
    '                           </div>' +
    '                        </div>' +
    '                       <div class="set-item">' +
    '                           <span class="left-label">高度:</span>' +
    '                           <div class="in-block items">' +
    '                                <input class="select-inp" type="number" placeholder="请输入高度" v-model="setData.height"/>' +
    '<p class="fs12 col999 margint4">内容过高可能覆盖其他区域内容 </p>' +
    '                           </div>' +
    '                        </div>' +
    '                        <div class="set-item">' +
    '                             <span class="left-label">显示滚动条：</span>' +
    '                             <div  class="in-block items select-list-wrap">' +
    '                                   <label class="switch">' +
    '                                        <input type="checkbox" class="scrollCheckbox" :checked="setData.showScroll">' +
    '                                        <div class="slider"><em  class="open">开</em><em class="close">关</em></div>' +
    '                                   </label>' +
    '                             </div>' +
    '                            </div>' +
    '                     </div>' +
    '                     <div class="bottom-btns">' +
    '                            <input class="btn btn-fff-auto" type="button" value="取消" @click="closePop">' +
    '                             <input class="btn btn-blue-auto" type="button" value="保存" @click="saveSet">' +
    '                     </div>' +
    '              </div>' +
    ' </div>',
    data: function () {
        return {
            positionArr: [{
                id: '1',
                text: "左侧靠边"
            }, {
                id: '2',
                text: "左侧靠内容"
            }, {
                id: '3',
                text: "右侧靠边"
            }, {
                id: '4',
                text: "右侧靠内容"
            }],
            setData: {
                url: "",
                position: '1',
                miniText: "最小化文字提示",
                width: 200,
                height: 200,
                showScroll:false
            },
            currData: {}
        }
    },
    mounted: function () {

    },
    methods: {
        init: function () {
            this.currData = JSON.parse(sessionStorage.getItem('globalIframeData'));
            this.$set(this.setData, 'url', this.currData.iframeData.src);
            this.$set(this.setData, 'position', this.currData.iframeData.position);
            this.$set(this.setData, 'miniText', this.currData.iframeData.minStatuText);
            this.$set(this.setData, 'width', this.currData.iframeData.width);
            this.$set(this.setData, 'height', this.currData.iframeData.height);
            this.$set(this.setData, 'showScroll', this.currData.iframeData.showScroll);
        },
        // 打开弹框
        openPop: function () {
            $("#globalIframeSet").fadeIn();
            unScrollPage();
        },
        // 关闭弹框
        closePop: function () {
            $("#globalIframeSet").fadeOut();
            removeUnScrollPage();
        },
        // 切换iframe的位置
        changePos: function (val) {
            this.$set(this.setData, 'position', val);
        },
        // 保存数据
        saveSet: function () {
            var setData = this.$parent.$refs.globalIframe.setDatas[this.currData.index];
            // 开启关闭滚动条
            var scrollCheckbox =  $("#globalIframeSet .scrollCheckbox").is(':checked');
            this.$set(this.setData,'showScroll',scrollCheckbox);
            this.$set(setData, 'showScroll', this.setData.showScroll);
            this.$set(setData, 'src', this.setData.url);
            this.$set(setData, 'position', this.setData.position);
            this.$set(setData, 'minStatuText', this.setData.miniText);
            this.$set(setData, 'width', this.setData.width);
            this.$set(setData, 'height', this.setData.height);
            this.closePop();
            //  显示位置为左侧靠内容的处理
            var $this=this;
            this.$nextTick(function () {
                var content = $("#globalIframe .gloabl-iframe-container").eq($this.currData.index);
                if ($this.setData.position == 2) {
                    var marginLeft = - parseInt(content.width())- 600 + 'px';
                    content.css("margin-left", marginLeft);
                }else if($this.setData.position == 1 || $this.setData.position==3){
                    content.css("margin-left", "0px");
                }else if($this.setData.position == 4){
                    content.css("margin-left", "600px");
                }
            })
        }
    }
});
