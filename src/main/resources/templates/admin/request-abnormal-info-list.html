<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org/">

<head>
    <meta charset="UTF-8">
    <title>高级选项</title>
    <link rel="stylesheet" th:href="@{/assets/icomoon/style.css?v=0722}">
    <link rel="stylesheet" th:href="@{/assets/lib/select2/select2.min.css?v=0722}">
    <!--滚动条美化-->
    <link rel="stylesheet" th:href="@{/assets/lib/mCustomScrollBar/jquery.mCustomScrollbar.css?v=0722}">
    <link rel="stylesheet" th:href="@{/assets/css/common.css?v=0722}">
    <link rel="stylesheet" th:href="@{/assets/css/website/advanced.css?v=0724}">
    <style>
        [v-cloak] {
            display: none;
        }
    </style>
</head>

<body>
<div id="blank-domain-list-vue" v-cloak>
    <div class="white-list">
        <div class="page-content" style="padding-top: 20px">
            <div class="temp-list manager-list">
                <table class="ui-table" border="1">
                    <tr>
                        <th style="width: 10%;">序号</th>
                        <th style="width: 25%;">请求地址</th>
                        <th style="width: 55%;">异常信息</th>
                        <th style="width: 10%;">时间</th>
                    </tr>

                    <tr v-for="(requestAbnormalInfo,index) in requestAbnormalInfoList">
                        <td>{{index+1}}</td>
                        <td>{{requestAbnormalInfo.url}}</td>
                        <td>{{requestAbnormalInfo.exceptionInfo}}</td>
                        <td>{{requestAbnormalInfo.createTime}}</td>
                    </tr>
                </table>
            </div>

            <!-- 分页 -->
            <div id="laypage" class="laypage-style"></div>
        </div>
    </div>

</div>

<script th:src="@{/assets/lib/jquery.min.js?v=0312}"></script>
<script th:src="@{/assets/lib/jquery-migrate.min.js?v=0312}"></script>
<script th:src="@{/assets/lib/mCustomScrollBar/jquery.mousewheel.min.js?v=0312}"></script>
<script th:src="@{/assets/lib/mCustomScrollBar/jquery.mCustomScrollbar.min.js?v=0312}"></script>
<script th:src="@{/assets/lib/select2/select2.min.js?v=0312}"></script>
<script th:src="@{/assets/js/common.js?v=0312}"></script>
<script th:src="@{/assets/js/dropzone.min.js}"></script>
<script th:src="@{/assets/js/component.fileupload.js}"></script>

<link rel="stylesheet" th:href="@{/assets/lib/layui/css/layui.css?v=020911}" media="all">
<script th:src="@{/assets/lib/layui/layui.js?v=020911}" charset="utf-8"></script>
<script th:src="@{/assets/lib/vue.js?v=0312}"></script>

<script th:inline="javascript">
    var ctx = [[${#servletContext.contextPath}]];
    var whiteListVue;

    $(function () {
        whiteListVue = new Vue({
            el: "#blank-domain-list-vue",
            data: {
                requestAbnormalInfoList: [],
                total: 0,
                pageNum: 1,
                pageSize: 10,
                pager: null,
                flag: true
            },
            created: function () {

            },
            mounted: function () {
                var $this = this;
                $this.loadDatas();
            },
            methods: {
                loadDatas: function () {
                    var $this = this;
                    var params = {
                        pageNum: $this.pageNum,
                        pageSize: $this.pageSize
                    };
                    $.post(ctx + '/sadmin/request-abnormal-infos', params, function (data) {
                        if (data.code == 1) {
                            $this.requestAbnormalInfoList = data.data.list;
                            $this.total = data.data.total;
                            $this.initPager();
                        }
                    });
                },
                initPager: function () {
                    var $this = this;
                    layui.use(['laypage'], function () {
                        var laypage = layui.laypage;
                        laypage.render({
                            elem: 'laypage',
                            curr: $this.pageNum,
                            limit: $this.pageSize,
                            count: $this.total,
                            layout: ['count', 'prev', 'page', 'next', 'limit', 'refresh', 'skip'],
                            jump: function (obj, first) {
                                $this.pageNum = obj.curr;
                                $this.pageSize = obj.limit;
                                if (!first) {
                                    $this.loadDatas();
                                }

                            }
                        });
                    });
                },
                showMsg: function (msg) {
                    layui.use('layer', function () {
                        var layer = layui.layer;
                        layer.msg(msg);
                    });
                },
                isEmpty: function (str) {
                    return typeof(str) == 'undefined' || str == null || "" === $.trim(str);
                },
                // true:数值型的，false：非数值型
                checkNumber: function (theObj) {
                    var reg = /^[0-9]+.?[0-9]$/;
                    var reg1 = /^[0-9]$/;
                    if (reg.test(theObj)) {
                        return true;
                    }
                    if (reg1.test(theObj)) {
                        return true;
                    }
                    return false;
                }

            }
        });
    });

</script>
</body>

</html>