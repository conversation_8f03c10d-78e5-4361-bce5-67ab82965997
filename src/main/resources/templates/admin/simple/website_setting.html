<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org/">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <link rel="stylesheet" th:href="@{/assets_dy/js/manage_admin/toast/index.css}">
    <link rel="stylesheet" th:href="@{/assets/icomoon/style.css}">
    <link rel="stylesheet" th:href="@{/assets_dy/css/manage_admin/public.css}">
    <link rel="stylesheet" th:href="@{/assets/lib/element-ui/lib/theme-chalk/index.css}">
    <link rel="stylesheet" th:href="@{/assets_dy/css/manage_admin/website_setting.css}">
    <style>
        [v-cloak] {
            display: none;
        }
        .c_message_wrapper .c_base_icon{
            display: inline-flex;
        }
    </style>
</head>

<body>
<div class="website-setting" id="website-setting" v-cloak>
    <div class="setting-form">
        <h2 class="inner-title">基本信息</h2>
        <div class="setting-form-item">
            <div class="setting-form-item-label">网站名称</div>
            <div class="setting-form-item-content">
                <input type="text" class="website-name" v-model="website.name">
            </div>
        </div>
        <div class="setting-form-item">
            <div class="setting-form-item-label">网站简介</div>
            <div class="setting-form-item-content">
                <textarea name="" id="" v-model="website.intro" placeholder="请输入"></textarea>
            </div>
        </div>
        <div class="setting-form-item">
            <div class="setting-form-item-label">网站图标
                <el-tooltip popper-class="item-tips" effect="dark" placement="top">
                    <div slot="content">
                        <p>示例图</p>
                        <img src="../../assets/images/manage_admin/mh_bg_1.jpg" height="39" width="189"/>
                    </div>
                    <i class="icon-mh-info"></i>
                </el-tooltip>
            </div>
            <div class="setting-form-item-content">
                <div class="logo-content">
                    <div class="logo-box">
                        <div class="logo-upload-box">
                            <div class="logo-img failure-box" v-if="icon.uploadStatus == 2">
                                <div class="fail-content">
                                    <img src="../../assets/images/manage_admin/attention.png" alt="">
                                    <p>上传失败</p>
                                    <div class="retry-btn" @click="retryUpload('icon')">重试</div>
                                </div>
                            </div>
                            <div class="logo-img" v-else-if="!icon.url" v-loading="icon.loading">
                                <div class="transparent-bg"></div>
                            </div>
                            <div class="logo-img" v-else-if="icon.url">
                                <img class="imgSrc" :src="icon.url" alt="">
                                <div class="delete-btn">
                                    <span class="delete-icon" @click="removeResource('icon')"></span>
                                </div>
                            </div>
                            <div class="right-upload-btn">
                                <el-upload class="upload-demo"
                                           ref="icon"
                                           action="#"
                                           accept="image/x-icon,image/jpeg,image/jpg,image/png"
                                           :show-file-list="false"
                                           :http-request="(file) => uploadFile('icon', file)">
                                    <p>格式：PNG/JPG/ICO<br>大小：32x32，16KB以下</p>
                                    <div class="upload-btn">上传</div>
                                </el-upload>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div style="background: #F0F0F0;margin: 20px 0;height: 1px;"></div>
        <h2 class="inner-title">网站素材</h2>
        <div class="setting-form-item">
            <div class="setting-form-item-label">单位Logo
                <el-tooltip popper-class="item-tips" effect="dark" placement="top">
                    <div slot="content">
                        <p>示例图</p>
                        <img src="../../assets/images/manage_admin/mh_bg_2.jpg" height="137" width="301"/>
                    </div>
                    <i class="icon-mh-info"></i>
                </el-tooltip>
            </div>
            <div class="setting-form-item-content">
                <div class="logo-content">
                    <div class="logo-box">
                        <p class="logo-txt">浅色logo</p>
                        <div class="logo-upload-box">
                            <div class="logo-img failure-box" v-if="qmxLogo1.uploadStatus == 2">
                                <div class="fail-content">
                                    <img src="../../assets/images/manage_admin/attention.png" alt="">
                                    <p>上传失败</p>
                                    <div class="retry-btn" @click="retryUpload('qmxLogo1')">重试</div>
                                </div>
                            </div>
                            <div class="logo-img" v-else-if="!qmxLogo1.url" v-loading="qmxLogo1.loading">
                                <div class="transparent-bg"></div>
                            </div>
                            <div class="logo-img" v-else-if="qmxLogo1.url">
                                <img class="imgSrc" :src="qmxLogo1.url" alt="">
                                <div class="delete-btn">
                                    <span class="delete-icon" @click="removeResource('qmxLogo1')"></span>
                                </div>
                            </div>
                            <div class="right-upload-btn">
                                <el-upload class="upload-demo"
                                           ref="qmxLogo1"
                                           action="#"
                                           accept="image/gif,image/jpeg,image/jpg,image/png"
                                           :show-file-list="false"
                                           :http-request="(file) => uploadFile('qmxLogo1', file)">
                                    <p>格式：PNG/JPG/GIF<br>大小：5M以下</p>
                                    <div class="upload-btn">上传</div>
                                </el-upload>
                            </div>
                        </div>
                    </div>
                    <div class="logo-box">
                        <p class="logo-txt">深色logo</p>
                        <div class="logo-upload-box">
                            <div class="logo-img failure-box" v-if="qmxLogo2.uploadStatus == 2">
                                <div class="fail-content">
                                    <img src="../../assets/images/manage_admin/attention.png" alt="">
                                    <p>上传失败</p>
                                    <div class="retry-btn" @click="retryUpload('qmxLogo2')">重试</div>
                                </div>
                            </div>
                            <div class="logo-img" v-else-if="!qmxLogo2.url" v-loading="qmxLogo2.loading">
                                <div class="transparent-bg"></div>
                            </div>
                            <div class="logo-img" v-else-if="qmxLogo2.url">
                                <img class="imgSrc" :src="qmxLogo2.url" alt="">
                                <div class="delete-btn">
                                    <span class="delete-icon" @click="removeResource('qmxLogo2')"></span>
                                </div>
                            </div>
                            <div class="right-upload-btn">
                                <el-upload class="upload-demo"
                                           ref="qmxLogo2"
                                           action="#"
                                           accept="image/gif,image/jpeg,image/jpg,image/png"
                                           :show-file-list="false"
                                           :http-request="(file) => uploadFile('qmxLogo2', file)">
                                    <p>格式：PNG/JPG/GIF<br>大小：5M以下</p>
                                    <div class="upload-btn">上传</div>
                                </el-upload>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--<div class="setting-form-item">
            <div class="setting-form-item-label">图片</div>
            <div class="setting-form-item-content">
                <div class="logo-content">
                    <div class="logo-box type2">
                        <div class="logo-upload-box">
                            <div class="logo-img type2 failure-box" v-if="qmxImage.uploadStatus == 2">
                                <div class="fail-content">
                                    <img src=".././../assets_dy/images/manage_admin/attention.png" alt="">
                                    <p>上传失败</p>
                                    <div class="retry-btn" @click="retryUpload('qmxImage')">重试</div>
                                </div>
                            </div>
                            <div class="logo-img" v-else-if="!qmxImage.url" v-loading="qmxImage.loading">
                                <div class="transparent-bg"></div>
                            </div>
                            <div class="logo-img type2" v-else-if="qmxImage.url">
                                <img :src="qmxImage.url" alt="">
                                <div class="delete-btn">
                                    <span class="delete-icon" @click="removeResource('qmxImage')"></span>
                                </div>
                            </div>
                            <div class="right-upload-btn">
                                <el-upload class="upload-demo"
                                           ref="qmxImage"
                                           action="#"
                                           accept="image/gif,image/jpeg,image/jpg,image/png"
                                           :show-file-list="false"
                                           :http-request="(file) => uploadFile('qmxImage', file)">
                                    <p>格式：PNG/JPG/GIF<br>大小：5M以下</p>
                                    <div class="upload-btn">上传</div>
                                </el-upload>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>-->
        <div class="setting-form-item">
            <div class="setting-form-item-label">首屏背景
                <el-tooltip popper-class="item-tips"  effect="dark" placement="top">
                    <div slot="content">
                        <p>示例图</p>
                        <img src="../../assets/images/manage_admin/mh_bg_3.jpg" height="137" width="301"/>
                    </div>
                    <i class="icon-mh-info"></i>
                </el-tooltip>
            </div>
            <div class="setting-form-item-content">
                <div class="logo-content">
                    <div class="logo-box type2">
                        <div class="logo-upload-box">
                            <div class="logo-img type2 failure-box" v-if="qmxVideo.uploadStatus == 2">
                                <div class="fail-content">
                                    <img src=".././../assets/images/manage_admin/attention.png" alt="">
                                    <p>上传失败</p>
                                    <div class="retry-btn" @click="retryUpload('qmxVideo')">重试</div>
                                </div>
                            </div>
                            <div class="logo-img" v-else-if="!qmxVideo.cover" v-loading="qmxVideo.loading">
                                <div class="transparent-bg"></div>
                            </div>
                            <div class="logo-img type2" v-else-if="qmxVideo.cover">
                                <img class="imgSrc" :src="qmxVideo.cover" alt="">
                                <div class="delete-btn">
                                    <span class="delete-icon" @click="removeResource('qmxVideo')"></span>
                                </div>
                            </div>
                            <div class="right-upload-btn">
                                <el-upload class="upload-demo"
                                           ref="qmxVideo"
                                           action="#"
                                           accept="video/mp4"
                                           :show-file-list="false"
                                           :http-request="(file) => uploadFile('qmxVideo', file)">
                                    <p>格式：MP4<br>大小：20M以下</p>
                                    <div class="upload-btn">上传</div>
                                </el-upload>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="save-btn" @click="saveWebsiteConfig()">保存</div>
</div>
</body>
<script th:src="@{/assets/lib/jquery.min.js?v=0312}"></script>
<script th:src="@{/assets/lib/vue.js}"></script>
<script th:src="@{/assets/lib/element-ui/lib/index.js}"></script>
<script th:src="@{/assets_dy/js/manage_admin/toast/index.js}"></script>
<script th:inline="javascript">
    var ctx = [[${#servletContext.contextPath}]];
    var vm = new Vue({
        el: '#website-setting',
        data() {
            return {
                website: [[${website}]],
                otherConfig: [[${otherConfig}]],
                icon: {
                    uploadStatus: 0,
                    message: '',
                    url: '',
                    loading: false
                },
                qmxLogo1: {
                    uploadStatus: 0,
                    message: '',
                    url: '',
                    loading: false
                },
                qmxLogo2: {
                    uploadStatus: 0,
                    message: '',
                    url: '',
                    loading: false
                },
                qmxImage: {
                    uploadStatus: 0,
                    message: '',
                    url: '',
                    loading: false
                },
                qmxVideo: {
                    uploadStatus: 0,
                    message: '',
                    cover: '',
                    url: '',
                    objectId: '',
                    loading: false
                },
            }
        },
        created() {
            this.qmxLogo1.url = this.otherConfig.logo1;
            this.qmxLogo2.url = this.otherConfig.logo2;
            this.qmxImage.url = this.otherConfig.imageUrl;
            this.qmxVideo.url = this.otherConfig.videoUrl;
            this.qmxVideo.cover = this.otherConfig.videoCoverUrl;
            this.qmxVideo.objectId = this.otherConfig.objectId;
            this.icon.url = this.website.favicon;
        },
        methods: {
            uploadFile(name, file) {
                if (!file) {
                    Toast({
                        type: 'error',
                        msg: '请选择文件',
                        isRem: false,
                        theme: 'dark'
                    });
                    return
                }

                let _that = this;
                _that.removeResource(name);
                _that[name].uploadStatus = 0;
                _that[name].loading = true;
                let formDatas = new FormData()
                formDatas.append('uploadFile', file.file);
                let url = ctx + '/usts/img';
                if (name === 'qmxVideo') {
                    url = '/engine2/usts';
                } else if (name === 'icon') {
                    url = ctx + '/usts/ico';
                }
                // 添加错误处理
                $.ajax({
                    url: url,
                    type: 'POST',
                    contentType: false,
                    processData: false,
                    data: formDatas,
                    success: function (data) {
                        _that[name].loading = false;
                        if (data.code == 1) {
                            _that[name].uploadStatus = 1;
                            if (name === 'qmxVideo') {
                                _that[name].cover = data.data.coverUrl ? data.data.coverUrl : data.data.url;
                                _that[name].url = data.data.url;
                                _that[name].objectId = data.data.objectId;
                            } else {
                                _that[name].url = data.data.url;
                            }
                        } else {
                            _that[name].uploadStatus = 2;
                            _that[name].message = data.message;
                            Toast({
                                type: 'error',
                                msg: data.message,
                                isRem: false,
                                theme: 'dark'
                            });
                        }
                    },
                    error: function () {
                        _that[name].loading = false;
                        _that[name].uploadStatus = 2;
                        _that[name].message = '上传失败，请重试';
                        Toast({
                            type: 'error',
                            msg: '上传失败，请重试',
                            isRem: false,
                            theme: 'dark'
                        });
                    }
                });
            },
            retryUpload(name) {
                // 获取上传组件的DOM元素并模拟点击
                const uploadElement = this.$refs[name].$el.querySelector('input[type=file]');
                if (uploadElement) {
                    uploadElement.click();
                } else {
                    console.error('找不到上传组件的input元素');
                }
            },
            removeResource(name) {
                this[name].url = '';
                if (name === 'qmxVideo') {
                    this.qmxVideo.cover = '';
                }
            },
            saveWebsiteConfig() {
                let _that = this;
                $.ajax({
                    url: ctx + '/beginner-guide/save-website-config',
                    type: 'POST',
                    data: {
                        websiteId: _that.website.id,
                        name: _that.website.name,
                        logo1: _that.qmxLogo1.url,
                        logo2: _that.qmxLogo2.url,
                        imageUrl: _that.qmxImage.url,
                        videoUrl: _that.qmxVideo.url,
                        videoCoverUrl: _that.qmxVideo.cover,
                        videoObjectId: _that.qmxVideo.objectId,
                        introduction: _that.website.intro,
                        icon: _that.icon.url
                    },
                    success: function (data) {
                        if (data.code == 1) {
                            Toast({
                                type: 'success',
                                msg: '保存成功',
                                isRem: false,
                                theme: 'dark'
                            });
                        } else {
                            Toast({
                                type: 'error',
                                msg: data.message,
                                isRem: false,
                                theme: 'dark'
                            });
                        }
                    },
                    error: function () {
                        _that.$message.error('保存失败');
                    }
                });
            }
        },
    })
</script>
</html>