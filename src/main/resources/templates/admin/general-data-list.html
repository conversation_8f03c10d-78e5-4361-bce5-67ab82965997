<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org/">

<head>
    <meta charset="UTF-8">
    <title>高级选项</title>
    <link rel="stylesheet" th:href="@{/assets/icomoon/style.css?v=0722}">
    <link rel="stylesheet" th:href="@{/assets/lib/select2/select2.min.css?v=0722}">
    <!--滚动条美化-->
    <link rel="stylesheet" th:href="@{/assets/lib/mCustomScrollBar/jquery.mCustomScrollbar.css?v=0722}">
    <link rel="stylesheet" th:href="@{/assets/css/common.css?v=0722}">
    <link rel="stylesheet" th:href="@{/assets/css/website/advanced.css?v=0724}">
    <style>
        [v-cloak] {
            display: none;
        }
        .ui-table {
            table-layout: fixed;
        }
        .ui-table tr th {
            width: 200px;
        }
        .ui-table tr td {
            width: 200px;
            word-break: break-all;
        }
        .pop-content{
            max-height: 500px;
        }

        .middle{
            overflow: scroll;
            height: 300px;
        }

        .pop-content1{
            max-height: 700px;
            width: 1200px !important;
        }

        .middle1{
            overflow: scroll;
            height: 600px;
        }
    </style>
</head>

<body>
<div id="general-data-list" v-cloak>
    <div class="white-list">
        <div class="page-content" >
            <div class="temp-list manager-list">
                <div class="clear">
                    <span>InstanceId：</span><input placeholder="搜索InstanceId" name="kws" v-model="instanceId" autocomplete="off" value="" style="border: 1px solid #2aabd2;border-radius: 3px;" onkeyup="searchEnter(event,1)">
                <button class="btn-add" @click="search()">搜索</button>
                </div>
                <table class="ui-table" border="1">
                    <tr>
                        <th>id</th>
                        <th>标题</th>
                        <th>作者</th>
                        <th>封面</th>
                        <th>子标题</th>
                        <th>简介</th>
                        <th>数量</th>
                        <th>操作</th>
                    </tr>

                    <tr v-for="(generalData,index) in generalDataList">
                        <td>{{generalData.id}}</td>
                        <td>{{generalData.title}}</td>
                        <td>{{generalData.author}}</td>
                        <td>{{generalData.coverUrl}}</td>
                        <td>{{generalData.subtitle}}</td>
                        <td>{{generalData.introduction}}</td>
                        <td>{{generalData.number}}</td>
                        <td>
                            <button class="btn-add" @click="generalDataDetail(generalData)">详情</button>
                            <button class="btn-add" @click="editWebsiteDiv(generalData)">富文本内容</button>
                        </td>
                    </tr>
                </table>
            </div>

            <!-- 分页 -->
            <div id="laypage" class="laypage-style"></div>
        </div>
    </div>

    <!--弹框：查看富文本-->
    <div id="addManagerPop" class="pop add-manager-pop-2" style="display: none;">
        <div class="pop-content" th:width="auto">
            <div class="top clear" >
                <span class="fs16 colo666 fl ft-bold">富文本内容</span>
                <i class="icon icon-del fr canncle-edit-name" @click="hidePop()"></i>
            </div>
            <div class="middle fs14 col333 pop-info clear">
                <form class="layui-form" action="" lay-filter="ipForm">
                    <div class="input-item">
                        <span>{{generalRich.content==null? '': generalRich.content}}</span>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <!--弹框：引擎数据详情-->
    <div id="generalDataDetail" class="pop add-manager-pop-2" style="display: none;">
        <div class="pop-content1 pop-content">
            <div class="top clear">
                <span class="fs16 colo666 fl ft-bold">GeneralData详情</span>
                <i class="icon icon-del fr canncle-edit-name" @click="hidePop()"></i>
            </div>
            <div class="middle middle1 fs14 col333 pop-info clear">
                <form class="layui-form" action="" lay-filter="ipForm">
                    <div class="input-item">
                        <span >id</span>
                        <input type="text" v-model="generalData.id" readonly="readonly">
                    </div>
                    <div class="input-item">
                        <span >标题</span>
                        <input type="text" v-model="generalData.title" readonly="readonly">
                    </div>
                    <div class="input-item">
                        <span >作者</span>
                        <input type="text" v-model="generalData.author" readonly="readonly">
                    </div>
                    <div class="input-item">
                        <span >封面</span>
                        <input type="text" v-model="generalData.coverUrl" readonly="readonly">
                    </div>
                    <div class="input-item">
                        <span >子标题</span>
                        <input type="text" v-model="generalData.subtitle" readonly="readonly">
                    </div>
                    <div class="input-item">
                        <span >简介</span>
                        <input type="text" v-model="generalData.introduction" readonly="readonly">
                    </div>
                    <div class="input-item">
                        <span >数量</span>
                        <input type="text" v-model="generalData.number" readonly="readonly">
                    </div>
                    <div class="input-item">
                        <span >页面类型 1门户自定义详情 2搜索列表 3外部地址 4搜索详情 5云舟学者详情 6云舟机构详情 7门户视频详情 13云盘视频详情</span>
                        <input type="text" v-model="generalData.pageType" readonly="readonly">
                    </div>
                    <div class="input-item">
                        <span >发布时间 - 字符串</span>
                        <input type="text" v-model="generalData.publishTime" readonly="readonly">
                    </div>
                    <div class="input-item">
                        <span >是否发布 0：否， 1：是</span>
                        <input type="text" v-model="generalData.isPublish" readonly="readonly">
                    </div>
                    <div class="input-item">
                        <span >顺序</span>
                        <input type="text" v-model="generalData.sequence" readonly="readonly">
                    </div>
                    <div class="input-item">
                        <span >分类ID</span>
                        <input type="text" v-model="generalData.typeId" readonly="readonly">
                    </div>
                    <div class="input-item">
                        <span >引擎实例ID</span>
                        <input type="text" v-model="generalData.engineInstanceId" readonly="readonly">
                    </div>
                    <div class="input-item">
                        <span >置顶 0：否，1：是</span>
                        <input type="text" v-model="generalData.isTop" readonly="readonly">
                    </div>
                    <div class="input-item">
                        <span >额外字符</span>
                        <input type="text" v-model="extra" readonly="readonly">
                    </div>
                    <div class="input-item">
                        <span >url</span>
                        <input type="text" v-model="generalData.url" readonly="readonly">
                    </div>
                    <div class="input-item">
                        <span >状态</span>
                        <input type="text" v-model="generalData.status" readonly="readonly">
                    </div>
                    <div class="input-item">
                        <span >下划线分割的父分类id</span>
                        <input type="text" v-model="generalData.typePath" readonly="readonly">
                    </div>
                    <div class="input-item">
                        <span >审核状态。0：未通过、1：已通过、2：待提交审核、3：审核中</span>
                        <input type="text" v-model="generalData.auditStatus" readonly="readonly">
                    </div>
                    <div class="input-item">
                        <span >审核人uid</span>
                        <input type="text" v-model="generalData.auditUid" readonly="readonly">
                    </div>
                    <div class="input-item">
                        <span >创建人uid</span>
                        <input type="text" v-model="generalData.createUid" readonly="readonly">
                    </div>
                    <div class="input-item">
                        <span >url是否是外部地址</span>
                        <input type="text" v-model="generalData.isOuter" readonly="readonly">
                    </div>
                    <div class="input-item">
                        <span >音视频地址</span>
                        <input type="text" v-model="generalData.mediaUrl" readonly="readonly">
                    </div>
                    <div class="input-item">
                        <span >视频封面地址</span>
                        <input type="text" v-model="generalData.videoCoverUrl" readonly="readonly">
                    </div>
                    <div class="input-item">
                        <span >详情访问量基数</span>
                        <input type="text" v-model="generalData.accessBaseNum" readonly="readonly">
                    </div>
                    <div class="input-item">
                        <span >详情页图片字符串</span>
                        <input type="text" v-model="generalData.graphicImgUrls" readonly="readonly">
                    </div>
                    <div class="input-item">
                        <span >发起审核人uid</span>
                        <input type="text" v-model="generalData.startAuditUid" readonly="readonly">
                    </div>
                    <div class="input-item">
                        <span >当前审核人uid，多个审核人逗号隔开</span>
                        <input type="text" v-model="generalData.currentAuditUid" readonly="readonly">
                    </div>
                    <div class="input-item">
                        <span >审核类型 0关闭审核 1不指定流程审核 2指定流程审核</span>
                        <input type="text" v-model="generalData.auditType" readonly="readonly">
                    </div>
                    <div class="input-item">
                        <span >云盘视频objectid</span>
                        <input type="text" v-model="generalData.videoObjectId" readonly="readonly">
                    </div>
                    <div class="input-item">
                        <span >详情页动态字段</span>
                        <input type="text" v-model="generalData.detailExtral" readonly="readonly">
                    </div>
                    <div class="input-item">
                        <span >是否敏感词。0：否， 1：是</span>
                        <input type="text" v-model="generalData.isSensitiveWords" readonly="readonly">
                    </div>
                    <div class="input-item">
                        <span >pid</span>
                        <input type="text" v-model="generalData.pid" readonly="readonly">
                    </div>
                    <div class="input-item">
                        <span >是由哪个数据推送过来的</span>
                        <input type="text" v-model="generalData.originDataId" readonly="readonly">
                    </div>
                    <div class="input-item">
                        <span >pc端，ip内，北航定制</span>
                        <input type="text" v-model="generalData.urlInner" readonly="readonly">
                    </div>
                    <div class="input-item">
                        <span >移动端，ip外，北航定制</span>
                        <input type="text" v-model="generalData.appUrl" readonly="readonly">
                    </div>
                    <div class="input-item">
                        <span >移动端，ip内，北航定制</span>
                        <input type="text" v-model="generalData.appUrlInner" readonly="readonly">
                    </div>
                    <div class="input-item">
                        <span >更新时间</span>
                        <input type="text" v-model="generalData.updateTime" readonly="readonly">
                    </div>
                    <div class="input-item">
                        <span >元数据ID</span>
                        <input type="text" v-model="generalData.resId" readonly="readonly">
                    </div>
                    <div class="input-item">
                        <span >标题转拼音的首个拼音</span>
                        <input type="text" v-model="generalData.fletter" readonly="readonly">
                    </div>
                </form>
            </div>
        </div>
    </div>

</div>

<script th:src="@{/assets/lib/jquery.min.js?v=0312}"></script>
<script th:src="@{/assets/lib/jquery-migrate.min.js?v=0312}"></script>
<script th:src="@{/assets/lib/mCustomScrollBar/jquery.mousewheel.min.js?v=0312}"></script>
<script th:src="@{/assets/lib/mCustomScrollBar/jquery.mCustomScrollbar.min.js?v=0312}"></script>
<script th:src="@{/assets/lib/select2/select2.min.js?v=0312}"></script>
<script th:src="@{/assets/js/common.js?v=0312}"></script>
<script th:src="@{/assets/js/dropzone.min.js}"></script>
<script th:src="@{/assets/js/component.fileupload.js}"></script>

<link rel="stylesheet" th:href="@{/assets/lib/layui/css/layui.css?v=020911}" media="all">
<script th:src="@{/assets/lib/layui/layui.js?v=020911}" charset="utf-8"></script>
<script th:src="@{/assets/lib/vue.js?v=0312}"></script>

<script th:inline="javascript">
    var ctx = [[${#servletContext.contextPath}]];
    var whiteListVue;
    function searchEnter(e, type) {
        var $this = whiteListVue;
        var et = window.event || e;
        if (et.keyCode == 13) {
            $this.pageNum = 1;
            $this.search();
        } else if (et.keyCode == 8) {
            if (type == 1 && $this.isEmpty($this.websiteName)) {
                $this.pageNum = 1;
                $this.search();
            }
        }
    }
    $(function () {
        whiteListVue = new Vue({
            el: "#general-data-list",
            data: {
                generalDataList: [],
                generalRich: {},
                generalData:{},
                extra:{},
                domain: [[${domain}]],
                realname: '',
                ip: '',
                startIp: '',
                endIp: '',
                total: 0,
                pageNum: 1,
                pageSize: 10,
                pager: null,
                flag: true,
                instanceId: '',
                preMaxNum:null,
                nexMinNum:null,

            },
            created: function () {

            },
            mounted: function () {
                var $this = this;
                // $this.initRadio();
                $this.flushTotal();
                $this.loadDatas();
            },
            methods: {
                flushTotal: function(){
                    var $this = this;
                    var params = {
                        'instanceId': $this.instanceId,
                    };
                    $.ajax({
                        type: "post",
                        data: JSON.stringify(params),
                        dataType: "json",
                        contentType: "application/json;charset=utf-8",
                        url:   '/engine2/engine2data/general-data-count',
                        success: function (json) {
                            if (json.code === 1) {
                                $this.total = json.data;
                                $this.initPager();
                            } else {
                                console.log(json.message)
                            }
                        },
                    });
                },
                search: function (){
                    var $this = this
                    $this.flag = true;
                    $this.preMaxNum = null;
                    $this.nextMinNum = null;
                    $this.flushTotal();
                    $this.loadDatas();
                },
                inputChangeUid: function () {
                    this.websiteUid = this.websiteUid.replace(/[^\d]/g, '');
                },
                inputChangeWfwfid: function () {
                    this.wfwfid = this.wfwfid.replace(/[^\d]/g, '');
                },
                initRadio: function () {
                    layui.use(['form'], function () {
                        var form = layui.form;
                        // radio切换
                        form.on('radio(radioIP)', function (data) {
                        });
                    });
                },
                loadDatas: function () {
                    var $this = this;
                    var params = {
                        'pageNum': $this.pageNum,
                        'pageSize': $this.pageSize,
                        'flag': $this.flag,
                        'preMaxNum':$this.preMaxNum,
                        'nextMinNum': $this.nextMinNum,
                        'instanceId': $this.instanceId,
                    };
                    $.ajax({
                        type: "post",
                        data: JSON.stringify(params),
                        dataType: "json",
                        contentType: "application/json;charset=utf-8",
                        url:   '/engine2/engine2data/general-data',
                        success: function (json) {
                            if (json.code === 1) {
                                $this.generalDataList = json.data.list;
                                var length = $this.generalDataList.length
                                if(length>0){
                                    $this.nextMinNum = $this.generalDataList[0].id
                                    $this.preMaxNum = $this.generalDataList[length-1].id
                                }
                            } else {
                                console.log(json.message)
                            }
                        },

                    });

                },
                initPager: function () {
                    var $this = this;
                    $("#laypage").hide();
                    if ($this.total>0) {
                        layui.use(['laypage'], function () {
                            var laypage = layui.laypage;
                            laypage.render({
                                elem: 'laypage',
                                curr: $this.pageNum,
                                limit: $this.pageSize,
                                count: $this.total,
                                layout: ['prev', 'next', 'limit'],
                                jump: function (obj, first) {
                                    if (obj.curr > $this.pageNum) {
                                        $this.flag = true;
                                    } else if (obj.curr < $this.pageNum) {
                                        $this.flag = false;
                                    }
                                    $this.pageNum = obj.curr;
                                    $this.pageSize = obj.limit;
                                    if (!first) {
                                        $this.loadDatas();
                                    }

                                }
                            });
                        });
                        $("#laypage").show();
                    }
                },
                showPop: function (div) {
                    $('.pop').hide();
                    $(div).show();
                },
                hidePop: function () {
                    $('.pop').hide();
                },
                copy: function (obj) {
                    return JSON.parse(JSON.stringify(obj));
                },
                userInfo: function (uid) {
                    var $this = this;
                    var personUrl = '/outer/user-info-outer?t=' + uid;
                    $.getJSON(personUrl, function (data) {
                        if (1 == data.code) {
                            if (!isEmpty(data.data.realname)) {
                                $this.realname = data.data.realname;
                            }
                        }
                    });
                },
                editWebsiteDiv: function (generalData) {
                    var $this = this;
                    var id = generalData.id
                    var params = {
                        'id': id,
                    };
                    $.ajax({
                        type: "post",
                        data: JSON.stringify(params),
                        dataType: "json",
                        contentType: "application/json;charset=utf-8",
                            url:   '/engine2/engine2data/general-rich',
                        success: function (json) {
                            if (json.code === 1) {
                                if(json.data != null){
                                    $this.generalRich = json.data;
                                }
                            } else {
                                console.log(json.message)
                            }
                        },

                    });
                    this.showPop('#addManagerPop');
                },
                generalDataDetail: function (generalData) {
                    var $this = this;
                    $this.generalData = generalData;
                    $this.extra = JSON.stringify(generalData.extra)
                    this.showPop('#generalDataDetail');
                },
                showMsg: function (msg) {
                    layui.use('layer', function () {
                        var layer = layui.layer;
                        layer.msg(msg);
                    });
                },
                isEmpty: function (str) {
                    return typeof(str) == 'undefined' || str == null || "" === $.trim(str);
                },
                // true:数值型的，false：非数值型
                checkNumber: function (theObj) {
                    var reg = /^[0-9]+.?[0-9]$/;
                    var reg1 = /^[0-9]$/;
                    if (reg.test(theObj)) {
                        return true;
                    }
                    if (reg1.test(theObj)) {
                        return true;
                    }
                    return false;
                }

            }
        });

    });

</script>
</body>

</html>