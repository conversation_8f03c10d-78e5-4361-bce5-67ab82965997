<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org/">

<head>
    <meta charset="UTF-8">
    <title>克隆中网站列表</title>
    <link rel="stylesheet" th:href="@{/assets/icomoon/style.css?v=0722}">
    <link rel="stylesheet" th:href="@{/assets/lib/select2/select2.min.css?v=0722}">
    <!--滚动条美化-->
    <link rel="stylesheet" th:href="@{/assets/lib/mCustomScrollBar/jquery.mCustomScrollbar.css?v=0722}">
    <link rel="stylesheet" th:href="@{/assets/css/common.css?v=0722}">
    <link rel="stylesheet" th:href="@{/assets/css/website/advanced.css?v=0724}">
    <style>
        [v-cloak] {
            display: none;
        }
    </style>
</head>

<body>
<div id="website-cloning-list-vue" v-cloak>
    <div class="white-list">
        <div class="page-content">
            <div class="temp-list manager-list">
                <div class="clear">
                    <button class="btn-add" @click="loadDatas()">搜索</button>
                </div>
                <table class="ui-table" border="1">
                    <tr>
                        <th style="width: 10%;">序号</th>
                        <th style="width: 25%;">网站ID</th>
                        <th style="width: 10%;">单位FID</th>
                        <th style="width: 25%;">网站名称</th>
<!--                        <th style="width: 20%;">操作</th>-->
                    </tr>

                    <tr v-for="(item,index) in websiteGrayList" style="height: 60px">
                        <td>{{index+1}}</td>
                        <td>{{item.id}}</td>
                        <td>{{item.wfwfid}}</td>
                        <td>{{item.name}}</td>
<!--                        <td>-->
<!--                            <button class="btn-add" @click="deleteWebsiteGray(item.id)">删除</button>-->
<!--                        </td>-->
                    </tr>
                </table>
            </div>

            <!-- 分页 -->
            <div id="laypage" class="laypage-style"></div>
        </div>
    </div>
</div>

<script th:src="@{/assets/lib/jquery.min.js?v=0312}"></script>
<script th:src="@{/assets/lib/jquery-migrate.min.js?v=0312}"></script>
<script th:src="@{/assets/lib/mCustomScrollBar/jquery.mousewheel.min.js?v=0312}"></script>
<script th:src="@{/assets/lib/mCustomScrollBar/jquery.mCustomScrollbar.min.js?v=0312}"></script>
<script th:src="@{/assets/lib/select2/select2.min.js?v=0312}"></script>
<script th:src="@{/assets/js/common.js?v=0312}"></script>
<script th:src="@{/assets/js/dropzone.min.js}"></script>
<script th:src="@{/assets/js/component.fileupload.js}"></script>

<link rel="stylesheet" th:href="@{/assets/lib/layui/css/layui.css?v=020911}" media="all">
<script th:src="@{/assets/lib/layui/layui.js?v=020911}" charset="utf-8"></script>
<script th:src="@{/assets/lib/vue.js?v=0312}"></script>

<script th:inline="javascript">
    var ctx = [[${#servletContext.contextPath}]];
    var websiteGrayListVue;
    function searchEnter(e, type) {
        var $this = websiteGrayListVue;
        var et = window.event || e;
        if (et.keyCode == 13) {
            $this.pageNum = 1;
            $this.loadDatas();
        } else if (et.keyCode == 8) {
            if (type == 1 && $this.isEmpty($this.wfwfid)) {
                $this.pageNum = 1;
                $this.loadDatas()
            } else if (type == 2 && $this.isEmpty($this.websiteId)) {
                $this.pageNum = 1;
                $this.loadDatas()
            }
        }
    }
    $(function () {
        websiteGrayListVue = new Vue({
            el: "#website-cloning-list-vue",
            data: {
                websiteGray:{},
                websiteGrayList: [],
                websiteId: '',
                total: 0,
                pageNum: 1,
                pageSize: 10,
                wfwfid:'',
                name:'',
                add: true,
                edit: false,
            },
            created: function () {

            },
            mounted: function () {
                var $this = this;
                $this.loadDatas();
            },
            methods: {
                loadDatas: function () {
                    var $this = this;
                    var params = {
                        pageNum: $this.pageNum,
                        pageSize: $this.pageSize,
                        wfwfid: $this.wfwfid,
                        websiteId: $this.websiteId,
                    };
                    $.post(ctx + '/sadmin/website-clone-list', params, function (data) {
                        if (data.code == 1) {
                            $this.websiteGrayList = data.data.list;
                            $this.total = data.data.total;
                            $this.initPager();
                        }
                    });
                },
                initPager: function () {
                    var $this = this;
                    layui.use(['laypage'], function () {
                        var laypage = layui.laypage;
                        laypage.render({
                            elem: 'laypage',
                            curr: $this.pageNum,
                            limit: $this.pageSize,
                            count: $this.total,
                            layout: ['count', 'prev', 'page', 'next', 'limit', 'refresh', 'skip'],
                            jump: function (obj, first) {
                                $this.pageNum = obj.curr;
                                $this.pageSize = obj.limit;
                                if (!first) {
                                    $this.loadDatas();
                                }

                            }
                        });
                    });
                },
                showMsg: function (msg) {
                    layui.use('layer', function () {
                        var layer = layui.layer;
                        layer.msg(msg);
                    });
                },
                showPop: function (div) {
                    $('.pop').hide();
                    $(div).show();
                },
                hidePop: function () {
                    $('.pop').hide();
                },
                copy: function (obj) {
                    return JSON.parse(JSON.stringify(obj));
                },
                isEmpty: function (str) {
                    return typeof(str) == 'undefined' || str == null || "" === $.trim(str);
                },
                toAdd: function () {
                    var $this = this;
                    var item = {
                        websiteGray:{
                            websiteId:''
                        }
                    }
                    $this.websiteGray = $this.copy(item);
                    this.showPop('#addManagerPop');
                    $this.edit = false;
                    $this.add = true;
                },
                toUpdate: function (item) {
                    var $this = this;
                    $this.websiteGray = $this.copy(item);
                    this.showPop('#addManagerPop');
                    $this.add = false;
                    $this.edit = true;
                },
            }
        });
    });

</script>
</body>

</html>