<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org/">

<head>
    <meta charset="UTF-8">
    <title>组件列表</title>
    <link rel="stylesheet" th:href="@{/assets/icomoon/style.css?v=0722}">
    <link rel="stylesheet" th:href="@{/assets/lib/select2/select2.min.css?v=0722}">
    <!--滚动条美化-->
    <link rel="stylesheet" th:href="@{/assets/lib/mCustomScrollBar/jquery.mCustomScrollbar.css?v=0722}">
    <link rel="stylesheet" th:href="@{/assets/css/common.css?v=0722}">
    <link rel="stylesheet" th:href="@{/assets/css/website/advanced.css?v=0724}">
    <style>
        [v-cloak] {
            display: none;
        }
    </style>
</head>

<body>
<div id="module-content-list-vue" v-cloak>
    <div class="white-list">
        <div class="page-content">
            <div class="temp-list manager-list">
                <div class="clear">
                    <div class="layui-form">
                        <div class="layui-inline">
                            <label class="layui-form-label">类型：</label>
                            <div class="layui-input-block">
                                <input class="layui-input" id="moduleType" />
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">标题：</label>
                            <div class="layui-input-block">
                                <input placeholder="请输入标题" class="layui-input" v-model="title" autocomplete="off"/>
                            </div>
                        </div>
                        <div class="layui-inline">
                            <button class="btn-add" @click="loadDatas()">搜索</button>
                        </div>
                    </div>
                </div>
                <table class="ui-table" border="1" style="text-align: center;">
                    <tr>
                        <th style="width: 10%;">ID</th>
                        <th style="width: 20%;">标题</th>
                        <th style="width: 20%;">封面</th>
                        <th style="width: 10%;">类型</th>
                        <th style="width: 12%;">编号（从大到小排序）</th>
                        <th style="width: 8%;">create_uid</th>
                        <th style="width: 10%;">修改时间</th>
                        <th style="width: 10%;">操作</th>
                    </tr>

                    <tr v-for="(resource,index) in moduleList">
                        <td>{{resource.id}}</td>
                        <td>{{resource.title}}</td>
                        <td>
                            <img v-if="resource.cover != null && resource.cover != ''" width='200px' height='100px' :src="resource.cover" />
                        </td>
                        <td>{{resource.typeName}}</td>
                        <td>{{resource.seq}}</td>
                        <td>{{resource.createUid}}</td>
                        <td>{{resource.updateTime}}</td>
                        <td>
                            <button class="btn-add" @click="showResource(resource)">详情</button>
                            <button class="btn-add" @click="editResource(resource)">编辑</button>
                            <button class="btn-add" @click="deleteResource(resource.id)">删除</button>
                        </td>
                    </tr>
                </table>
            </div>

            <!-- 分页 -->
            <div id="laypage" class="laypage-style"></div>
        </div>
    </div>

    <!--弹框：添加管理员-->
    <div id="addManagerPop" class="pop add-manager-pop-2" style="display: none;">
        <div class="pop-content">
            <div class="top clear">
                <span class="fs16 colo666 fl ft-bold" id="popTitle">编辑</span>
                <i class="icon icon-del fr canncle-edit-name" @click="hidePop()"></i>
            </div>
            <div class="middle fs14 col333 pop-info clear">
                <form class="layui-form" action="" lay-filter="ipForm">
                    <div class="layui-form-item">
                        <label class="layui-form-label">标题：</label>
                        <div class="layui-input-block">
                            <input type="text" :readonly="!isShow" autocomplete="off" class="layui-input" v-model="resource.title">
                        </div>
                    </div>
                    <div class="layui-form-item" v-show="isShow">
                        <label class="layui-form-label">类型：</label>
                        <div class="layui-input-block">
                            <input readonly class="layui-input" id="moduleTypeEdit" />
                        </div>
                    </div>
                    <div class="layui-form-item" v-show="isShow">
                        <label class="layui-form-label">封面：</label>
                        <div class="layui-input-block">
                            <img width='200px' height='100px' :src="resource.cover" />
                            <button type="button" class="btn-add" id="uploadImg">上传封面</button>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">编号：</label>
                        <div class="layui-input-block">
                            <input type="number" autocomplete="off" class="layui-input" v-model="resource.seq">
                        </div>
                    </div>
                    <div class="layui-form-item" v-show="!isShow">
                        <label class="layui-form-label">content：</label>
                        <div class="layui-input-block">
                            <textarea cols="40" rows="8" readonly="readonly" style="border: 1px solid;">{{resource.content}}</textarea>
                        </div>
                    </div>
                    <div class="layui-form-item" v-show="!isShow">
                        <label class="layui-form-label">appContent：</label>
                        <div class="layui-input-block">
                            <textarea cols="40" rows="8" readonly="readonly" style="border: 1px solid;">{{resource.appContent}}</textarea>
                        </div>
                    </div>
                </form>
            </div>
            <div class="bottom-btns">
                <input type="button" value="取消" class="btn btn-fff-auto canncle-edit-name" @click="hidePop()">
                <input type="button" value="确定" class="btn btn-blue-auto" @click="saveDomainBlank()">
            </div>
        </div>
    </div>
</div>

<script th:src="@{/assets/lib/jquery.min.js?v=0312}"></script>
<script th:src="@{/assets/lib/jquery-migrate.min.js?v=0312}"></script>
<script th:src="@{/assets/lib/mCustomScrollBar/jquery.mousewheel.min.js?v=0312}"></script>
<script th:src="@{/assets/lib/mCustomScrollBar/jquery.mCustomScrollbar.min.js?v=0312}"></script>
<script th:src="@{/assets/lib/select2/select2.min.js?v=0312}"></script>
<script th:src="@{/assets/js/common.js?v=0312}"></script>
<script th:src="@{/assets/js/dropzone.min.js}"></script>
<script th:src="@{/assets/js/component.fileupload.js}"></script>

<link rel="stylesheet" th:href="@{/assets/lib/layui/css/layui.css?v=020911}" media="all">
<script th:src="@{/assets/lib/layui/layui.js?v=020911}" charset="utf-8"></script>
<link rel="stylesheet" th:href="@{/assets/lib/layui/module/cascader/cascader.css}">
<script th:src="@{/assets/lib/layui/module/cascader/cascader.js}" charset="utf-8"></script>
<script th:src="@{/assets/lib/vue.js?v=0312}"></script>

<script th:inline="javascript">
    var ctx = [[${#servletContext.contextPath}]];
    var whiteListVue;

    $(function () {
        whiteListVue = new Vue({
            el: "#module-content-list-vue",
            data: {
                isShow: true,
                moduleList: [],
                moduleTypeTree: [],
                firstModuleTypeId: -1,
                resource: {},
                total: 0,
                pageNum: 1,
                pageSize: 10,
                title:'',
                layCascader: null,
                layCascaderEdit: null
            },
            created: function () {

            },
            mounted: function () {
                var $this = this;

                layui.use(['form', 'upload', 'layer'], function () {
                    var form = layui.form;
                    var upload = layui.upload;
                    var uploadInst = upload.render({
                        elem: '#uploadImg' //绑定元素
                        , url: ctx + '/sadmin/upload-cover' //上传接口
                        , done: function (res) {
                            if(res.code === 1){
                                $this.resource.cover = res.data;
                            } else{
                                layer.msg("上传失败", {icon: 7})
                            }
                        }
                        , error: function () {
                            layer.msg("上传失败", {icon: 7})
                        }
                    });

                    $this.loadModuleType();
                    $this.loadDatas();
                });
            },
            methods: {
                loadDatas: function () {
                    var $this = this;
                    var params = {
                        pageNum: $this.pageNum,
                        pageSize: $this.pageSize,
                        typeId: $this.layCascader ? $this.layCascader.getCheckedValues() : null,
                        title: $this.title
                    };
                    $.get(ctx + '/sadmin/module/list', params, function (data) {
                        if (data.code === 1) {
                            $this.moduleList = data.data.list;
                            $this.total = data.data.total;
                            $this.moduleList.forEach(r => {
                                if (r.type === 1) {
                                    if (r.content) {
                                        var img = JSON.parse(r.content);
                                        r.content = "<img width='200px' height='100px' id='" + r.id + "'  src='" + img.url + "' />";
                                    }
                                }
                            });
                            $this.initPager();
                        }
                    });
                },
                loadModuleType: function (){
                    var $this = this;
                    $.get(ctx + '/sadmin/module-type/tree', {}, function (data) {
                        if (data.code === 1) {
                            $this.moduleTypeTree = data.data;
                            layui.use('layCascader', function () {
                                var layCascader = layui.layCascader;
                                $this.layCascader = layCascader({
                                    elem: '#moduleType',
                                    options: data.data,
                                    // showAllLevels: false,
                                    placeholder: '请选择类型',
                                    filterable: true,
                                    clearable: true,
                                    props: {
                                        expandTrigger: 'hover'
                                    }
                                });

                                var layCascaderEdit = layui.layCascader;
                                $this.layCascaderEdit = layCascaderEdit({
                                    elem: '#moduleTypeEdit',
                                    options: $this.moduleTypeTree,
                                    // showAllLevels: false,
                                    placeholder: '请选择类型',
                                    filterable: true,
                                    clearable: true,
                                    props: {
                                        expandTrigger: 'hover'
                                    },
                                });
                            });
                        }
                    });
                },
                initPager: function () {
                    var $this = this;
                    layui.use(['laypage'], function () {
                        var laypage = layui.laypage;
                        laypage.render({
                            elem: 'laypage',
                            curr: $this.pageNum,
                            limit: $this.pageSize,
                            count: $this.total,
                            layout: ['count', 'prev', 'page', 'next', 'limit', 'refresh', 'skip'],
                            jump: function (obj, first) {
                                $this.pageNum = obj.curr;
                                $this.pageSize = obj.limit;
                                if (!first) {
                                    $this.loadDatas();
                                }
                            }
                        });
                    });
                },
                showPop: function (div) {
                    $('.pop').hide();
                    $(div).show();
                },
                hidePop: function () {
                    $('.pop').hide();
                },
                editResource: function (resource) {
                    var $this = this;
                    layui.use(['form','layCascader'], function () {
                        var form = layui.form;
                        $this.resource = $this.copy(resource);
                        $this.layCascaderEdit.setValue(resource.typeId);
                        $this.isShow = true;
                        $this.showPop('#addManagerPop');
                        $('#popTitle').html('编辑')
                    });
                },
                showResource: function (resource) {
                    var $this = this;
                    layui.use(['form','layCascader'], function () {
                        var form = layui.form;
                        $this.isShow = false;
                        $this.resource = $this.copy(resource);
                        $this.layCascaderEdit.setValue(resource.typeId);
                        $this.showPop('#addManagerPop');
                        $('#popTitle').html('查看')
                    });
                },
                deleteResource: function (id) {
                    $this = this;
                    layui.use('layer', function () {
                        layer.confirm('您确定要删除吗？', {
                            btn: ['确定', '取消'] //按钮
                        }, function () {
                            $.ajax({
                                url: getContextPath() + '/sadmin/module/delete',
                                type: 'get',
                                data: {
                                    id: id
                                },
                                success: function (data) {
                                    if (data.code === 1) {
                                        $this.showMsg("删除成功");
                                        $this.loadDatas();
                                    } else {
                                        if (data.message.indexOf('50081') > -1){
                                            $this.showMsg('组件存在默认组件类型依赖关系，先修改组件类型的默认组件ID后再删除')
                                        }else{
                                            $this.showMsg(data.message);
                                        }
                                    }
                                }
                            });
                        });
                    });
                },
                saveDomainBlank: function () {
                    var $this = this;
                    if ($this.isEmpty($this.resource.title)) {
                        $this.showMsg("请输入标题");
                        return;
                    }
                    $.ajax({
                        url: getContextPath() + '/sadmin/module/update',
                        type: 'post',
                        data: {
                            id: $this.resource.id,
                            title: $this.resource.title,
                            typeId: $this.layCascaderEdit.getCheckedValues(),
                            cover: $this.resource.cover,
                            seq: $this.resource.seq,
                        },
                        success: function (data) {
                            if (data.code == 1) {
                                $this.loadDatas();
                                $this.hidePop();
                                $this.showMsg("操作成功");
                            } else {
                                $this.showMsg(data.message);
                            }
                        }
                    });
                },
                copy: function (obj) {
                    return JSON.parse(JSON.stringify(obj));
                },
                showMsg: function (msg) {
                    layui.use('layer', function () {
                        var layer = layui.layer;
                        layer.msg(msg);
                    });
                },
                isEmpty: function (str) {
                    return typeof (str) == 'undefined' || str == null || "" === $.trim(str);
                },
                // true:数值型的，false：非数值型
                checkNumber: function (theObj) {
                    var reg = /^[0-9]+.?[0-9]$/;
                    var reg1 = /^[0-9]$/;
                    if (reg.test(theObj)) {
                        return true;
                    }
                    if (reg1.test(theObj)) {
                        return true;
                    }
                    return false;
                }
            }
        });
    });

</script>
</body>

</html>