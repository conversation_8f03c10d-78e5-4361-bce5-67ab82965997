<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <title>微服务智慧门户</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" th:href="@{/assets/icomoon/style.css}">
    <link rel="stylesheet" th:href="@{/assets/lib/layui/css/layui.css?v=020911}">
    <link rel="stylesheet" th:href="@{/assets/css/common.css}">
    <!-- 自定义图标 -->
    <link rel="stylesheet" th:href="@{/assets_dy/icomoon/style.css}" media="all">
</head>

<body>
    <div class="my-article visits-pv">
        <div class="echart-top">
            <div class="title fl">分组管理员信息统计</div>
            <div class="fr">
                <div class="layui-form">
                    <div class="layui-form-item">
                        <div class="layui-inline">
                            <div class="layui-input-inline calendar-input">
                                <i class="icon-ct-calendar"></i>
                                <input type="text" class="layui-input visit-date" placeholder="请选择起始时间">
                            </div>
                            <button type="button" class="custom-link"><i class="icon-ct-export"></i>导出</button>
                        </div>
                    </div>
                </div>
            </div>
            <ul class="fr filter-times">
                <li class="active">近7日</li>
                <li>近30日</li>
            </ul>
        </div>
        <table id="myTable" lay-filter="myTable"></table>
        <div class="bottom-div">
            <div id="laypage"></div>
        </div>
    </div>
    <script type="text/html" id="operateButton">
        <div class="font0">
            <a class="custom-btn btn-look" lay-event="look">查看</a>
            <a class="custom-btn btn-edit" lay-event="edit">编辑</a>
            <a class="custom-btn btn-del" lay-event="del">删除</a>
        </div>
    </script>
    <!-- 自定义样式 -->
    <link rel="stylesheet" th:href="@{/assets_dy/css/new-style.css}">
    <script th:src="@{/assets/lib/jquery.min.js}"></script>
    <script th:src="@{/assets/lib/layui/layui.js?v=020911}"></script>
    <script th:src="@{/assets/lib/echarts4/echarts.min.js}"></script>
    <script>
        $(function () {
            changestatisticsPeriod()
            calendarInit()
            layui.use(['laydate', 'form'], function () {
                var form = layui.form,
                    laydate = layui.laydate;
                //日期时间选择器
                laydate.render({
                    elem: '#testDate',
                    type: 'datetime',
                    format: 'yyyy-MM-dd HH:mm'
                });
            });
            layui.use(['table', 'laypage'], function () {
                var table = layui.table,
                    laypage = layui.laypage;
                laypage.render({
                    elem: 'laypage',
                    count: 100,
                    layout: ['prev', 'page', 'next'],
                    prev: '<i class="layui-icon layui-icon-left"></i>',
                    next: '<i class="layui-icon layui-icon-right"></i>',
                });
                table.render({
                    elem: '#myTable',
                    cellMinWidth: 100,
                    height: 457,
                    data: [{
                        group: '教师发展中心',
                        tNumber: 46,
                    }, {
                        group: '教务处',
                        tNumber: 60,
                    }, {
                        group: '课程中心',
                        tNumber: 50,
                    }],
                    cols: [
                        [
                            {
                                field: 'group',
                                title: '所在组别'
                            },
                            {
                                field: 'tNumber',
                                title: '文章数量'
                            },
                        ]
                    ]
                })
            })
        })

        // 日历的初始化
        function calendarInit() {
            layui.use('laydate', function () {
                var laydate = layui.laydate;
                lay('.visit-date').each(function () {
                    laydate.render({
                        elem: this,
                        type: 'datetime',
                        range: true,
                        min: '2020-1-1',
                        max: '2020-12-31',
                        theme: '#3D82F2',
                        trigger: 'click'
                    });
                });
            })
        }
        // 图表数据年月日的切换
        function changestatisticsPeriod() {
            $(".filter-times>li").on("click", function () {
                $(this).addClass("active").siblings().removeClass("active");
            })
        }
    </script>
</body>

</html>