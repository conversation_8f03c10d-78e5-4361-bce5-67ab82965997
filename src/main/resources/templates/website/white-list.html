<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org/">

<head>
    <meta charset="UTF-8">
    <title>高级选项</title>
    <link rel="stylesheet" th:href="@{/assets/icomoon/style.css?v=0722}">
    <link rel="stylesheet" th:href="@{/assets/lib/select2/select2.min.css?v=0722}">
    <!--滚动条美化-->
    <link rel="stylesheet" th:href="@{/assets/lib/mCustomScrollBar/jquery.mCustomScrollbar.css?v=0722}">
    <link rel="stylesheet" th:href="@{/assets/css/common.css?v=0722}">
    <link rel="stylesheet" th:href="@{/assets/css/website/advanced.css?v=20210621}">
    <style>
        [v-cloak] {
            display: none;
        }
    </style>
</head>

<body>
<div id="white-list-vue" v-cloak>
    <div class="white-list">
        <div class="page-content">
            <div class="temp-list manager-list">
                <form class="layui-form forbid-tips col333" action="" onsubmit="return false;">
                    <div class="layui-form-item fl">
                        <label class="layui-form-label">是否用于限制网站IP访问：</label>
                        <div class="layui-input-inline">
                            <input type="radio" name="check" value="1" title="是" lay-filter="radioBool" v-model="check">
                            <div class="tip-div">
                                <img class="btn-tip" th:src="@{/assets_dy/images/icon-info.png}" alt="">
                                <p class="tip-txt">限制后，仅白名单内IP可访问网站</p>
                            </div>
                            <input type="radio" name="check" value="0" title="否" lay-filter="radioBool" v-model="check">
                        </div>
                        <div class="layui-input-inline tip-input" v-show="check == 0">
                            <input type="text" v-model="tips" placeholder="请输入白名单外IP访问门户时，提示弹窗内显示的文案信息">
                        </div>
                    </div>
                    <button class="btn-add fr" @click="saveCheckAndTips">保存</button>
                    <div class="clear"></div>
                </form>
                <div class="clear">
                    <button class="btn-add" @click="addIp()">添加</button>
                </div>
                <table class="ui-table" border="1">
                    <tr>
                        <th style="width: 10%;">序号</th>
                        <th style="width: 80%;">IP段</th>
                        <th style="width: 10%;">操作</th>
                    </tr>

                    <tr v-for="(ip,index) in datas">
                        <td>{{index+1}}</td>
                        <td>{{ip}}</td>
                        <td>
                            <button class="btn-remove" @click="removeWhiteIp(ip)">移出</button>
                        </td>
                    </tr>
                </table>
            </div>

            <!-- 分页 -->
            <div id="laypage" class="laypage-style"></div>
        </div>
    </div>

    <!--弹框：添加管理员-->
    <div id="addManagerPop" class="pop add-manager-pop-2" style="display: none;">
        <div class="pop-content">
            <div class="top clear">
                <span class="fs16 colo666 fl ft-bold">新增</span>
                <i class="icon icon-del fr canncle-edit-name" @click="hidePop()"></i>
            </div>
            <div class="middle fs14 col333 pop-info clear">
                <form class="layui-form" action="" lay-filter="ipForm">
                    <div class="layui-form-item">
                        <label class="layui-form-label w-200">输入白名单IP地址：</label>
                        <div class="layui-input-block">
                            <input type="radio" name="ipType" value="IP" title="IP" lay-filter="radioIP" checked="">
                            <input type="radio" name="ipType" value="IP段" title="IP段" lay-filter="radioIP">
                        </div>
                    </div>
                    <div class="input-item model-1" style="display: block;">
                        <input type="text" v-model="ip" placeholder="请输入IP地址">
                    </div>
                    <div class="input-item model-2">
                        <input type="text" placeholder="请输入起始IP地址" v-model="startIp">&nbsp;&nbsp;&nbsp;&macr;&nbsp;&nbsp;
                        <input type="text" placeholder="请输入结束IP地址" v-model="endIp">
                    </div>
                </form>
            </div>
            <div class="bottom-btns">
                <input type="button" value="取消" class="btn btn-fff-auto canncle-edit-name" @click="hidePop()">
                <input type="button" value="确定" class="btn btn-blue-auto" @click="addWhiteIp()">
            </div>
        </div>
    </div>
</div>

    <script th:src="@{/assets/lib/jquery.min.js?v=0312}"></script>
    <script th:src="@{/assets/lib/jquery-migrate.min.js?v=0312}"></script>
    <script th:src="@{/assets/lib/mCustomScrollBar/jquery.mousewheel.min.js?v=0312}"></script>
    <script th:src="@{/assets/lib/mCustomScrollBar/jquery.mCustomScrollbar.min.js?v=0312}"></script>
    <script th:src="@{/assets/lib/select2/select2.min.js?v=0312}"></script>
    <script th:src="@{/assets/js/common.js?v=0312}"></script>
    <script th:src="@{/assets/js/dropzone.min.js}"></script>
    <script th:src="@{/assets/js/component.fileupload.js}"></script>

    <link rel="stylesheet" th:href="@{/assets/lib/layui/css/layui.css?v=020911}" media="all">
    <script th:src="@{/assets/lib/layui/layui.js?v=020911}" charset="utf-8"></script>
    <script th:src="@{/assets/lib/vue.js?v=0312}"></script>

    <script th:inline="javascript">
        var ctx = [[${#servletContext.contextPath}]];
        var whiteListVue;

            whiteListVue = new Vue({
                el: "#white-list-vue",
                data: {
                    website: [[${website}]],
                    datas: [],
                    ip: '',
                    startIp: '',
                    endIp: '',
                    total: 0,
                    pageNum: 1,
                    pageSize: 10,
                    pager: null,
                    websiteIps: [[${websiteIps}]],
                    check: 0,
                    tips: ''
                },
                created: function () {

                },
                mounted: function () {
                    var $this = this;
                    $this.initCheckAndTips();
                    $this.initRadio();
                    $this.loadDatas();
                },
                methods: {
                    initCheckAndTips: function () {
                        var $this = this;
                        if ($this.websiteIps != null) {
                            $this.check = $this.websiteIps.check == null ? 0 : $this.websiteIps.check;
                            $this.tips = $this.websiteIps.tips;
                        }
                    },
                    initRadio: function () {
                        var $this = this;
                        layui.use(['form'], function () {
                            var form = layui.form;
                            // radio切换
                            form.on('radio(radioIP)', function (data) {
                                if (data.value == 'IP') {
                                    $('.model-2').hide();
                                    $('.model-1').show();
                                } else if (data.value == 'IP段') {
                                    $('.model-1').hide();
                                    $('.model-2').show();
                                }
                            });
                            form.on('radio(radioBool)', function (data) {
                                $this.check = data.value;
                            });
                        });
                    },
                    loadDatas: function () {
                        var $this = this;
                        var params = {
                            pageNum: $this.pageNum,
                            pageSize: $this.pageSize
                        };
                        $.post(ctx + '/advanced/' + $this.website.id + "/white-ip-list-data", params, function (data) {
                            if (data.code == 1) {
                                $this.datas = data.data.ipList;
                                $this.total = data.data.total;
                                $this.initPager();
                            }
                        });
                    },
                    initPager: function () {
                        var $this = this;
                        layui.use(['laypage'], function () {
                            var laypage = layui.laypage;
                            laypage.render({
                                elem: 'laypage',
                                curr: $this.pageNum,
                                limit: $this.pageSize,
                                count: $this.total,
                                layout: ['count', 'prev', 'page', 'next', 'limit', 'refresh', 'skip'],
                                jump: function (obj, first) {
                                    $this.pageNum = obj.curr;
                                    $this.pageSize = obj.limit;
                                    if(!first){
                                        $this.loadDatas();
                                    }

                                }
                            });
                        });
                    },
                    showPop: function (div) {
                        $('.pop').hide();
                        $(div).show();
                    },
                    hidePop: function() {
                        $('.pop').hide();
                    },
                    addIp: function () {
                        this.ip = '';
                        this.startIp = '';
                        this.endIp = '';
                        layui.form.val("ipForm", {
                            "ipType": "IP"
                        })
                        $('.model-2').hide();
                        $('.model-1').show();
                        this.showPop('#addManagerPop');
                    },
                    checkIp: function () {
                        var $this = this;
                        var ipType = $("input[name='ipType']:checked").val();
                        if (ipType == 'IP') {
                            if ($this.isEmpty($this.ip)) {
                                $this.showMsg("请输入ip地址");
                                return false;
                            }
                            if (!$this.isValidIp($this.ip)) {
                                $this.showMsg("ip地址不正确");
                                return false;
                            }
                            if (!this.isValidIpRange($this.ip)) {
                                $this.showMsg("您当前输入ip范围过大，请确认你输入的是否是子网掩码");
                                return false;
                            }
                        } else if (ipType == 'IP段') {
                            if ($this.isEmpty($this.startIp) || $this.isEmpty($this.endIp)) {
                                $this.showMsg("请输入ip段");
                                return false;
                            }
                            if (!$this.isValidIp($this.startIp)) {
                                $this.showMsg("起始ip地址不正确");
                                return false;
                            }
                            if (!$this.isValidIp($this.endIp)) {
                                $this.showMsg("结束ip地址不正确");
                                return false;
                            }
                            //ipv6时，起止应该都是ipv6
                            if (!$this.isIpv6($this.startIp) && !$this.isIpv6($this.endIp)) {
                            } else if($this.isIpv6($this.startIp) && $this.isIpv6($this.endIp)) {
                            }else {
                                $this.showMsg("起始ip地址应该都是IPV6");
                                return false;
                            }

                            if (!$this.isValidIpRange($this.startIp)) {
                                $this.showMsg("起始ip范围过大，请确认你输入的是否是子网掩码");
                                return false;
                            }
                            if (!this.isValidIpRange($this.endIp)) {
                                $this.showMsg("结束ip范围过大，请确认你输入的是否是子网掩码");
                                return false;
                            }
                        }
                        return true;
                    },
                    addWhiteIp: function () {
                        var $this = this;
                        if ($this.checkIp()) {
                            var ipType = $("input[name='ipType']:checked").val();
                            var param = {};
                            if (ipType == 'IP') {
                                param.ip = $this.ip + "-" + $this.ip;
                            } else {
                                param.ip = $this.startIp + "-" + $this.endIp;
                            }

                            param.isAdmin = 0;

                            $.post(ctx + "/advanced/" + $this.website.id + "/white-ip/add", param, function (data) {
                                if (data.code == 1) {
                                    $this.hidePop();
                                    $this.showMsg("添加成功");
                                    $this.loadDatas();
                                } else if(data.code == 0) {
                                    $this.showMsg(data.message);
                                } else {
                                    $this.showMsg('登录信息异常，请刷新网页！');
                                }
                            });
                        }
                    },
                    removeWhiteIp: function (ip) {
                        var $this = this;
                        $.post(ctx + "/advanced/" + $this.website.id + "/white-ip/remove", {ip: ip,isAdmin:0}, function (data) {
                            if (data.code == 1) {
                                $this.showMsg("移出成功");
                                $this.loadDatas();
                            } else if(data.code == 0) {
                                $this.showMsg(data.message);
                            } else {
                                $this.showMsg('登录信息异常，请刷新网页！');
                            }
                        });
                    },
                    saveCheckAndTips: function () {
                        var $this = this;
                        if ($this.check == 1) {
                            $this.tips = '';
                        }
                        $.post(ctx + '/advanced/' + $this.website.id + '/white-ip/check-tips/save', {
                            check: $this.check,
                            tips: $this.tips
                        }, function (data) {
                            if (data.code == 1) {
                                $this.showMsg("保存成功");
                            } else if(data.code == 0) {
                                $this.showMsg(data.message);
                            } else {
                                $this.showMsg('登录信息异常，请刷新网页！');
                            }
                        });
                    },
                    isValidIp: function (ip) {
                        if (this.isIpv6(ip)) {
                            return true;
                        }
                        var reg = /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/;
                        return reg.test(ip);
                    },
                    isValidIpRange: function (ip) {
                        if (this.isIpv6(ip)) {
                            return true;
                        }
                        return parseInt(ip.split(".")[0]) <= 240;
                    },
                    isIpv6: function (ip) {
                        //ipv6简单校验
                        if (ip.indexOf(':') != -1) {
                            return true;
                        }
                        return false;
                    },
                    showMsg: function (msg) {
                        layui.use('layer', function(){
                            var layer = layui.layer;
                            layer.msg(msg);
                        });
                    },
                    isEmpty: function (str) {
                        return typeof(str) == 'undefined' || str == null || "" === $.trim(str);
                    }
                }
            });

    </script>
</body>

</html>