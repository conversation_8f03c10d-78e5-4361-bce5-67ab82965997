<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <title>公共文化开通</title>
    <link rel="stylesheet" th:href="@{/assets/icomoon/style.css}">
    <link rel="stylesheet" th:href="@{/assets/lib/select2/select2.min.css}">
    <!--滚动条美化-->
    <link rel="stylesheet" th:href="@{/assets/lib/mCustomScrollBar/jquery.mCustomScrollbar.css}">
    <link rel="stylesheet" th:href="@{/assets/css/common.css}">
    <link rel="stylesheet" th:href="@{/assets_dy/css/public-culture-open.css}">
    <style>

    </style>
</head>

<body>
<div class="header">
    <div class="page-title">公共文化开通</div>
</div>
<!-- 输入界面 -->
<div class="w-800 t-content">
    <div class="page-content">
        <div class="form-box">
            <div class="list-item">
                <label class="list-label" for="productId">选择产品</label>
                <select class="list-inline-input" name="" id="productId">
                    <option th:value="${item.id}" th:each="item: ${products}" th:text="${item.productName}">文化馆</option>
                </select>
            </div>
            <div class="list-item">
                <label class="list-label">目标单位FID</label>
                <input class="list-inline-input" type="text" placeholder="请输入FID" id="wfwfid" th:value="${wfwfid}" readonly>
            </div>
            <div class="list-item">
                <label class="list-label">复制网站ID</label>
                <input class="list-inline-input" type="text" placeholder="请输入复制的网站ID,分隔" id="websiteIds" >
            </div>
            <div class="list-item">
                <label class="list-label">目标单位管理员UID</label>
                <input class="list-inline-input" type="text" placeholder="请输入UID" id="uid" th:value="${UID}" readonly>
            </div>
            <a href="javascript:;" class="btn-submit fs14">确认并克隆</a>
        </div>
    </div>
</div>
<!-- 结果界面 -->
<div class="w-800 txt-c" style="display: none">
    <div class="page-content">
        <div class="result-box">
            <img class="result-img" th:src="@{/assets_dy/images/img-wait.png}" alt="">
            <p class="result-txt1 fs16">复制开始，预计等待5-10分钟，请勿重复操作</p>
            <p class="result-txt2 fs14">请直接到微服务后台查看复制结果</p>
            <a href="javascript:;" class="btn-back">返回</a>
        </div>
    </div>
</div>
<!-- toast -->
<div class="toast-box fs14">
    <img th:src="@{/assets_dy/images/attention.png}" alt=""><span>请完善信息后提交</span>
</div>
<script th:src="@{/assets/lib/jquery.min.js?v=20220511}"></script>
<script th:src="@{/assets/lib/mCustomScrollBar/jquery.mousewheel.min.js?v=20220511}"></script>
<script th:src="@{/assets/lib/mCustomScrollBar/jquery.mCustomScrollbar.min.js?v=20220511}"></script>
<script th:src="@{/assets/lib/select2/select2.min.js?v=20220511}"></script>
<script th:src="@{/assets/js/common.js?v=20220511}"></script>
<script th:src="@{/assets/js/dropzone.min.js}"></script>
<script th:src="@{/assets/js/component.fileupload.js}"></script>

<link rel="stylesheet" th:href="@{/assets/lib/layui/css/layui.css?v=020911}" media="all">
<script th:src="@{/assets/lib/layui/layui.js?v=020911}" charset="utf-8"></script>
<script type="text/javascript" th:inline="text">
    var ctx = "[[@{/}]]";
    ctx = "/" == ctx ? "" : ctx;
    ctx = ctx.endsWith("/") ? ctx.substr(0, ctx.length - 1) : ctx;
</script>
<script>
    var layer ;
    layui.use(['form', 'layer'], function () {
        layer = layui.layer;
    });

    $("#productId").select2({
        width: '240px',
        minimumResultsForSearch: -1
    });

    var loading = null;

    $('.btn-submit').click(function () {
        var productId = $('#productId').val();
        var wfwfid = $('#wfwfid').val();
        var websiteIds = $('#websiteIds').val();
        if(!websiteIds){
            layer.msg('请输入网站ID');
            return;
        }
        var uid = $('#uid').val();

        if (!productId) {
            $('.toast-box').show();
            setTimeout(function () {
                $('.toast-box').hide();
            }, 3000);
            return;
        }

        loading = layer.load(0, {
            shade: 0.2, // 遮罩
        });

        var data = {productId: productId, wfwfid: wfwfid, uid: uid,websiteIds: websiteIds};
        $.post(ctx + "/website/qyg/wfw/clone", data, function (res) {
            if (res.code == 1) {
                $(".t-content").hide();
                $(".txt-c").show();
            } else {
                layer.msg(res.message);
            }
            layer.close(loading);
        });
    })
</script>
</body>

</html>
