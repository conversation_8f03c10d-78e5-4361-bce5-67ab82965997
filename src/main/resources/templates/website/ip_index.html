<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org/">
<head>
    <meta charset="UTF-8">
    <title>门户网站列表页</title>
    <link rel="stylesheet" type="text/css" th:href="@{/assets/icomoon/style.css?v=20210621}">
    <link rel="stylesheet" type="text/css" th:href="@{/assets/css/common.css?v=20210621}">
    <link rel="stylesheet" type="text/css" th:href="@{/assets/css/website/home.css?v=20210621}">
</head>
<body>
<div class="page-home">
    <div class="banner-box">
        <img th:src="@{/assets/images/website/banner.png}" alt="">
        <div class="top-btns">
            <!--<a href="javascript:;" th:if="${mhparam != null}" th:href="@{/website/previous}" class="btn">智慧门户α版</a>-->
            <a href="#" class="btn recycleBin" onclick="refreshRecycle(true)">回收站</a>
            <a href="//sharewh.chaoxing.com/share/note/40fdf110-4274-49b9-ab34-eaaa58c228c9/note_detail" target="_blank" class="btn">镜像版本</a>
            <a href="https://mooc1.chaoxing.com/zt/208170041.html" target="_blank" class="btn">使用手册</a>
            <a th:if="${isMirror}" th:href="@{/admin/modify}" class="btn" target="_blank" >修改密码</a>
            <a th:if="${isMirror}" href="javascript:;" onclick="loginOut()" class="btn" target="_blank" >退出登录</a>
        </div>
        <div th:if="${isMirror}" class="version-box">
            <span class="cur" id="versionInfo">当前版本：[[${latestVersion.versionId}]]</span>
            <span class="cur" id="newVersionTip" style="display: none">已经是最新版本</span>
            <a href="javascript:;" id="checkVersionBtn" th:onclick="checkUpdate()" class="btn">检查更新</a>
            <a href="javascript:;" id="newVersionDownloadBtn" target="_blank" class="btn" style="display: none">下载最新版</a>
        </div>
    </div>
    <div class="temp-list-box">
        <div class="btns">
            <a href="javascript:;" th:onclick="showAddWebsite()" class="ui-btn-add icon-add-lighter btn-add"></a>
        </div>
        <ul class="list" id="websiteList">
        </ul>
    </div>
</div>
<!--删除弹框-->
<div id="deleteTemp" class="pop delete-temp-pop" style="display: none;">
    <div class="pop-content">
        <div class="top clear">
            <span class="fs16 colo666 fl ft-bold">删除</span>
            <i class="icon icon-del fr canncle-edit-name" onclick="hidePop()"></i>
        </div>
        <div class="middle fs14 col333">
            <img class="icon-delete" th:src="@{/assets/images/website/img-delete.png}" alt="">
            <p class="tip">确认删除？</p>
        </div>
        <div class="bottom-btns">
            <input type="button" value="取消" class="btn btn-fff-auto canncle-edit-name" onclick="hidePop()">
            <input type="button" value="确定" class="btn btn-blue-auto sure-delete">
        </div>
    </div>
</div>
<!--高级：绑定自有域名-->
<div id="bindDnsPop" class="pop bind-dns-pop" style="display: none;">
    <div class="pop-content">
        <div class="top clear">
            <span class="fs16 colo666 fl ft-bold">高级设置</span>
            <i class="icon icon-del fr canncle-edit-name" onclick="hidePop()"></i>
        </div>
        <div class="middle fs14 col333 pop-ipt-list">
            <div class="item">
                <label class="item-label">单位域名：</label>
                <input id="domainExternalInput" type="text" placeholder="" class="ipt-box">
            </div>
            <div class="item">
                <label class="item-label">CNAME解析到：</label>
                <span class="fs14 col999" id="cname">template.[[${host}]]</span>
            </div>
            <div class="item link-list">
                <a href="https://mooc1.chaoxing.com/ztnodedetailcontroller/visitnodedetail?courseId=205439029&knowledgeId=174120888&_from_=&rtag=" target="_blank" class="col-yellow fs14">挂接流程？</a>
                <a id="websiteMirror" href="javascript:;" target="_blank" class="txt-theme fs14">站点镜像</a>
            </div>
        </div>
        <div class="bottom-btns">
            <input type="button" value="取消" class="btn btn-fff-auto canncle-edit-name" onclick="hidePop()">
            <input type="button" value="保存" class="btn btn-blue-auto" id="saveDomainExternalBtn">
        </div>
    </div>
</div>
<!--添加-->
<div id="addTempPop" class="pop add-temp-pop" style="display: none;">
    <div class="pop-content">
        <div class="top clear">
            <span class="fs16 colo666 fl ft-bold">创建网站</span>
            <i class="icon icon-del fr canncle-edit-name" onclick="hidePop()"></i>
        </div>
        <div class="middle fs14 col333 pop-ipt-list">
            <div class="item">
                <label class="item-label">网站名称：</label>
                <input id="websiteName" type="text" placeholder="" class="ipt-box big-ipt">
            </div>
            <div class="item">
                <label class="item-label">默认域名：</label>
                <span class="tip fs14 col999">https://</span>
                <input id="domainName" type="text" class="ipt-box ipt-dns" onkeyup="this.value=this.value.replace(/\s+/g,'')">
                <span class="tip fs14 col999">.[[${host}]]</span>
            </div>
        </div>
        <div class="bottom-btns">
            <input type="button" value="取消" class="btn btn-fff-auto canncle-edit-name" onclick="hidePop()">
            <input type="button" value="保存" th:onclick="saveWebsite()" class="btn btn-blue-auto">
        </div>
    </div>
</div>

<!--回收站-->
<div id="recycleBin" class="pop recycle-temp-pop"  style="display: none">
    <div class="pop-content">
        <div class="top clear">
            <span class="fs16 colo666 fl ft-bold">回收站</span>
            <i class="icon icon-del fr canncle-edit-name" onclick="hideRecyclePop()"></i>
        </div>
        <div class="middle fs14 col333 recycleTable">
            <table>
                <thead>
                <tr>
                    <th>网站名称</th>
                    <th>操作</th>
                </tr>
                </thead>
                <tbody id="recycleWebsites">
                </tbody>
            </table>
        </div>
    </div>
</div>
<!--回收站删除弹框-->
<div id="recycleDeleteTemp" class="pop delete-temp-pop" style="display: none;">
    <div class="pop-content">
        <div class="top clear">
            <span class="fs16 colo666 fl ft-bold">删除</span>
            <i class="icon icon-del fr canncle-edit-name" onclick="hidePop('#recycleDeleteTemp')"></i>
        </div>
        <div class="middle fs14 col333">
            <img class="icon-delete" th:src="@{/assets/images/website/img-delete.png}" alt="">
            <p class="tip">删除后无法恢复，确认删除？</p>
        </div>
        <div class="bottom-btns">
            <input type="button" value="取消" class="btn btn-fff-auto canncle-edit-name" onclick="hidePop('#recycleDeleteTemp')">
            <input type="button" value="确定" class="btn btn-blue-auto confirm-delete-btn">
        </div>
    </div>
</div>

<div class="loading">
    <div class="content">
        <img class="loading-gif" th:src="@{/assets/images/website/loading.gif}" alt="">
        <p class="loading-txt">正在检查更新...</p>
    </div>
</div>

<script id="website-data" type="text/x-dot-template">
    {{~ it:val}}
    <li data-websiteId="{{=val.id}}" class="website-li{{=val.id}}">
        <div class="img-box">
            <img src="{{=val.cover}}" alt="">
        </div>
        <div class="temp-info">
            <div class="edit-parents temp-name">
                <div class="disabled">
                    <span class="need-edit name">{{=val.name}}</span>
                    <span class="ui-btn-min btn-edit-name icon-edit"></span>
                </div>
                <div class="editing">
                    <input type="text" value="" class="name-ipt">
                    <span class="btn-editing btn-edit-cancel">取消</span>
                    <span class="btn-editing btn-edit-save">保存</span>
                </div>
            </div>
            <div class="edit-parents temp-dns">
                <label class="item-label">默认域名：</label>
                <div class="disabled">
                    <span class="need-edit dns">http://{{=host.split(':')[0]}}:{{=val.domain}}</span>
                    <span class="ui-btn-min btn-edit-dns icon-edit"></span>
                    <span class="ui-btn-min btn-copy icon-copy"></span>
                </div>
                <div class="editing">
                    <span class="dns-http"></span>
                    <input type="text" value="" class="dns-ipt border-btm ">
                    <span class="dns-post"></span>
                    <span class="btn-editing btn-edit-cancel">取消</span>
                    <span class="btn-editing btn-edit-save">保存</span>
                </div>
            </div>
            {{? !isEmpty(val.domainExternal)}}
            <div class="mg-top10">
                <label class="item-label">自有域名：</label>
                <span class="fs16 col666">{{=val.domainExternal}}</span>
            </div>
            {{?}}
        </div>
        <div class="right-btns">
            <a href="javascript:;" class="btn-right-edit" onclick="preview({{=val.id}})">预览</a>
            <!--<a th:if="${website.isSelf != 1}" th:href="@{/advanced/roles/{id}/role-list(id=${website.id})}" target="_blank" class="btn-right-edit btn-manage">管理</a>-->
            {{? val.isSelf == 1}}
            <a href="javascript:;" class="btn-right-edit" th:onclick="edit({{=val.id}})">编辑</a>
            {{?}}
            <a href="/advanced/{{=val.id}}/index" target="_blank" class="btn-right-edit btn-manage">管理</a>
        </div>
        {{? val.isSelf == 1}}
        <div class="btn-delete icon-rubbish webList-delete"></div>
        {{?}}
    </li>
    {{~}}
</script>


<script id="recycle-website-data" type="text/x-dot-template">
    {{~ it:val}}
    <tr>
        <td>{{=val.name}}</td>
        <td>
            <div class="refresh" onclick="updateStatus({{=val.id}}, 1)">
                <i class="icon-refresh"></i>
                <span>恢复</span>
            </div>
            <div class="delete" onclick="confirmDeleteWebsite({{=val.id}})">
                <i class="icon-rubbish"></i>
                <span>删除</span>
            </div>
        </td>
    </tr>
    {{~}}
</script>

<script th:src="@{/assets/lib/jquery.min.js?v=3027}"></script>
<script th:src="@{/assets/lib/jquery-migrate.min.js?v=3027}"></script>
<script th:src="@{/assets/js/common.js?v=3027}"></script>
<script th:src="@{/assets/js/doT.js}"></script>


<script type="text/javascript" th:inline="text">
    var ctx = "[[@{/}]]";
    ctx = "/" == ctx ? "" : ctx;
    ctx = ctx.endsWith("/") ? ctx.substr(0, ctx.length - 1) : ctx;
</script>
<script th:inline="javascript">
    var isMirror = [[${isMirror}]];
    var latestVersion = [[${latestVersion}]];
    var host = [[${host}]];
    var wfwfid = [[${wfwfid}]];
    var uid = [[${uid}]];

    $(function () {
        loadWebsites();
        initEidtTemp();
    })

    function showAddWebsite() {
        $("#websiteName").val('');
        $.get(ctx+'/website/domain/generate', {}, function (data) {
            $("#domainName").val(data.data);
        })
        $("#addTempPop").show();
    }

    function saveWebsite() {
        if(checkWebsite()){
            var name = $.trim($('#websiteName').val());
            var domain = $('#domainName').val();

            $.post(ctx + "/website/domain/validate", {domain: domain}, function (data) {
                if (data.code == 1) {
                    $("#addTempPop").hide();
                    window.location.href = ctx + "/website/template-select?name=" + encodeURIComponent(name) + "&domain=" + domain;
                } else {
                    alert(data.message);
                }
            });
        }
    }

    function checkWebsite() {
        if ($.trim($("#websiteName").val()).length == 0) {
            alert('网站名称不能为空');
            return false;
        }
        if($("#domainName").val().length < 3){
            alert("域名请至少输入三个字符");
        }
        // if (!checkWebsiteName($("#websiteName").val())) {
        //     alert('网站名称不能包含特殊符号');
        //     return false;
        // }
        if (!checkDomain($("#domainName").val())) {
            alert('域名只能包含字母和数字');
            return false;
        }
        return true;
    }

    function edit(websiteId) {
        window.open("http://"+host+ "/website/" + websiteId + "/edit");
    }

    function preview(websiteId) {
        window.open("http://"+host+ "/website/" + websiteId + "/show");
    }


    // 修改网站状态 status: false软删除 true恢复正常
    function updateStatus(websiteId, status){
        $.ajax({
            type: "post",
            url: ctx+"/website/update-status",
            data: {websiteId: websiteId, status: status},
            dataType: "json",
            success: function (data) {
                if(data.code==1){
                    if (status == 1) {
                        loadWebsites();
                    } else {
                        refreshRecycle(false);
                    }
                } else if (data.code == 0) {
                    console.log(data.message);
                }
            },
            error: function (data) {
                console.log("失败");
            }
        });
    }

    /*编辑模板名字*/
    function initEidtTemp() {
        /*编辑模板名字*/
        $('body').on('click', '.page-home .temp-list-box .temp-info .btn-edit-name', function () {
            var name = $(this).siblings('.need-edit').text();
            $(this).parents('.disabled').hide();
            $(this).parents('.edit-parents').find('.editing').show();
            $(this).parents('.edit-parents').find('.editing input').val(name).focus();

        });
        /*编辑模板域名*/
        $('body').on('click', '.page-home .temp-list-box .temp-info .btn-edit-dns', function () {
            $(this).parents('.disabled').hide();
            var url = $(this).siblings('.need-edit').text();
            var parents = $(this).parents('.edit-parents');
            if (url != '') {
                parents.find('.editing').css('display', 'inline-block');
                var http = url.split('//')[0] + '//'+host.split(':')[0]+ ':';
                parents.find('.dns-http').text(http);
                var zdy = url.split('.' + host)[0].split('//')[1];
                parents.find('.dns-ipt').val(url.split(':')[2]).focus();
            }
        });
        // $('body').on('blur', '.temp-list-box .editing input', function () {
        //     $(this).parents('.edit-parents').find('.editing').hide();
        //     $(this).parents('.edit-parents').find('.disabled').css('display', 'inline-block');
        // })
        /*取消编辑*/
        $('body').on('click', '.temp-list-box .editing .btn-edit-cancel', function () {
            $(this).parents('.edit-parents').find('.editing').hide();
            $(this).parents('.edit-parents').find('.disabled').css('display', 'inline-block');
        })

        /*保存修改名称*/
        $('body').on('click', '.temp-list-box .temp-name .btn-edit-save', function () {
            var parents = $(this).parents('.edit-parents');
            var newName = $.trim($(this).parents('.editing').find('input').val());
            if (newName.length == 0) {
                $(this).parents('.editing').find('input').focus();
                alert('网站名称不能为空');
                return false;
            }
            // if (!checkWebsiteName(newName)) {
            //     alert('网站名称不能包含特殊字符');
            //     $(this).parents('.editing').find('input').focus();
            //     return false;
            // }
            parents.find('.editing').hide();
            parents.find('.disabled').show();
            parents.find('.disabled .name').text(newName);
            var websiteId = $(this).parents('li').data('websiteid');
            updateWebsiteName(newName, websiteId);
        })
        /*保存修改域名*/
        $('body').on('click', '.temp-list-box .temp-dns .btn-edit-save', function () {
            var parents = $(this).parents('.edit-parents');
            var newZdy = $(this).parents('.editing').find('input').val();
            if(isEmpty(newZdy)){
                alert("域名不能为空");
                return;
            }
            if (!checkDomain(newZdy)) {
                alert('域名只能包含字母和数字');
                $(this).parents('.editing').find('input').focus();
                return false;
            }
            var websiteId = $(this).parents('li').data('websiteid');
            updateWebDomain(newZdy, websiteId, parents);
        })

        /*复制网址*/
        $('body').on('click', '.temp-info .btn-copy', function () {
            var url = $(this).parents('.edit-parents').find('.dns').text().trim();
            copyText(url, function () {
                toast({
                    content: "复制成功",
                    time: 3000,
                    type: 'success'
                })
            });
        });

        $('body').on('click', '.webList-delete', function () {
            $("#deleteTemp").show();
            var websiteId = $(this).parent().data("websiteid");

            // 确定删除
            $("#deleteTemp .sure-delete").off("click").click(function(){
                $("#deleteTemp").hide();
                updateStatus(websiteId, 0);
                // delweb.remove();
            });
        });

    }

    /*复制*/
    function copyText(text, callback) { // text: 要复制的内容， callback: 回调
        var tag = document.createElement('input');
        tag.setAttribute('id', 'cp_hgz_input');
        tag.value = text;
        document.getElementsByTagName('body')[0].appendChild(tag);
        document.getElementById('cp_hgz_input').select();
        document.execCommand('copy');
        document.getElementById('cp_hgz_input').remove();
        if (callback) {
            callback(text)
        }
    }


    /*显示弹框*/
    function showPop(div) {
        $(div).show();
    }

    function bindDomain(websiteId, domainExternal, domain, host) {
        // if(isEmpty(domainExternal)){
        //     $("#domainExternalInput").val('');
        // }else{
        //     $("#domainExternalInput").val(domainExternal);
        // }
        // $("#cname").text(domain + "." + host);
        // $("#websiteMirror").attr("href", "http://portal.chaoxing.com/export/transfer/" + websiteId + "/export");
        // $("#saveDomainExternalBtn").off("click").click(function(){
        //     var editVal = $("#domainExternalInput").val();
        //     if(isEmpty(editVal)){
        //         alert("单位域名不能为空");
        //         return;
        //     }
        //     if (!checkExternalDomain(editVal)) {
        //         alert('域名只能包含字母、数字和短横线');
        //         return;
        //     }
        //     if(editVal == domainExternal){
        //         $("#bindDnsPop").hide();
        //         return;
        //     }
        //     $.ajax({
        //         type:"post",
        //         url: ctx+'/website/update',
        //         data:{
        //             id: websiteId,
        //             domainExternal:editVal
        //         },
        //         dataType:"json",
        //         success:function(data){
        //             if(data.code == 1){
        //                 $("#bindDnsPop").hide();
        //                 location.reload();
        //             }else{
        //                 alert(data.message);
        //             }
        //         },
        //         error: function () {
        //             console.log("失败");
        //         }
        //     });
        // })

        // $("#bindDnsPop").show();
    }

    function updateWebsiteName(name,websiteId){
        $.ajax({
            type: "post",
            url: ctx+'/website/update',
            dataType: "json",
            data:{
                id: websiteId,
                name: name
            },
            success: function (data) {
                if(data.code && data.code==1){
                } else if (data.code == 0) {
                    alert(data.message);
                }
            },
            error: function (res) {
                console.log("失败");
            }
        });
    }

    function updateWebDomain(domain, websiteId, parents){
        $.ajax({
            type:"post",
            url: ctx+'/website/update',
            dataType:"json",
            data:{
                id: websiteId,
                domain:domain
            },
            success:function(data){
                if(data.code && data.code==1){
                    var newZdy = parents.find('.editing').find('input').val();
                    parents.find('.editing').hide();
                    parents.find('.disabled').css('display', 'inline-block');
                    var newUrl = parents.find('.dns-http').text() + newZdy + parents.find('.dns-post').text();
                    parents.find('.disabled .dns').text(newUrl);
                }else{
                    alert(data.message);
                    parents.find('.editing').find('input').focus();
                }
            },
            error:function(){
                console.log("失败");
            }
        });
    }


    /*关闭删除弹框*/
    function hidePop(selector) {
        if (selector) {
            $(selector).hide();
        } else {
            $('.pop').hide();
        }
    }

    function isEmpty(str) {
        return typeof(str) == 'undefined' || str == null || "" === $.trim(str);
    }

    function checkWebsiteName(str) {
        return /^[-\w\u4e00-\u9fa5]*$/.test(str);
    }

    function checkDomain(str) {
        return /^[a-zA-Z0-9]+$/.test(str);
    }

    function checkExternalDomain(str) {
        return /^[-a-zA-Z0-9]+(\.[-a-zA-Z0-9]+)*$/.test(str);
    }

    function checkUpdate() {
        loading();
        $.get('http://portal.chaoxing.com/export/check?version=' + latestVersion.versionId, {}, function (data) {
            var result = data.data;
            if(result.indexOf('最新版为：') != -1) {
                var downloadUrl = 'http://portal.chaoxing.com/export/check/upgrade?version=' + latestVersion.versionId;
                $("#newVersionDownloadBtn").attr("href", downloadUrl);
                $("#newVersionDownloadBtn").show();
            }
            $("#newVersionTip").text(result);
            $("#newVersionTip").show();
            $("#checkVersionBtn").hide();
        }).complete(function () {
            closeLoading();
        });
    }


    function loading() {
        $('.loading').show();
    }

    function closeLoading() {
        $('.loading').hide();
    }

    /**
     * 退出登录
     */
    function loginOut() {
        var keys = document.cookie.match(/[^ =;]+(?==)/g);
        if(host.indexOf(":")>-1){
            host = host.split(":")[0];
        }
        if (keys) {
            for (var i = keys.length; i--;) {
                document.cookie = keys[i] + '=0;path=/;domain=' + host + ';expires=' + new Date(0).toUTCString() // 清除当前域名下的，例如 .m.ratingdog.cn
            }
        }
        window.location ="/mirror";
    }

    function isEmpty(value){
        if(value == undefined || value == null || value === ""){
            return true;
        }
        return false;
    }

    function loadWebsites() {
        $.post(ctx + "/website/website-manage-list", {wfwfid: wfwfid, uid: uid, status: 1}, function (data) {
            if (data.code == 1) {
                var websiteTemp = doT.template($("#website-data").text());
                $("#websiteList").html(websiteTemp(data.data));
            } else {
                console.log(data.message);
            }
        });
    }

    function refreshRecycle(pop) {
        $.post(ctx + "/website/website-manage-list", {wfwfid: wfwfid, uid: uid, status: 0}, function (data) {
            if (data.code == 1) {
                var websiteTemp = doT.template($("#recycle-website-data").text());
                $("#recycleWebsites").html(websiteTemp(data.data));
            } else {
                console.log(data.message);
            }
        });
        if (pop) {
            showPop('#recycleBin');
        }
    }

    function confirmDeleteWebsite(id) {
        showPop('#recycleDeleteTemp');
        $("#recycleDeleteTemp .confirm-delete-btn").off("click").click(function () {
            $.post(ctx + "/website/" + id + "/delete", function (data) {
                if (data.code == 1) {
                    hidePop('#recycleDeleteTemp');
                    refreshRecycle(false);
                } else {
                    console.log(data.message);
                }
            });
        });

    }

    function hideRecyclePop() {
        loadWebsites();
        $("#recycleBin").hide();
    }
</script>
</body>
</html>
