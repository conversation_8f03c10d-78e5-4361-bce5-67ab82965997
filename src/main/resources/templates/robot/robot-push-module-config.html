<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org/">

<head>
    <meta charset="UTF-8">
    <title>智能体推送模块配置</title>
    <link rel="stylesheet" th:href="@{/assets/icomoon/style.css}">
    <link rel="stylesheet" th:href="@{/assets/lib/select2/select2.min.css}">
    <link rel="stylesheet" th:href="@{/assets/lib/mCustomScrollBar/jquery.mCustomScrollbar.css}">
    <link rel="stylesheet" th:href="@{/assets/css/common.css}">
    <link rel="stylesheet" th:href="@{/assets/css/website/advanced.css}">
    <style>
        [v-cloak] {
            display: none !important;
        }
    </style>
</head>
<script type="text/javascript" th:inline="text">
    var ctx = "[[@{/}]]".replace(/\/$/, "");
</script>
<body>
    <div class="white-list" id="moduleContent" v-cloak>
        <div class="page-content">
            <div class="temp-list module-check-list">
                <div class="clearfix">
                    <button class="btn-bg-blue btn-check-on" @click="goBack">返回</button>
                    <div class="custom-search fr">
                        <div class="layui-inline"><input placeholder="搜索模块名称" autocomplete="off" value="" class="layui-input" v-model.trim="moduleName" @keyup.enter="loadDatas(1)"></div>
                        <button class="layui-btn"><i class="layui-icon layui-icon-search" @click="loadDatas(1)"></i></button>
                    </div>
                </div>
                <form class="layui-form custom-form" action="#">
                    <table class="ui-table" border="1">
                        <tr>
                            <th>所属页面</th>
                            <th>模块名称</th>
                            <th>开启智能体推送</th>
                        </tr>
                        <tr v-for="(obj, index) in list">
                            <td>{{obj.pageName}}</td>
                            <td>{{obj.applicationName}}</td>
                            <td><input type="checkbox" name="appId" lay-skin="switch" :data-id="obj.appId" lay-filter="switchTest" lay-text="开|关" :checked="obj.status != 0" ></td>
                        </tr>
                    </table>
                </form>
            </div>
            <!-- 分页 -->
            <div id="laypage" class="laypage-style"></div>
        </div>
    </div>

    <script th:src="@{/assets/lib/jquery.min.js?v=0312}"></script>
    <script th:src="@{/assets/lib/mCustomScrollBar/jquery.mousewheel.min.js?v=0312}"></script>
    <script th:src="@{/assets/lib/mCustomScrollBar/jquery.mCustomScrollbar.min.js?v=0312}"></script>
    <script th:src="@{/assets/lib/select2/select2.min.js?v=0312}"></script>
    <script th:src="@{/assets/js/common.js?v=0312}"></script>
    <script th:src="@{/assets/js/dropzone.min.js}"></script>
    <script th:src="@{/assets/js/component.fileupload.js}"></script>
    <script th:src="@{/assets/lib/vue.js}"></script>

    <link rel="stylesheet" th:href="@{/assets/lib/layui/css/layui.css?v=020911}" media="all">
    <script th:src="@{/assets/lib/layui/layui.js?v=020911}" charset="utf-8"></script>

    <script th:inline="javascript">
        var indexid = '[[${websiteId}]]';
        var wfwfid = '[[${wfwfid}]]';
      var  moduleVm = new Vue({
            el: '#moduleContent',
            data: {
                websiteId: [[${websiteId}]],
                list: [],
                generalData: {},
                total: 0,
                pageIndex: [[${pageIndex}]],
                pageSize: 10,
                moduleName: ''
            },
            created: function () {
                var that = this;
            },
            mounted: function () {
                var that = this;
                that.loadDatas(that.pageIndex);
                that.initSwitch();
            },
            updated:function(){
                layui.use(['form', 'laypage'], function () {
                    var form = layui.form;
                    form.render();
                });
            },
            methods: {
                goBack: function (){
                    var that = this;
                    var url = ctx + "/beginner-guide/"+that.websiteId;
                    window.location.href=url;
                },
                initPage: function (pageNum) {
                    var that = this;
                    that.pageIndex = pageNum;
                    layui.use(['form', 'laypage'], function () {
                        var form = layui.form;
                        layui.laypage.render({
                            elem: 'laypage',
                            curr: that.pageIndex,
                            limit: that.pageSize,
                            count: that.total,
                            layout: ['count', 'prev', 'page', 'next', 'refresh', 'skip'],
                            jump: function (obj, first) {
                                that.pageIndex = obj.curr;
                                if(!first){
                                    that.loadDatas(that.pageIndex);
                                    form.render();
                                }
                            }
                        });
                        layui.use(['form', 'laypage'], function () {
                            var form = layui.form;
                            form.render();
                        });
                    });
                },
                loadDatas: function (pageNum) {
                    var that = this;
                    var url = ctx + "/robot-push-module-config/getConfigList";
                    var params = {
                        websiteId: that.websiteId,
                        pageIndex: that.pageIndex-1,
                        pageSize: that.pageSize,
                        searchWord: that.moduleName
                    };
                    $.ajax({
                        url: url,
                        type: "get",
                        data: params,
                        dataType: "json",
                        success: function(data){
                            if(data.code == 1){
                                that.total = data.data.record.total;
                                that.list = data.data.record.list;
                                that.initPage(pageNum);
                                that.initLayUi();
                                that.initCheckBox();
                                layui.use(['form', 'laypage'], function () {
                                    var form = layui.form;
                                    form.render();
                                });
                            }
                        },
                        error:function(){
                            layui.use('layer', function(){
                                layer.msg("加载数据错误");
                            });
                        }
                    });
                },
                initSwitch: function () {
                    var that = this;
                    layui.use(['form', 'laypage'], function () {
                        var form = layui.form;
                        //监听开关事件
                        form.on('switch(switchTest)', function (data) {
                            var status = data.elem.checked == true ? 1 : 0;//判断开关状态
                            var appId = $(this).attr("data-id");
                            that.updateStatus(appId, status);
                        });
                        form.render();
                    });
                },
                initLayUi: function(){
                    layui.use(['form', 'laypage'], function () {
                        var form = layui.form;
                        form.render();
                    });
                },
                showLoading: function () {
                    var that = this;
                    that.loading = layer.load(0, {
                        shade: 0.2, // 遮罩
                    });
                },
                closeLoading: function () {
                    var that = this;
                    layer.close(that.loading);
                },
                isEmpty: function (str) {
                    return typeof(str) == 'undefined' || str == null || "" === $.trim(str);
                },
                updateStatus: function (appId, status) {
                    var that = this;
                    var url = ctx + "/robot-push-module-config/updateStatus";
                    var params = {
                        appId: appId,
                        status: status,
                        websiteId:  that.websiteId
                    };
                    $.ajax({
                        url: url,
                        type: "get",
                        data: params,
                        dataType: "json",
                        success: function(data){
                            var list = that.list;
                            for (var i = 0; i < list.length; i++) {
                                if(appId == list[i].appId){
                                    list[i].status = status;
                                }
                            }
                        },
                        error:function(){
                            layui.use('layer', function(){
                                layer.msg("修改出错");
                            });
                        }
                    });
                },
                build: function (id, fid) {
                    var that = this;
                    var id = JSON.stringify(id)
                    var url = ctx + "/module-audit/build?instanceId="+id+"&fid="+fid+"&websiteId="+indexid+"&pageIndex="+that.pageIndex;
                    window.location.href=url;
                }
            },
        });
        /*显示弹框*/
        function showPop(div) {
            $('.pop').hide();
            $(div).show();
        }

        /*关闭删除弹框*/
        function hidePop() {
            $('.pop').hide();
        }

        function post(URL, PARAMS) {
            var temp = document.createElement("form");
            temp.action = URL;
            temp.method = "post";
            temp.style.display = "none";
            for (var x in PARAMS) {
                var opt = document.createElement("textarea");
                opt.name = x;
                opt.value = PARAMS[x];
                temp.appendChild(opt);
            }
            document.body.appendChild(temp);
            temp.submit();
            return temp;
        }
    </script>
</body>

</html>
