<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org/">

<head>
    <meta charset="UTF-8">
    <title>用户访问量</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">

    <link rel="stylesheet" th:href="@{/assets/icomoon/style.css?v=20210621}">
    <link rel="stylesheet" th:href="@{/assets/lib/layui/css/layui.css?v=020911}">
    <link rel="stylesheet" th:href="@{/assets/css/common.css?v=20210621}">
    <link rel="stylesheet" th:href="@{/assets_dy/css/new_statistics.css?v=20210621}">

    <!-- 自定义图标 -->
    <link rel="stylesheet" th:href="@{/assets_dy/icomoon/style.css?v=20210621}" media="all">
    <style>
        .custom-select .pvst input{
            width: 100px;
        }
    </style>
</head>

<body>
<div>

    <!--中间内容-->
    <div class="right-content">
        <!--面包屑-->
        <ul class="bread-crumb">
            <li><a href="#">统计</a> <span class="icon icon-down"></span></li>
            <li class="active">用户访问量</li>
        </ul>
        <div>
            <div class="echart-box">
                <div class="echart-top">
                    <h2 class="fl">个人访问量</h2>
                    <div class="fr">
                        <div class="layui-form">
                            <div class="layui-form-item">
                                <div class="layui-inline">
                                    <div class="layui-input-inline calendar-input">
                                        <i class="icon-ct-calendar"></i>
                                        <input type="text" class="layui-input visit-date" id="userDateRange" placeholder="请选择起始时间">
                                    </div>
                                    <button type="button" class="custom-link" onclick="exportUserData()"><i class="icon-ct-export"></i>导出</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <table class="ui-table" border="1">
                    <tbody id="userTable">
                    </tbody>
                </table>
                <div id="laypage" class="laypage-style"></div>
            </div>
        </div>
    </div>
</div>
<script th:src="@{/assets/lib/jquery.min.js}"></script>
<script th:src="@{/assets/lib/layui/layui.js?v=020911}"></script>
<script th:src="@{/assets/lib/echarts2/echarts-all.js}"></script>
<script type="text/javascript" th:inline="text">
    var ctx = "[[@{/}]]".replace(/\/$/, "");
</script>
<script type="text/javascript" th:inline="text">
    var mirror = [[${mirror}]];
    var websiteId = [[${websiteId}]];
    var loadingLayer = null;
    var pageSize = 15;
    var total = 20;
    $(function () {
        calendarInit();
        loadUserTable(1);
    });


    // 日历的初始化
    function calendarInit() {
        layui.use('laydate', function () {
            var laydate = layui.laydate;
            layui.use('laydate', function () {
                var laydate = layui.laydate;
                laydate.render({
                    elem: "#userDateRange",
                    range: true,
                    theme: '#3D82F2',
                    trigger: 'click',
                    done: function(value, date, endDate){
                        $("#userDateRange").val(value);
                        loadUserTable(1);
                    }
                });
            })
        })
    }

    function loadUserTable(pageNum) {
        var dateRangeStr = $("#userDateRange").val();
        var startTime = "";
        var endTime = "";
        if(!isEmpty(dateRangeStr)) {
            startTime = $.trim(dateRangeStr.split(" - ")[0]);
            endTime = $.trim(dateRangeStr.split(" - ")[1]);
        }
        var param = {
            websiteId: websiteId,
            endTime: endTime,
            startTime: startTime,
            pageNum: pageNum,
            pageSize: pageSize
        };
        $.getJSON(ctx+"/data-count/website/"+websiteId+"/uv-statistics-list",param,function(json){
            var sq = 1;
            var html ='<tr>\n' +
                '<th style="width: 10%;">序号</th>\n' +
                '<th style="width: 30%;">用户名</th>\n' +
                '<th style="width: 30%;">UID</th>\n' +
                '<th style="width: 30%;">浏览量</th>\n' +
                '</tr>';
            json.data.list.forEach(function(d){
                html +='<tr>' +
                    '<td>'+ sq++ +'</td>'+
                    '<td>'+dealEmptyValue(d.name, '')+'</td>'+
                    '<td>'+dealEmptyValue(d.uid, '')+'</td>'+
                    '<td>'+dealEmptyValue(d.vcount, '0')+'</td>'+
                    '</tr>';
            })
            $("#userTable").html(html);

            total = json.data.total;

            initPage(pageNum, json.data.total);
        });
    }

    function exportUserData() {
        var type = $("#userDateType").find("li.active").attr("data-type");
        var dateRangeStr = $("#userDateRange").val();
        var startTime = "";
        var endTime = "";
        if(!isEmpty(dateRangeStr)) {
            startTime = $.trim(dateRangeStr.split(" - ")[0]);
            endTime = $.trim(dateRangeStr.split(" - ")[1]);
        }
        if(!type){
            type = '';
        }
        var url = ctx + "/data-count/" + websiteId + "/uv-statistics-export?startTime="+startTime+"&endTime="+endTime+"&type="+type+"&pageSize=" +total;
        if(!mirror) {
            $.ajax({
                type: "get",
                url: url,
                success: function (data) {
                    console.log(data);
                    layui.use(function () {
                        var layer = layui.layer;
                        layer.msg("数据准备中，请稍后到下载中心查看")
                    })
                },
                error: function (res) {
                    console.log(res.message);
                    layui.use(function () {
                        var layer = layui.layer;
                        layer.msg(res.message)
                    })
                }
            });
        } else {
            window.open(url);
        }
    }

    function initPage(pageNum, total) {
        layui.use(["laypage"], function () {
            layui.laypage.render({
                elem: 'laypage',
                curr: pageNum,
                limit: pageSize,
                count: total,
                layout: ['count', 'prev', 'page', 'next', 'skip'],
                jump: function (obj, first) {
                    if(!first){
                        loadUserTable(obj.curr);
                    }
                }
            });
        });
    }

    /*是否为空*/
    function isEmpty(value) {
        if (value == undefined || value == null || value === "") {
            return true;
        }
        return false;
    }
    function dealEmptyValue(value, defaultValue) {
        if (isEmpty(value)) {
            return defaultValue;
        } else {
            return value;
        }
    }
    function showLoading() {
        loadingLayer = layer.load(0, {
            shade: 0.2, // 遮罩
        });
    }
    function closeLoading() {
        layer.close(loadingLayer);
    }
</script>
</body>

</html>