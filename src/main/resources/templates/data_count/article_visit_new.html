<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org/">

<head>
    <meta charset="UTF-8">
    <title>微服务智慧门户</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">

    <link rel="stylesheet" th:href="@{/assets/icomoon/style.css?v=20210621}">
    <link rel="stylesheet" th:href="@{/assets/lib/layui/css/layui.css?v=020911}">
    <link rel="stylesheet" th:href="@{/assets/css/common.css?v=20210621}">
    <link rel="stylesheet" th:href="@{/assets_dy/css/new_statistics.css?v=20210621}">

    <!-- 自定义图标 -->
    <link rel="stylesheet" th:href="@{/assets_dy/icomoon/style.css?v=20210621}" media="all">
</head>

<body>
    <div>
        <!--中间内容-->
        <div class="right-content">
            <!--面包屑-->
            <ul class="bread-crumb">
                <li><a href="javascript:">统计</a> <span class="icon icon-down"></span></li>
                <li th:if="${sourcePage == 'profileData'}"><a th:href="${'/data-count/data-overview?websiteId=' + websiteId}">数据概况</a> <span class="icon icon-down"></span></li>
                <li th:if="${sourcePage == 'typeVisit'}"><a th:href="${'/data-count/module-statistics?websiteId=' + websiteId}">模块统计</a> <span class="icon icon-down"></span></li>
                <li th:if="${sourcePage == 'typeVisit'}"><a th:href="${'/data-count/website/' + websiteId + '/type-visit' + (instanceId != null ? '?instanceId=' + instanceId : '')}">分类访问详情</a> <span class="icon icon-down"></span></li>
                <li class="active">文章浏览量</li>
            </ul>
            <div>
                <div class="echart-box">
                    <div class="echart-top">
                        <h2 class="fl">文章访问量排名</h2>
                        <div class="custom-select layui-input-block fr">
                            <form class="layui-form" action="">
                                <select name="city" id="typeSelect" lay-verify="required" lay-filter="typeSelect">
                                    <option value=""></option>
                                </select>
                            </form>
                        </div>
                        <div class="custom-select layui-input-block fr">
                            <form class="layui-form" action="">
                                <select name="city" id="engineSelect" lay-verify="required" lay-filter="engineSelect">
                                    <option value=""></option>
                                    <option th:each="engine, stat: ${engineList}" th:value="${engine.id}">[[${engine.name}]]</option>
                                </select>
                            </form>
                        </div>
                        <div class="fr" style="margin-right: 20px;">
                            <div class="layui-form">
                                <div class="layui-form-item">
                                    <div class="layui-inline">
                                        <div class="layui-input-inline calendar-input">
                                            <i class="icon-ct-calendar"></i>
                                            <input type="text" id="articleDateRange" class="layui-input visit-date" placeholder="请选择起始时间">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <ul class="fr filter-times" id="articleDateType">
                            <li class="active" data-type="1">近7日</li>
                            <li data-type="2">近30日</li>
                        </ul>
                    </div>
                    <table class="ui-table" border="1">
                        <tbody id="articleVisitTable">
                        </tbody>
                    </table>
                    <div id="laypage" class="laypage-style"></div>
                    <div class="echart-bottom">
                        <button type="button" data-method="offset" data-type="auto" class="layui-btn fr" onclick="exportArticleTableData()">导出明细</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script th:src="@{/assets/lib/jquery.min.js}"></script>
    <script th:src="@{/assets/lib/layui/layui.js?v=020911}"></script>
    <script th:src="@{/assets/lib/echarts2/echarts-all.js}"></script>
    <script type="text/javascript" th:inline="text">
        var ctx = "[[@{/}]]".replace(/\/$/, "");
    </script>
    <script th:inline="javascript">
        var websiteId = [[${website.id}]];
        var instanceId = [[${instanceId}]];
        $(function () {
            calendarInit();
            initSelect();
            changestatisticsPeriod();
            loadArticleTable(1);
            if(instanceId){
                getEngineTypes(instanceId);
            }
        });

        function loadArticleTable(pageNum) {
            var type = $("#articleDateType").find("li.active").attr("data-type");
            var dateRangeStr = $("#articleDateRange").val();
            var startTime = "";
            var endTime = "";
            if(!isEmpty(dateRangeStr)) {
                startTime = $.trim(dateRangeStr.split(" - ")[0]);
                endTime = $.trim(dateRangeStr.split(" - ")[1]);
            }
            var param = {
                type: type,
                startTime: startTime,
                endTime: endTime,
                page: pageNum - 1,
                websiteId: websiteId
            };
            if (!isEmpty($("#engineSelect").val())) {
                param.instanceId = $("#engineSelect").val();
            }
            if (!isEmpty($("#typeSelect").val())) {
                param.typeId = $("#typeSelect").val();
            }
            $.getJSON(ctx+"/data-count/website/"+websiteId+"/article-visit-list", param, function(json){
                var sq = 1;
                var html ='<tr>\n' +
                    '<th style="width: 8%;">序号</th>\n' +
                    '<th style="width: 32%;">文章名称</th>\n' +
                    '<th style="width: 12%;">所属模块</th>\n' +
                    '<th style="width: 12%;">所属分类</th>\n' +
                    '<th style="width: 12%;">总浏览量</th>\n' +
                    '</tr>';
                json.data.list.forEach(function(d){
                    html +='<tr>' +
                        '<td>'+ sq++ +'</td>'+
                        '<td>'+d.title+'</td>'+
                        '<td>'+d.engineName+'</td>'+
                        '<td>'+d.typeName+'</td>'+
                        '<td>'+d.pv+'</td>';
                    html +='</td>';
                    html += '</tr>';
                })
                $("#articleVisitTable").html(html);
                initEnginePage(pageNum, json.data.total);
            });
        }

        // 日历的初始化
        function calendarInit() {
            layui.use('laydate', function () {
                var laydate = layui.laydate;
                laydate.render({
                    elem: "#articleDateRange",
                    range: true,
                    theme: '#3D82F2',
                    trigger: 'click',
                    done: function(value, date, endDate){
                        $("#articleDateRange").val(value);
                        loadArticleTable(1);
                    }
                });
            })
        }

        // 图表数据年月日的切换
        function changestatisticsPeriod() {
            $(".filter-times>li").on("click", function () {
                $(this).addClass("active").siblings().removeClass("active");
                var parentId = $(this).parent().attr("id");
                if (parentId == 'articleDateType') {
                    loadArticleTable(1);
                }
            })
        }

        function initEnginePage(pageNum, total) {
            layui.use(["laypage"], function () {
                layui.laypage.render({
                    elem: 'laypage',
                    curr: pageNum,
                    limit: 10,
                    count: total,
                    layout: ['count', 'prev', 'page', 'next', 'skip'],
                    jump: function (obj, first) {
                        if(!first){
                            loadArticleTable(obj.curr);
                        }
                    }
                });
            });
        }

        function initSelect() {
            layui.use('form', function(){
                var form = layui.form;
                form.on('select(engineSelect)', function(data){
                    getEngineTypes(data.value);
                    loadArticleTable(1);
                });
                form.on('select(typeSelect)', function(data){
                    loadArticleTable(1);
                });
                if (instanceId != null) {
                    $("#engineSelect").val(instanceId);
                }
                form.render();
            });
        }

        /*是否为空*/
        function isEmpty(value) {
            if (value == undefined || value == null || value === "") {
                return true;
            }
            return false;
        }

        function exportArticleTableData() {
            var type = $("#articleDateType").find("li.active").attr("data-type");
            var dateRangeStr = $("#articleDateRange").val();
            var startTime = "";
            var endTime = "";
            if(!isEmpty(dateRangeStr)) {
                startTime = $.trim(dateRangeStr.split(" - ")[0]);
                endTime = $.trim(dateRangeStr.split(" - ")[1]);
            }
            var url = ctx + "/data-count/website/" + websiteId + "/article-data-export?type=" + type + "&startTime=" + startTime + "&endTime=" + endTime;
            if (!isEmpty($("#engineSelect").val())) {
                url += "&instanceId=" + $("#engineSelect").val();
            }
            window.open(url);
        }

        function getEngineTypes(instanceId){
            var param = {
                websiteId: websiteId,
                instanceId: instanceId,
            };
            $.getJSON(ctx+"/data-count/website/"+websiteId+"/module-types", param, function(json){
                $("#typeSelect").html('');
                json.data.forEach(function(d){
                    var option = $('<option>').val(d.id).text(d.name)
                    $("#typeSelect").append(option);
                })

                layui.use('form', function(){
                    var form = layui.form;
                    form.render();
                });
            });
        }
    </script>
</body>

</html>