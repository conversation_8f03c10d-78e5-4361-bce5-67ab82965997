<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org/">
<head>
    <meta charset="UTF-8">
    <title>登录同步</title>
</head>
<body>

</body>
<script th:inline="javascript">
    var sign = [[${sign}]];
    var url = [[${url}]];
    // 构建form定位到portal.chaoxing.com/sso-login/cookie/sync
    var cookieForm = document.createElement("form");
    document.body.appendChild(cookieForm);
    cookieForm.method = 'post';
    // 域名
    cookieForm.action = '//portal.chaoxing.com/sso-login/cookie/sync';

    var signElement = document.createElement("input");
    signElement.setAttribute("type","hidden");
    signElement.name = "sign";
    signElement.value = sign;
    cookieForm.appendChild(signElement);

    var urlElement = document.createElement("input");
    urlElement.setAttribute("type","hidden");
    urlElement.name = "url";
    urlElement.value = url;
    cookieForm.appendChild(urlElement);

    cookieForm.submit();
</script>
</html>