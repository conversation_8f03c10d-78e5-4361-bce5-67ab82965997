<!doctype html><html lang="zh-CN"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="ie=edge"><meta name="renderer" content="webkit"><title></title><link rel="stylesheet" href="/assets/css/vendor.min.css"><link rel="stylesheet" href="/assets/lib/layui/css/layui.css"><link rel="stylesheet" href="/assets/icomoon/style.css?v=20240717"><link rel="stylesheet" href="/assets/css/page.min.css?v=20240717"><link rel="stylesheet" href="/assets/css/library_normal/editor.css?v=20240717"></head><body><div class="editor-page" id="index" v-cloak><general-top ref="generalTop" @toast="showToastEvent" @save="getCoordinates"></general-top><pop-nav bgtype="fullScreen" :bgs="pageBgs" :bgmusic="musicStyle" :showheader="showHeader" :showbanner="showBanner" :showfooter="showFooter" :showothermodule="otherGlobalModule" :webjson="webJson" @updateglobalmodule="updateModule" @updatebg="updateBg" @updatebgmusic="updateBgmusic"></pop-nav><div class="content"><div class="aid-groups"><div id="fullpage"><div class="section full-slide" v-for="(nav,index) in leftNavs" :class="index===0?'multi-nest-container':''"><div v-if="index===0"><div id="header" class="full-width base-container" v-show="showHeader"><ul id="menu"></ul></div><div id="banner" class="base-container" v-show="showBanner"></div><div id="sidGroups"><div id="contact" v-show="showQuery"></div></div></div><div v-if="pageBg[index] && pageBg[index].img && pageBg[index].styleClass" class="bg-box full-slide-bg" :class="pageBg[index].styleClass" :style="{'background-image':'url('+pageBg[index].img+')','opacity':pageBg[index].ransparency}"></div><div v-else-if="pageBg[index] && pageBg[index].img && !pageBg[index].styleClass" class="full-slide-bg" :class="pageBg[index].showStyle==0?'img-stretch':'img-tile'" :style="{'background-image':'url('+pageBg[index].img+')','opacity':pageBg[index].ransparency}"></div><div v-else-if="pageBg[index] && pageBg[index].bgColor" class="full-slide-bg" :style="{'background-color':pageBg[index].bgColor}"></div><div class="grid-stack-wrap grid-wrap editor-grid" :class="index===0?'nest-container':''"><div class="guides"></div><div class="grid-stack-main"><div class="grid-stack" :id="'grid'+(index+1)" :data-nav-id="nav.id"></div></div></div></div></div></div></div><div class="ft-nav-wrap" :class="{'anchorPositoinTL':leftNavSets.typeId && leftNavSets.typeId==8}"><div class="general-set"><div><a class="set-btn" target="_blank" :href="'/engine2/navigation/admin/basic?pageId='+pageId"><span class="icon-set"></span><span>设置</span></a></div></div><div id="fp-nav"><ul class="pagination-color pagination-circle1" v-if="leftNavSets.typeId && leftNavSets.typeId==1"><li v-for="(nav,index) in leftNavs" class="nav"><a href="#" :class="{'active':index==0}"><span class="pagination"></span></a></li></ul><ul class="pagination-block" v-else-if="leftNavSets.typeId && leftNavSets.typeId==2"><li v-for="(nav,index) in leftNavs" class="nav"><a href="#" :class="{'active':index==0}"><img v-if="nav.url" :src="nav.url" class="pagination-item-icon"><p class="fs12 col999 overHidden1">{{nav.name}}</p></a></li></ul><ul class="pagination-color pagination-big-circle" v-else-if="leftNavSets.typeId && leftNavSets.typeId==3"><li v-for="(nav,index) in leftNavs" class="nav"><a href="#" :class="{'active':index==0}"><span class="pagination">{{index>10?'0'+index+1:index+1}}</span></a></li></ul><ul class="pagination-color common-right-nav1" v-else-if="leftNavSets.typeId&&leftNavSets.typeId==4"><li v-for="(item,index) in leftNavs" class="nav"><a :href="'#grid'+(index+1)"><img v-if="item.url" :src="item.url" class="pagination-item-icon"><p v-else class="placeholder"></p><p class="overHidden1" :title="item.name">{{item.name}}</p></a></li></ul><ul class="pagination-color common-right-nav2" v-if="leftNavSets.typeId&&leftNavSets.typeId==5"><li v-for="(item,index) in leftNavs" class="nav"><a :href="'#grid'+(index+1)"><img v-if="item.url" :src="item.url" class="pagination-item-icon"><p class="anchors-name">{{item.name}}</p></a></li></ul><ul class="pagination-color common-right-nav3" v-if="leftNavSets.typeId==6"><li v-for="(item,index) in leftNavs" class="nav bg-hover-theme"><a :href="'#grid'+(index+1)"><img v-if="item.url" :src="item.url" class="pagination-item-icon img-normal"> <img v-if="item.url" :src="item.url2" class="pagination-item-icon img-hover"><p v-else class="placeholder"></p><p class="overHidden1">{{item.name}}</p></a></li></ul><div class="common-left-nav1" v-if="leftNavSets.typeId && leftNavSets.typeId==8" :style="{'background-image':'url('+leftNavSets.logoUrl+')'}"><ul class="pagination-color txt-theme fs22"><li v-for="(nav,index) in leftNavs" class="nav"><a href="#" :class="{'active':index==0}"><p class="overHidden1">{{nav.name}}</p></a></li></ul><div class="toggle-box" @click="anchorToggleFunc"><div class="btn-box bg-theme fs18 btn-open"><span class="txt-1">收起</span><span class="txt-2">展开</span><i class="icon-arrow-down2"></i></div></div></div><div class="pagination-color common-right-nav4" v-if="leftNavSets.typeId && leftNavSets.typeId==9" :style="{'background-image':'url('+leftNavSets.logoUrl+')'}"><ul class="anchors-nav"><li v-for="(item,index) in leftNavs" class="nav"><a href="#"><div class="img-box"><img v-if="item.url" :src="item.url" class="pagination-item-icon"></div><p class="overHidden1">{{item.name}}</p></a></li></ul></div><div class="pagination-color common-right-nav5" v-if="leftNavSets.typeId && leftNavSets.typeId==10"><div class="logo-box" @click="anchors10Toggle($event)"><img :src="leftNavSets.logoUrl" alt=""></div><ul class="anchors-nav bg-theme-rgba20"><li v-for="(item,index) in leftNavs" class="nav"><a href="#" class="bg-hover-theme"><div class="img-box"><img v-if="item.url" :src="item.url" class="pagination-item-icon"> <img v-if="item.url2" :src="item.url2" class="pagination-item-icon-active"></div><span class="overHidden1">{{item.name}}</span></a></li></ul></div></div></div><div id="nameEdit" class="pop name-edit-pop" v-show="showEditName"><div class="pop-content"><div class="top clear"><span class="fs16 colo666 fl" id="moduleTitle"></span> <i class="icon icon-del fr canncle-edit-name"></i></div><div class="middle fs14 col333"><label>应用生成名称:</label> <input class="inp-name" id="moduleNewName" type="text" placeholder="输入模块名称"></div><div class="bottom-btns"><input class="btn btn-fff-auto canncle-edit-name" type="button" value="取消"> <input class="btn btn-blue-auto" type="button" value="保存" id="saveName"></div></div></div><div class="toast" v-show="showToast"><span v-if="toastType" class="icon" :class="{'icon-failure':toastType =='failure','icon-succeed':toastType=='success'}"></span> <span>{{toastMessage}}</span></div><edit-btn @toast="showToastEvent"></edit-btn><module-set ref="moduleSet" @toast="showToastEvent"></module-set><edit-img></edit-img><picture-lib ref="pictureLib"></picture-lib><bay-window-set ref="bayWindowSet"></bay-window-set><bay-window v-show="showBayWindow" ref="bayWindow"></bay-window><video-set ref="videoSet"></video-set><layout-tab-set></layout-tab-set><search-set ref="searchSet"></search-set><weather-date-set ref="weatherDate"></weather-date-set><plugin-set ref="pluginSet"></plugin-set><global-iframe v-show="showGlobalIframe" ref="globalIframe"></global-iframe><global-iframe-set ref="globalIframeSet"></global-iframe-set><div id="footer" class="full-width base-container" v-show="showFooter && activePageIndex==leftNavs.length"></div><page-close ref="showPageForbid"></page-close><bg-music v-show="musicStyle.show" ref="bgMusic"></bg-music><maps-set ref="mapsSet"></maps-set><edit-select ref="selectSet"></edit-select></div><script src="/assets/lib/jquery.min.js"></script><script src="/assets/lib/vue.js"></script><script src="/assets/lib/layui/layui.js?v=020911"></script><script>"https"==document.location.protocol.split(":")[0]&&(window.HOST_TYPE="2")</script><script src="/assets/js/all.min.js"></script><script src="/assets/lib/echarts4/echarts.min.js"></script><script src="/assets/lib/echarts4/china.js"></script><script src="/assets/lib/echarts4/world.js"></script><script src="/assets/js/component.min.js"></script><script src="/assets/lib/ckeditor/ckeditor.js"></script><script src="/assets/js/page.min.js?v=20240717"></script><script src="/assets/js/library_normal/editor.js?v=20240717"></script></body></html>