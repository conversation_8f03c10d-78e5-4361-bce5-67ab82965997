<!DOCTYPE html><html><head><meta charset="utf-8"><meta name="viewport" content="width=device-width"><title>表单模板</title><link rel="stylesheet" href="/assets/css/common.css?v=20240717"><link rel="stylesheet" href="/assets/icomoon/style.css?v=20240717"><link rel="stylesheet" href="/assets/css/index_common.css?v=20240717"><link rel="stylesheet" href="/assets/lib/jQueryUi/jquery.ui.css"><link rel="stylesheet" href="/assets/lib/spectrum/spectrum.css"><link rel="stylesheet" href="/assets/css/general_top.css?v=20240717"><link rel="stylesheet" href="/assets/css/pop_nav.css?v=20240717"><link rel="stylesheet" href="/assets/css/form_lists/dialog.css?v=20240717"><link rel="stylesheet" href="/assets/css/form_lists/editor.css?v=20240717"><style>[v-cloak]{display:none}</style></head><body><div id="editor" class="row" v-cloak><div id="pageTopWrap"><div class="page-top"><div class="w1180 clear"><div class="pages fl"><div class="fs14 default-color show-name"><span class="cur-page">{{pageName}}</span> <i class="btn-edit-form icon-edit" @click="dataSourceFunc(formId)"></i></div></div><div class="btns fr"><input type="button" value="预览" class="btn btn-fff-auto" @click="getCoordinates('scan')"> <input type="button" value="保存" class="btn btn-blue-auto" @click="getCoordinates('save')"></div></div></div></div><pop-nav :bg="pageBg" :webjson="webJson" @updatebg="updateBg"></pop-nav><div id="items-1" class="list-group list-dash-border"><div v-for="(item,index) in myArray" :key="index" class="list-group-item" :data-app-id="item.newId"><div class="top-btns"><div class="top-btn-groups"><span class="set module-set" @click="setFunc(item, index)"><i class="icon-set icon"></i>设置</span> <span class="delete" @click="deleteFunc(item,index)"><i class="icon-rubbish icon"></i><span>删除</span></span> <span class="handle"><i class="icon-drag icon"></i><span>拖拽</span></span></div></div><div class="module-box" :class="{'padding50':item.id!==4}"><div class="item-engine-title clear txt-l" v-if="item.id!==4"><span class="title-t"><span class="title-icon bg-theme"></span> <span class="inlineMiddle">{{item.name}}</span></span></div><div v-if="item.id===4"><div class="text-box"><div class="text-avatar" v-if="DataBox[item.newId][0] && DataBox[item.newId][0].value"><img :src="DataBox[item.newId][0].value" alt=""></div><div class="text-top"><p class="text-top-name overHidden1" v-if="DataBox[item.newId][1] && DataBox[item.newId][1].value">{{DataBox[item.newId][1].value}}</p><div class="text-top-intro fs14"><span v-if="DataBox[item.newId][2] && DataBox[item.newId][2].value">{{DataBox[item.newId][2].value}}</span> <span v-if="DataBox[item.newId][3] && DataBox[item.newId][3].value">{{DataBox[item.newId][3].value}}</span> <span v-if="DataBox[item.newId][4] && DataBox[item.newId][4].value">{{DataBox[item.newId][4].value}}</span></div></div><div class="text-bottom"><ul class="text-bottom-lists" v-if="DataBox[item.newId]"><li v-for="(itemtext,indextext) in DataBox[item.newId]" :key="indextext" v-if="indextext > 4 && indextext < 11"><p class="list-name" v-if="itemtext.key != undefined">{{itemtext.key}}</p><p class="list-val" v-if="itemtext.value != undefined">{{itemtext.value}}</p></li></ul></div></div></div><div class="table-box" v-if="item.id===1"><div class="table-content"><table class="common-table" v-if="!item.hasData"><thead class="thead"><tr><th v-for="(item_th,index_th) in tableTest.head" :key="index_th">{{item_th.name}}</th></tr></thead><tbody class="tbody"><tr v-for="(item_tr,index_tr) in tableTest.data" :key="index_tr" class="txt-c"><td v-for="(item_td,index_td) in item_tr" :key="index_td">{{item_td.value}}</td></tr></tbody></table><table class="common-table" v-else><thead class="thead"><tr><th v-for="(item_th,index_th) in DataBox[item.newId].head" :key="index_th">{{item_th.name}}</th></tr></thead><tbody class="tbody"><tr v-for="(item_tr,index_tr) in DataBox[item.newId].data" :key="index_tr" class="txt-c"><td v-for="(item_td,index_td) in item_tr" :key="index_td">{{item_td.value}}</td></tr></tbody></table></div></div><div class="richtext-box" v-if="item.id===2"><div class="richtext-content" v-if="!item.hasData">{{DataBox[item.newId].value}}</div><div class="richtext-iframe" v-else><div class="content noteContent richtext multieditor" :id="'editor'+item.newId"></div></div></div><div class="image-box" v-if="item.id===3"><div class="images-content" v-if="item.hasData"><ul class="image-lists" v-if="DataBox[item.newId].list"><li v-for="(itemimg, indeximg) in DataBox[item.newId].list" :key="indeximg"><div class="list-cover"><img :src="itemimg" alt=""></div></li></ul><ul class="image-lists" v-else><li><div class="list-cover"><img :src="DataBox[item.newId].value" alt=""></div></li></ul></div><div class="images-content" v-else>空数据</div></div><div class="introduce-box" v-if="item.id===0"><div class="introduce-content">{{DataBox[item.newId].value}}</div></div></div></div></div><div id="formbg" v-if="pageBg != {}"><div class="bg-box" :style="{'background':pageBg.bgColor}"></div></div><div class="dialog setListDialog" v-if="setListDialog"><div class="masking" @click="closeSetDialog"></div><div class="dialog-content"><div class="top clear"><span class="fs16 col666 fl">表单字段</span> <i class="icon icon-del fr" @click="closeSetDialog"></i></div><div class="middle fs14 col333"><div class="item-set-box"><div class="show-lists lists-radio" v-if="radioLists.length>0"><label class="radio-box" v-for="(item, index) in radioLists" :key="index" @change="selectRadio(item)"><input type="radio" :value="item.val" name="formRadio" :checked="item.checked"> <span>{{item.name}}</span></label></div><div class="show-lists lists-checkbox" v-if="checkLists.length>0"><div class="item-engine-title clear txt-l"><span class="title-t"><span class="title-icon bg-theme"></span> <span>选择表单字段</span></span></div><div><label class="radio-box" v-for="(item, index) in checkLists" :key="index" @change="selectCheckbox(item)"><input type="checkbox" :value="item.val" name="formCheckbox" :checked="item.checked" :disabled="item.disabled"> <span>{{item.name}}</span></label></div></div><div class="sort-box" v-if="selectTypeList.length>0"><p class="list-title">排序</p><div class="selected-list-box"><ul class="number-box"><li class="sort-list" v-for="(item, index) in selectTypeList" :key="index"><span class="list-num fl">{{index+1}}</span></li></ul><ul id="formSort" class="list-box"><li class="sort-list" v-for="(item, index) in selectTypeList" :key="index" :data-id="item.id"><div class="list-name fl">{{item.name}}</div><i class="list-icon typehandle icon-move fr"></i></li></ul></div></div></div></div><div class="bottom-btns"><input type="button" value="取消" class="btn btn-fff-auto" @click="closeSetDialog"> <input type="button" value="保存" class="btn btn-blue-auto" @click="formData"></div></div></div><div class="dialog dataSourceDialog" v-if="dataSourceDialog"><div class="dialog-content"><div class="top clear"><span class="fs16 col666 fl">表单数据源选择</span> <i class="icon icon-del fr" @click="closeDataSource"></i></div><div class="middle fs14 col333"><p class="fs14 col333">请先选择页面使用的表单数据源</p><div class="show-lists lists-radio"><label class="radio-box" v-for="(item, index) in dataSourceList" :key="index" @change="selecSourceRadio(item)"><input type="radio" :value="item.val" name="formRadio" :checked="item.checked"> <span>{{item.name}}</span></label></div></div><div class="bottom-btns"><input type="button" value="确认" class="btn btn-blue-auto" @click="closeDataSource"></div></div></div><div class="toast" v-show="showToast"><span v-if="toastType" class="icon" :class="{'icon-failure':toastType =='failure','icon-succeed':toastType=='success'}"></span> <span>{{toastMessage}}</span></div></div><script src="/assets/lib/jquery.min.js"></script><script src="/assets/lib/vue.js"></script><script src="/assets/lib/sortable/Sortable.min.js"></script><script src="/assets/lib/sortable/jquery-sortable.js"></script><script src="/assets/js/common.js"></script><script src="/assets/lib/jQueryUi/jquery.ui.js"></script><script src="/assets/js/form_lists/popNav.js?v=20240717"></script><script src="/assets/lib/spectrum/spectrum.js"></script><script src="/assets/js/select_color.js"></script><script src="https://noteyd.chaoxing.com/res/plugin/ueditor4thirdparty/multieditor.js"></script><script src="/assets/js/left_nav.js?v=20240717"></script><script src="/assets/js/form_lists/editor.js?v=20240717"></script></body></html>