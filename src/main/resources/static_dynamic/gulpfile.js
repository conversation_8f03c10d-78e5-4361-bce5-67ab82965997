var gulp = require('gulp'),
    concat = require('gulp-concat'),//文件合并
    uglify = require('gulp-uglify'),//js压缩
    minifyCss = require('gulp-minify-css'),//css压缩
    rev = require('gulp-rev'),//对文件名加MD5后缀
    clean = require('gulp-clean'),//清理
    revCollector = require('gulp-rev-collector');//路径替换
    auto = require('gulp-autoprefixer');//解决浏览器兼容问题的插件
//自用开发
var sass = require('gulp-sass');
var watch = require('gulp-watch');

// //css处理任务
// gulp.task('mini-css', function () {
//     gulp.src(['./assets/css/**/*.css'])
//         .pipe(minifyCss())
//         // .pipe(rev())
//         .pipe(gulp.dest('./dist01/assets/css'))
//         .pipe(rev.manifest())
//         .pipe(gulp.dest('./rev/css'));
// });
// //js处理任务
// gulp.task('mini-js', function () {
//     gulp.src(['./assets/js/**/*.js'])
//         .pipe(uglify({
//             //mangle: true,//类型：Boolean 默认：true 是否修改变量名
//             mangle: false
//         }).on('error', function(e){
//             console.log(e);
//             return this.end();
//         }))
//         // .pipe(rev())
//         .pipe(gulp.dest('./dist01/assets/js'))
//         .pipe(rev.manifest())
//         .pipe(gulp.dest('./rev/js'));
// });
// //lib处理任务
// gulp.task('lib-js', function () {
//     gulp.src(['./assets/lib/*', './assets/lib/*/**', './assets/lib/*/*/**'])
//         .pipe(gulp.dest('./dist01/assets/lib'));
// });
// // 处理图片
// gulp.task('image', function () {
//     gulp.src(['./assets/images/*', './assets/images/*/**'])
//         .pipe(gulp.dest('./dist01/assets/images'));
// });
// // 处理图片
// gulp.task('iconfont', function () {
//     gulp.src(['./assets/icomoon/*', './assets/icomoon/*/**'])
//         .pipe(gulp.dest('./dist01/assets/icomoon'));
// });
// //路径替换任务
// gulp.task('rev', function () {
//     gulp.src(['./rev/*/*json', './page/**/*.html'])
//         .pipe(revCollector({
//             replaceReved: true,
//         }))
//         .pipe(gulp.dest('./dist01/page'));
// });
// //清理文件
// gulp.task('clean', function () {
//     gulp.src(['./dist01', './rev'], {read: false})
//         .pipe(clean({force: true}));
// });
// // gulp.task('default', ['clean', 'mini-css', 'mini-js','lib-js','image','iconfont', 'rev']);
// gulp.task('default', ['clean', 'mini-css', 'mini-js','lib-js','image','iconfont','rev']);
//自用开发
gulp.task('sasslist', function () {
    return gulp.src('./assets_dy/scss/*.scss')
        .pipe(sass({
            outputStyle: 'expanded' // 输出方式
        }))
        .pipe(auto({//处理兼容
            overrideBrowserslist: [
                "Android 4.1",
                "iOS 7.1",
                "Chrome > 31",
                "ff > 20",
                "ie >= 8",
                'last 10 versions'// 所有主流浏览器最近10版本用
            ]
        }))
        .pipe(gulp.dest('./assets_dy/css/'));
});
gulp.task('watchlist', function () {
    gulp.watch('./assets_dy/scss/*.scss', ['sasslist']);
});
gulp.task('default', ['sasslist', 'watchlist']);
