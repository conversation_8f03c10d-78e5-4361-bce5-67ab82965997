<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <title>门户网站列表页</title>
    <link rel="stylesheet" href="../../../static/assets/icomoon/style.css">
    <link rel="stylesheet" href="../../../static/assets/css/common.css">
    <link rel="stylesheet" href="../../../static/assets/css/website/home.css">
</head>

<body>
    <div class="page-home custom-page">
        <div class="temp-list-box">
            <div class="btns">
                <div class="layui-form-item">
                    <label class="layui-form-label">切换频率：</label>
                    <div class="layui-input-block">
                        <div id="switchSpeed" class="switch-options"></div>
                        <div class="layui-form-mid" style="padding: 9px 0px !important; margin-left: 10px;">秒</div>
                    </div>
                </div>
                <a href="javascript:;" class="ui-btn-save">保存</a>
            </div>
            <ul class="list">
                <li>
                    <div class="img-box">
                        <img src="http://portal.chaoxing.com/upload/portal/website-data/20210402232337834Z.png" alt="">
                    </div>
                    <div class="temp-info">
                        <div class="edit-parents temp-name">
                            <div class="disabled">
                                <span class="need-edit name">默认模板</span>
                            </div>
                        </div>
                        <div class="edit-parents temp-dns">
                            <label class="item-label">访问地址：</label>
                            <div class="disabled">
                                <span class="need-edit dns"><a href="#" target="_blank">http://7576467ge.portal.chaoxing.com</a></span>
                            </div>
                        </div>
                    </div>
                    <div class="right-btns">
                        <span class="js-sort-up icon-arrow-up"></span>
                        <span class="js-sort-down icon-arrow-down"></span>
                        <span class="icon-close-eye"></span>
                        <span class="icon-eye"></span>
                    </div>
                </li>
                <li>
                    <div class="img-box">
                        <img src="http://portal.chaoxing.com/upload/portal/website-data/20210402232403170x.png" alt="">
                    </div>
                    <div class="temp-info">
                        <div class="edit-parents temp-name">
                            <div class="disabled">
                                <span class="need-edit name">默认模板</span>
                            </div>
                        </div>
                        <div class="edit-parents temp-dns">
                            <label class="item-label">访问地址：</label>
                            <div class="disabled">
                                <span class="need-edit dns"><a href="#" target="_blank">http://7576467ge.portal.chaoxing.com</a></span>
                            </div>
                        </div>
                    </div>
                    <div class="right-btns">
                        <span class="js-sort-up icon-arrow-up"></span>
                        <span class="js-sort-down icon-arrow-down"></span>
                    </div>
                </li>
                <li>
                    <div class="img-box">
                        <img src="http://portal.chaoxing.com/upload/portal/website-data/20210402235639365q.png" alt="">
                    </div>
                    <div class="temp-info">
                        <div class="edit-parents temp-name">
                            <div class="disabled">
                                <span class="need-edit name">默认模板</span>
                            </div>
                        </div>
                        <div class="edit-parents temp-dns">
                            <label class="item-label">访问地址：</label>
                            <div class="disabled">
                                <span class="need-edit dns"><a href="#" target="_blank">http://7576467ge.portal.chaoxing.com</a></span>
                            </div>
                        </div>
                    </div>
                    <div class="right-btns">
                        <span class="js-sort-up icon-arrow-up"></span>
                        <span class="js-sort-down icon-arrow-down"></span>
                    </div>
                </li>
                <li>
                    <div class="img-box">
                        <img src="http://portal.chaoxing.com/upload/portal/website-data/20210402225428679M.png" alt="">
                    </div>
                    <div class="temp-info">
                        <div class="edit-parents temp-name">
                            <div class="disabled">
                                <span class="need-edit name">默认模板</span>
                            </div>
                        </div>
                        <div class="edit-parents temp-dns">
                            <label class="item-label">访问地址：</label>
                            <div class="disabled">
                                <span class="need-edit dns"><a href="#" target="_blank">http://7576467ge.portal.chaoxing.com</a></span>
                            </div>
                        </div>
                    </div>
                    <div class="right-btns">
                        <span class="js-sort-up icon-arrow-up"></span>
                        <span class="js-sort-down icon-arrow-down"></span>
                    </div>
                </li>
            </ul>
        </div>
    </div>

    <script src="../../../static/assets/lib/jquery.min.js"></script>

    <!-- layui -->
    <link rel="stylesheet" href="../../../static/assets/lib/layui/css/layui.css">
    <script src="../../../static/assets/lib/layui/layui.js?v=020911"></script>

    <script>
        // $(function () {

        // })

        layui.use('slider', function () {
            var slider = layui.slider;
            slider.render({
                elem: '#switchSpeed',
                input: true,
                min: 3,
                max: 120
            });
        });
    </script>
</body>

</html>
