<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>高级选项</title>
    <link rel="stylesheet" href="../../../static/assets/icomoon/style.css">
    <link rel="stylesheet" href="../../../static/assets/lib/layui/css/layui.css">
    <link rel="stylesheet" href="../../../static/assets/lib/select2/select2.min.css">
    <!--滚动条美化-->
    <link rel="stylesheet" href="../../../static/assets/lib/mCustomScrollBar/jquery.mCustomScrollbar.css">

    <link rel="stylesheet" href="../../../static/assets/css/common.css">
    <link rel="stylesheet" href="../../../static/assets/css/website/advanced-new.css">
</head>
<body>
<div class="header">
    <div class="w-1200 logo-txt">微服务智慧门户</div>
</div>
<div class="">
    <div class="breadcrumb-box">
        <a href="#" class="item">超星大学<i class="icon-up"></i></a>
        <!--<a href="#" class="item"><i class="icon-up"></i>学习支持</a>-->
        <!--<a href="#" class="item prev"><i class="icon-up"></i>体验与创新服务</a>-->
        <a href="#" class="item current"><i class="icon-up"></i>高级选项</a>
    </div>
    <div class="page-content">
        <div class="temp-list">
            <div class="form-box">
                <div class="item">
                    <label class="item-label">网站名称：</label>
                    <input type="text" placeholder="" class="ipt-box">
                </div>
                <div class="item flex-style">
                    <label class="item-label">默认域名：</label>
                    <div class="flex-style fs14 col8A9099">https://<input type="text" class="ipt-box marginLR6" placeholder="" value="67760ie">.portal.chaoxing.com</div>
                </div>
                <div class="item layui-form flex-style">
                    <label class="item-label">启用单位域名：</label>
                    <div class="switch-btn">
                        <div class="layui-form-item">
                            <input type="checkbox" name="zzz" lay-skin="switch" lay-text="开|关">
                        </div>
                    </div>
                </div>
                <div class="item layui-form flex-style url-open">
                    <label class="item-label"></label>
                    <div class="url-bg fs14 col474F59">
                        单位域名<input type="text" class="ipt-box marginL20" placeholder="" value="">
                        <p class="tips">单位域名生效需要挂接，了解<a href="#">挂接流程？</a></p>
                    </div>
                </div>
                <div class="item flex-style">
                    <label class="item-label flex-style">网站网页图标：<div class="tip-div"><i class="btn-tip"></i>
                        <div class="tip-txt"><p>限制后，仅白名单内IP可访问网站</p><img src="../../../static/assets/images/baidu.png">
                            <i class="triangle"></i></div>
                        </div>
                    </label>
<!--                   TODO 注释：上传前样式-->
                    <div class="upload-div">
                        <div class="flex-style">
                            <div class="upload-style"><i class="icon"></i><p>上传图片</p><input type="file" class="file-button"></div>
                            <p class="file-tips">最佳尺寸：16*16 或 32*32，建议大小不超过16KB</p>
                        </div>
                    </div>
                    <!--                   TODO 注释：上传后样式-->
                    <div class="upload-div">
                        <div class="flex-style">
                            <div class="upload-style">
                                <div class="img-box js-crop-img">
                                    <img src="../../../static/assets/images/icon-14-info.png">
                                </div>
                            </div>
                            <div class="file-btn-div flex-style">
                                <span>删除</span>
                                <div class="again">重新上传<input type="file" class="file-button"></div>
                            </div>
                        </div>
                        <p class="txt">效果查看：</p>
                        <div class="result-show">
                            <img src="../../../static/assets/images/icon-14-info.png">
                            <span class="overHidden1">你的网站名</span>
                            <i class="delete-icon"></i>
                        </div>
                    </div>
                </div>

                <button class="btn-save">保存</button>
            </div>
        </div>
    </div>

</div>
<!--弹框：添加管理员-->
<div id="addManagerPop" class="pop add-manager-pop" style="display: none;">
    <div class="pop-content">
        <div class="top clear">
            <span class="fs16 colo666 fl ft-bold">添加管理员</span>
            <i class="icon icon-del fr canncle-edit-name" onclick="hidePop()"></i>
        </div>
        <div class="middle fs14 col333 pop-info clear">
            <div class="search-box fr">
                <input type="text" class="ipt-search" placeholder="搜索">
                <i class="icon-search"></i>
            </div>
            <table class="ui-table" border="1">
                <tr>
                    <th style="width: 10%;">选择</th>
                    <th style="width: 20%;">姓名</th>
                    <th style="width: 35%;">手机号</th>
                    <th style="width: 35%;">学工号</th>
                </tr>
            </table>
            <div class="table-box">
                <table class="ui-table" border="1">
                    <tr>
                        <td style="width: 10%;">
                            <input type="radio" name="manager">
                        </td>
                        <td style="width: 20%;">张三</td>
                        <td style="width: 35%;">17737483328</td>
                        <td style="width: 35%;">234982423849</td>
                    </tr>
                    <tr>
                        <td>
                            <input type="radio" name="manager">
                        </td>
                        <td>张三</td>
                        <td>17737483328</td>
                        <td>234982423849</td>
                    </tr>
                    <tr>
                        <td>
                            <input type="radio" name="manager">
                        </td>
                        <td>张三</td>
                        <td>17737483328</td>
                        <td>234982423849</td>
                    </tr>
                    <tr>
                        <td>
                            <input type="radio" name="manager">
                        </td>
                        <td>张三</td>
                        <td>17737483328</td>
                        <td>234982423849</td>
                    </tr>
                    <tr>
                        <td>
                            <input type="radio" name="manager">
                        </td>
                        <td>张三</td>
                        <td>17737483328</td>
                        <td>234982423849</td>
                    </tr>
                    <tr>
                        <td>
                            <input type="radio" name="manager">
                        </td>
                        <td>张三</td>
                        <td>17737483328</td>
                        <td>234982423849</td>
                    </tr>
                    <tr>
                        <td>
                            <input type="radio" name="manager">
                        </td>
                        <td>张三</td>
                        <td>17737483328</td>
                        <td>234982423849</td>
                    </tr>
                    <tr>
                        <td>
                            <input type="radio" name="manager">
                        </td>
                        <td>张三</td>
                        <td>17737483328</td>
                        <td>234982423849</td>
                    </tr>
                    <tr>
                        <td>
                            <input type="radio" name="manager">
                        </td>
                        <td>张三</td>
                        <td>17737483328</td>
                        <td>234982423849</td>
                    </tr>
                    <tr>
                        <td>
                            <input type="radio" name="manager">
                        </td>
                        <td>张三</td>
                        <td>17737483328</td>
                        <td>234982423849</td>
                    </tr>
                    <tr>
                        <td>
                            <input type="radio" name="manager">
                        </td>
                        <td>张三</td>
                        <td>17737483328</td>
                        <td>234982423849</td>
                    </tr>
                </table>
            </div>
        </div>
        <div class="bottom-btns">
            <input type="button" value="取消" class="btn btn-fff-auto canncle-edit-name" onclick="hidePop()">
            <input type="button" value="下一步" class="btn btn-blue-auto" onclick="showPop('#sureLimitsPop')">
        </div>
    </div>
</div>
<!--门户权限-->
<div id="sureLimitsPop" class="pop sure-limits-pop" style="display: none;">
    <div class="pop-content">
        <div class="top clear">
            <span class="fs16 colo666 fl ft-bold">门户权限</span>
            <i class="icon icon-del fr canncle-edit-name" onclick="hidePop()"></i>
        </div>
        <div class="middle fs14 col333">
            <div class="item">
                <span class="label">管理员：</span>
                <span>张三</span>
            </div>
            <div class="item">
                <span class="label">权限：</span>
                <ul class="limit-list">
                    <li class="all">
                        <input type="checkbox" name="all">
                        <span>全部权限</span>
                    </li>
                    <li>
                        <input type="checkbox" name="item" checked>
                        <span>服务资源</span>
                    </li>
                    <li>
                        <input type="checkbox" name="item" checked>
                        <span>服务资源</span>
                    </li>
                    <li>
                        <input type="checkbox" name="item">
                        <span>服务资源</span>
                    </li>
                    <li>
                        <input type="checkbox" name="item">
                        <span>服务资源</span>
                    </li>
                    <li>
                        <input type="checkbox" name="item" checked>
                        <span>服务资源</span>
                    </li>
                    <li>
                        <input type="checkbox" name="item">
                        <span>服务资源</span>
                    </li>
                </ul>
            </div>


        </div>
        <div class="bottom-btns">
            <input type="button" value="取消" class="btn btn-fff-auto canncle-edit-name" onclick="hidePop()">
            <input type="button" value="确定" class="btn btn-blue-auto">
        </div>
    </div>
</div>


<!--删除弹框-->
<div id="deleteTemp" class="pop delete-temp-pop" style="display: none;">
    <div class="pop-content">
        <div class="top clear">
            <span class="fs16 colo666 fl ft-bold">删除</span>
            <i class="icon icon-del fr canncle-edit-name" onclick="hidePop()"></i>
        </div>
        <div class="middle fs14 col333">
            <img class="icon-delete" src="../../../static/assets/images/website/img-delete.png" alt="">
            <p class="tip">删除后无法恢复，确认删除？</p>
        </div>
        <div class="bottom-btns">
            <input type="button" value="取消" class="btn btn-fff-auto canncle-edit-name" onclick="hidePop()">
            <input type="button" value="确定" class="btn btn-blue-auto">
        </div>
    </div>
</div>

<script src="../../../static/assets/lib/jquery.min.js"></script>
<script src="../../../static/assets/lib/mCustomScrollBar/jquery.mousewheel.min.js"></script>
<script src="../../../static/assets/lib/mCustomScrollBar/jquery.mCustomScrollbar.min.js"></script>
<script src="../../../static/assets/lib/select2/select2.min.js"></script>
<script src="../../../static/assets/lib/layui/layui.js?v=020911" charset="utf-8"></script>
<script src="../../../static/assets/lib/jqthumb.min.js"></script>
<script src="../../../static/assets/js/common.js"></script>
<script>
    $(function () {
        initTabs('body', {
            current: 0,
            nav: $('.page-content .tabs span'),
            navInfo: $('.page-content .temp-list'),
            curClass: 'current'
        })

        $(".js-login-select").select2({
            width: '100%',
            minimumResultsForSearch: -1
        });
        layui.use('form', function () {
            var form = layui.form;
        });

        /*登录方式改变*/
        $('#loginWalSelect').change(function () {
            var val = $(this).val();
            $('.login-way-info').hide();
            $('.login-way-info').eq(val - 1).show();
        })

        loginWayIsReg();
        initManageFun();
        initLimitsCheckBox();
        cropImgFun("body")

        /*美化下拉的滚动条*/
        $(".table-box").mCustomScrollbar({
            theme: "minimal-dark"
        });

    });
    /*编辑管理员权限*/
    function initManageFun() {
        $('body').on('click', '.manager-list .icon-edit', function () {
            showPop('#sureLimitsPop');
        })
        $('body').on('click', '.manager-list .icon-rubbish', function () {
            showPop('#deleteTemp');
        })
    }

    /*权限单选多选*/
    function initLimitsCheckBox() {
        $("body").on('click', '.limit-list input[name=all]', function () {
            if (this.checked) {
                $(".limit-list input[name=item]").prop("checked", true);
            } else {
                $(".limit-list input[name=item]").prop("checked", false);
            }
        });

        $("body").on('click', '.limit-list input[name=item]', function () {
            if (this.checked == false) {
                $(".limit-list input[name=all]:checkbox").prop('checked', false);
            }
            else {
                var count = $(".limit-list input[name=item]:checked").length;
                if (count == $(".limit-list input[name=item]").length) {
                    $("input[name=all]:checkbox").prop("checked", true);
                }
            }
        });
    }

    //   登录方式 是否带注册
    function loginWayIsReg() {
        $('body').on('click', '#iptLoginReg', function () {
            if ($(this).is(':checked')) {
                $(this).parents('.login-way-info').find('.item-reg-address').show();
            } else {
                $(this).parents('.login-way-info').find('.item-reg-address').hide();
            }
        })

    }

    function initTabs(ele, obj) {
        var dom = ele ? ele : 'body';
        var def = {
            current: 0,
            nav: $(dom).find('.eng-tabs span'),
            navInfo: $(dom).find('.eng-tabs-info'),
            curClass: 'current txt-theme'
        };
        var opt = obj ? obj : def;
        $(dom).find(opt.navInfo).eq(opt.current).show();
        opt.nav.on('click', function () {
            var index = $(this).index();
            if (opt.current != index) {
                opt.current = index;
                $(this).siblings().removeClass(opt.curClass);
                $(this).addClass(opt.curClass);
                $(this).parents(dom).find(opt.navInfo).hide();
                $(this).parents(dom).find(opt.navInfo).eq(index).show();
            }
        })
    }
    /*显示弹框*/
    function showPop(div) {
        $('.pop').hide();
        $(div).show();

    }

    /*关闭删除弹框*/
    function hidePop() {
        $('.pop').hide();
    }
</script>
</body>
</html>
