<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title>微服务智慧门户</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="../../../static/assets/icomoon/style.css">
    <link rel="stylesheet" href="../../../static/assets/lib/layui/css/layui.css">
    <link rel="stylesheet" href="../../../static/assets/css/common.css">

    <!-- 自定义图标 -->
    <link rel="stylesheet" href="../../assets_dy/icomoon/style.css" media="all">
</head>

<body>
    <div class="province-rank">
        <div id="mapEChart" style=" height: 100%;"></div>
    </div>
    <!-- 自定义样式 -->
    <link rel="stylesheet" href="../../assets_dy/css/new-style.css">
    <script src="../../../static/assets/lib/jquery.min.js"></script>
    <script src="../../../static/assets/lib/layui/layui.js?v=020911"></script>
    <script src="../../../static/assets/lib/echarts4/echarts.min.js"></script>
    <script src="../../../static/assets/lib/echarts4/china.js"></script>
</body>
<script>
    $(function () {
        // 基于准备好的dom，初始化echarts实例
        var myChart = echarts.init(document.getElementById('mapEChart'));

        // 指定图表的配置项和数据
        var data = [{
                name: '江苏省',
                value: 5.3
            },
            {
                name: '北京市',
                value: 3.8
            },
            {
                name: '上海',
                value: 4.6
            },
            {
                name: '重庆',
                value: 3.6
            },
            {
                name: '河北',
                value: 3.4
            },
            {
                name: '河南',
                value: 3.2
            },
            {
                name: '云南',
                value: 1.6
            },
            {
                name: '辽宁',
                value: 4.3
            },
            {
                name: '黑龙江',
                value: 4.1
            },
            {
                name: '湖南',
                value: 2.4
            },
            {
                name: '安徽',
                value: 3.3
            },
            {
                name: '山东',
                value: 3.0
            },
            {
                name: '新疆',
                value: 1
            },
            {
                name: '江苏',
                value: 3.9
            },
            {
                name: '浙江',
                value: 3.5
            },
            {
                name: '江西',
                value: 2.0
            },
            {
                name: '湖北',
                value: 2.1
            },
            {
                name: '广西',
                value: 3.0
            },
            {
                name: '甘肃',
                value: 1.2
            },
            {
                name: '山西',
                value: 3.2
            },
            {
                name: '内蒙古',
                value: 3.5
            },
            {
                name: '陕西',
                value: 2.5
            },
            {
                name: '吉林',
                value: 4.5
            },
            {
                name: '福建',
                value: 2.8
            },
            {
                name: '贵州',
                value: 1.8
            },
            {
                name: '广东',
                value: 3.7
            },
            {
                name: '青海',
                value: 0.6
            },
            {
                name: '西藏',
                value: 0.4
            },
            {
                name: '四川',
                value: 3.3
            },
            {
                name: '宁夏',
                value: 0.8
            },
            {
                name: '海南',
                value: 1.9
            },
            {
                name: '台湾',
                value: 0.1
            },
            {
                name: '香港',
                value: 0.1
            },
            {
                name: '澳门',
                value: 0.1
            }
        ];

        var yData = [];
        var barData = [];

        for (var i = 0; i < 10; i++) {
            barData.push(data[i]);
            yData.push(i + data[i].name);
        }
        var option = {
            title: [{
                show: true,
                text: '来访省份排名',
                textStyle: {
                    color: '#181E33',
                    fontSize: 18
                },
                right: 320,
                top: 180
            }],
            tooltip: {
                show: true,
                backgroundColor: '#ffffff',
                padding: [12,12],
                textStyle: {
                    color: '#333333',
                },
                extraCssText: 'box-shadow: 0px 6px 16px -8px rgba(0, 0, 0, 0.08), 0px 9px 28px rgba(0, 0, 0, 0.05), 0px 12px 48px 16px rgba(0, 0, 0, 0.03)',
                formatter: function (params) {
                    return params.name + '：' + params.data['value'] + '%'
                },
            },
            visualMap: {
                type: 'piecewise',
                splitNumber: 7,
                inverse: true,
                orient: 'horizontal',
                itemWidth: 16,
                itemHeight: 16,
                text: ['高', '低'],
                showLabel: false,
                seriesIndex: [0],
                min: 0,
                max: 7,
                inRange: {
                    color: ['#ABD7FF', '#9EC6FE', '#8EB0FC', '#79B7FF', '#42A4FF', '#2B77FF', '#0264F8', ]
                },
                textStyle: {
                    color: '#7B93A7'
                },
                left: 300,
                bottom: 160,
                itemGap: 4,
            },
            grid: {
                right: 148,
                top: 220,
                bottom: 200,
                width: '12%'
            },
            xAxis: {
                show: false
            },
            yAxis: {
                type: 'category',
                inverse: true,
                nameGap: 16,
                axisLine: {
                    show: false,
                    lineStyle: {
                        color: '#ddd'
                    }
                },
                axisTick: {
                    show: false,
                    lineStyle: {
                        color: '#ddd'
                    }
                },
                axisLabel: {
                    interval: 0,
                    margin: 85,
                    textStyle: {
                        color: '#455A74',
                        align: 'left',
                        fontSize: 14
                    },
                    rich: {
                        a: {
                            color: '#fff',
                            backgroundColor: '#FFA300',
                            width: 20,
                            height: 20,
                            align: 'center',
                            borderRadius: 4
                        },
                        b: {
                            color: '#fff',
                            backgroundColor: '#529EFF',
                            width: 20,
                            height: 20,
                            align: 'center',
                            borderRadius: 4
                        },
                        c: {
                            color: '#fff',
                            backgroundColor: '#E8916B',
                            width: 20,
                            height: 20,
                            align: 'center',
                            borderRadius: 4
                        },
                        d: {
                            color: '#fff',
                            backgroundColor: '#9FB5D9',
                            width: 20,
                            height: 20,
                            align: 'center',
                            borderRadius: 3
                        }
                    },
                    formatter: function (params) {
                        if (parseInt(params.slice(0, 1)) == 0) {
                            return [
                                '{a|' + (parseInt(params.slice(0, 1)) + 1) + '}' + '  ' + params
                                .slice(1)
                            ].join('\n')
                        } else if (parseInt(params.slice(0, 1)) == 1) {
                            return [
                                '{b|' + (parseInt(params.slice(0, 1)) + 1) + '}' + '  ' + params
                                .slice(1)
                            ].join('\n')
                        } else if (parseInt(params.slice(0, 1)) == 2) {
                            return [
                                '{c|' + (parseInt(params.slice(0, 1)) + 1) + '}' + '  ' + params
                                .slice(1)
                            ].join('\n')
                        } else {
                            return [
                                '{d|' + (parseInt(params.slice(0, 1)) + 1) + '}' + '  ' + params
                                .slice(1)
                            ].join('\n')
                        }
                    }
                },
                data: yData
            },
            geo: {
                // roam: true,
                map: 'china',
                left: 'left',
                right: '300',
                layoutCenter: ['40%', '50%'],
                layoutSize: '100%',
                label: {
                    emphasis: {
                        show: false
                    }
                },
                itemStyle: {
                    emphasis: {
                        areaColor: '#FF8F47'
                    }
                }
            },
            series: [{
                name: 'mapSer',
                type: 'map',
                roam: false,
                geoIndex: 0,
                label: {
                    show: false,
                },
                data: data
            }, {
                name: 'barSer',
                type: 'bar',
                roam: false,
                visualMap: false,
                zlevel: 2,
                barMaxWidth: 8,
                barGap: 0,
                itemStyle: {
                    normal: {
                        color: function (params) {
                            // build a color map as your need.
                            var colorList = [{
                                    colorStops: [{
                                        offset: 0,
                                        color: '#FFA300' // 0% 处的颜色
                                    }, {
                                        offset: 1,
                                        color: '#FF8A00' // 100% 处的颜色
                                    }]
                                },
                                {
                                    colorStops: [{
                                        offset: 0,
                                        color: '#529EFF' // 0% 处的颜色
                                    }, {
                                        offset: 1,
                                        color: '#5282FF' // 100% 处的颜色
                                    }]
                                },
                                {
                                    colorStops: [{
                                        offset: 0,
                                        color: '#E8916B' // 0% 处的颜色
                                    }, {
                                        offset: 1,
                                        color: '#E8826B' // 100% 处的颜色
                                    }]
                                },
                                {
                                    colorStops: [{
                                        offset: 0,
                                        color: '#9FB5D9' // 0% 处的颜色
                                    }, {
                                        offset: 1,
                                        color: '#9FB5D9' // 100% 处的颜色
                                    }]
                                }
                            ];
                            if (params.dataIndex == 0) {
                                return colorList[0]
                            } else if (params.dataIndex == 1) {
                                return colorList[1]
                            } else if (params.dataIndex == 2) {
                                return colorList[2]
                            } else {
                                return colorList[3]
                            }
                        },
                        barBorderRadius: 15
                    }
                },
                data: barData
            }]
        };

        // 使用刚指定的配置项和数据显示图表。
        myChart.setOption(option);
    })
</script>

</html>