<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title>微服务智慧门户</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="../../../static/assets/icomoon/style.css">
    <link rel="stylesheet" href="../../../static/assets/lib/layui/css/layui.css">
    <link rel="stylesheet" href="../../../static/assets/css/common.css">
    <!-- 自定义图标 -->
    <link rel="stylesheet" href="../../assets_dy/icomoon/style.css" media="all">
</head>

<body>
    <div class="my-article visits-pv">
        <div class="echart-top" style="margin-bottom: 20px;">
            <div class="title fl">在线管理员</div>
        </div>
        <table id="myTable" lay-filter="myTable"></table>
        <div class="bottom-div">
            <div id="laypage"></div>
        </div>
    </div>
    <script type="text/html" id="operateButton">
        <div class="font0">
            <a class="custom-btn btn-look" lay-event="look">强制下线</a>
        </div>
    </script>
    <!-- 自定义样式 -->
    <link rel="stylesheet" href="../../assets_dy/css/new-style.css">
    <script src="../../../static/assets/lib/jquery.min.js"></script>
    <script src="../../../static/assets/lib/layui/layui.js?v=020911"></script>
    <script src="../../../static/assets/lib/echarts4/echarts.min.js"></script>
    <script>
        $(function(){
            var data1= [{
                        name:'林萍萍',
                        login_time: "2021-10-04 21:51",
                        login_duration: "1小时2分钟23秒",
                        login_ip:"************"
                }];
            layui.use(['laydate','form'], function () {
                var form = layui.form,
                    laydate = layui.laydate;
                //日期时间选择器
                laydate.render({
                    elem: '#testDate',
                    type: 'datetime',
                    format: 'yyyy-MM-dd HH:mm'
                });
            });
            layui.use(['table', 'laypage'], function () {
                var table = layui.table,
                    laypage = layui.laypage;
                laypage.render({
                    elem: 'laypage',
                    count: 100,
                    layout: ['prev', 'page', 'next'],
                    prev: '<i class="layui-icon layui-icon-left"></i>',
                    next: '<i class="layui-icon layui-icon-right"></i>',
                });
                table.render({
                    elem: '#myTable',
                    cellMinWidth: 100,
                    height: 457,
                    data: [{
                        name:'林萍萍',
                        login_time: "2021-10-04 21:51",
                        login_duration: "1小时2分钟23秒",
                        login_ip:"************"
                    }],
                    cols: [
                        [
                            { field: 'name', title: '姓名',width: 140 ,templet: function(d){
                                return '<span style="color: #4C88FF;cursor: pointer;">'+ d.name +'</span>'
                            }},
                            { field: 'login_time', title: '登录时间',width: 145},
                            { field: 'login_duration', title: '登录时长',width: 140},
                            { field: 'login_ip', title: '登录ip'},
                            { title: '操作', fixed: 'right', width: 82, toolbar: '#operateButton' }
                        ]
                    ]
                })
            })
        })
    </script>
</body>

</html>