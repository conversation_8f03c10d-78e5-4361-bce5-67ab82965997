<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>微服务智慧门户</title>
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <meta name="renderer" content="webkit">
    <link rel="stylesheet" href="../../../static/assets/icomoon/style.css">
    <link rel="stylesheet" href="../../../static/assets/lib/layui/css/layui.css">
    <link rel="stylesheet" href="../../../static/assets/css/common.css">
    <link rel="stylesheet" href="../../assets_dy/css/visit_statistics.css">
</head>
<body>
<div>
    <!--头部-->
    <div class="header">
        <h1>微服务智慧门户</h1>
    </div>
    <!--左侧导航-->
    <div class="left-navs">
        <ul class="navs">
            <li>
                <a href="visit_statistics.html">
                    <i class="icon icon-icon1"></i>
                    <span>网站访问统计</span>
                </a>
            </li>
            <li>
                <a href="engine_visit_statistics.html">
                    <i class="icon icon-icon2"></i>
                    <span>应用数据统计</span>
                </a>
            </li>
            <li  class="active">
                <a href="chapter_visit_top10.html">
                    <i class="icon icon-icon3"></i>
                    <span>文章访问Top10</span>
                </a>
            </li>
            <li>
                <a href="engine_visit_detail.html">
                    <i class="icon icon-icon3"></i>
                    <span>访问明细</span>
                </a>
            </li>
        </ul>
    </div>

    <!--中间内容-->
    <div class="right-content top10">
        <!--面包屑-->
        <ul class="bread-crumb">
            <li><a href="#">超星大学</a> <span class="icon icon-down"></span></li>
            <li><a href="#">我的门户</a><span class="icon icon-down"></span></li>
            <li class="active">访问统计</li>
        </ul>
        <div class="top10-main">
            <div class="content-top">
                <ul class="filters fl">
                    <li class="active">昨日</li>
                    <li>近7日</li>
                    <li>近30日</li>
                    <li>本月</li>
                </ul>
                <div class="layui-form fl">
                    <div class="layui-form-item">
                        <div class="layui-inline">
                            <label class="layui-form-label">时间：</label>
                            <div class="layui-input-inline calendar-input">
                                <span class="icon icon-calendar"></span>
                                <input type="text" class="layui-input visit-date" placeholder="开始时间">
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">至：</label>
                            <div class="layui-input-inline  calendar-input">
                                <span class="icon icon-calendar"></span>
                                <input type="text" class="layui-input  visit-date" placeholder="结束时间">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="table-wrap">
                <table class="layui-hide" id="visitTop" lay-filter="visitTop"></table>
            </div>
        </div>
    </div>
</div>
<script src="../../../static/assets/lib/jquery.min.js"></script>
<script src="../../../static/assets/lib/layui/layui.js?v=020911"></script>
<script>
    $(function () {
        layuiInit();
        filterChange();
    });

    // 表单，日历,初始化
    function layuiInit() {
        layui.use('table', function () {
            var table = layui.table;
            table.render({
                elem: '#visitTop'
                , data: [{
                    name: '防控疫情，只能是依法防控，不能存在“法外之地”',
                    type: '疫情防控',
                    engineName: '图文应用',
                    scanCount: 10
                },{
                    name: '防控疫情，只能是依法防控，不能存在“法外之地”',
                    type: '疫情防控',
                    engineName: '图文应用',
                    scanCount: 10
                }
                ]
                , cols: [[
                    {field: 'name', title: '名称', width: '40%'}
                    , {field: 'type', title: '分类', width: '20%'}
                    , {field: 'engineName', title: '应用', width: '20%'}
                    , {field: 'scanCount', title: '浏览量', width: '20%'}
                ]]
                , page: false
            });

            //监听行单击事件（双击事件为：rowDouble）
            table.on('row(visitTop)', function (obj) {
                var data = obj.data;
                console.log("跳转到详情页面");
            });


            layui.use('laydate', function() {
                var laydate = layui.laydate;
                lay('.visit-date').each(function(){
                    laydate.render({
                        elem: this
                        ,min: '2020-1-1'  // 最大值，最小值
                        ,max: '2020-12-31'
                        ,theme: '#3D82F2'
                        ,trigger: 'click'
                    });
                });
            })
        });
    }
    // 过滤条件的切换
    function filterChange() {
        $(".filters>li").on("click",function () {
            if(!$(this).hasClass("active")){
                $(this).addClass("active").siblings().removeClass("active");
            }
        })
    }
</script>
</body>
</html>