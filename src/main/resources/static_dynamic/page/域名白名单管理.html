<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>域名白名单管理</title>
    <link rel="stylesheet" href="../assets_dy/js/element-ui/index.css">
    <link rel="stylesheet" href="../assets_dy/icomoon/style.css">
    <link rel="stylesheet" href="../assets_dy/css/common-reset.css">
    <link rel="stylesheet" href="../assets_dy/css/white-list-manage.css">
</head>
<body>
<div class="page-white-manage" id="whiteListManage" v-cloak>
    <div class="page-container">
        <div class="top-tab-right fr">
            <i class="icon-tip"></i>门户跳转外链仅支持白名单内的域名
        </div>
        <el-tabs v-model="activeName" @tab-click="handleTopTabClick">
            <el-tab-pane label="白名单" name="first">
                <div class="filtrate-box clearfix">
                    <div class="fl">
                        <el-button type="primary" icon="el-icon-plus" size="small" @click="addPopVisible=true">添加
                        </el-button>
                        <el-button type="primary" plain size="small" @click="uploadPopVisible=true">导入</el-button>
                        <el-button type="primary" plain size="small" @click="deleteConfirm">删除</el-button>
                    </div>
                    <div class="fr">
                        <el-input
                                size="small"
                                class="w-300"
                                placeholder="搜索域名"
                                v-model="value">
                            <i slot="suffix" class="el-input__icon el-icon-search"></i>
                        </el-input>
                    </div>
                </div>
                <div class="table-box">
                    <el-table
                            ref="multipleTable"
                            :data="tableData"
                            tooltip-effect="dark"
                            style="width: 100%"
                            @selection-change="handleSelectionChange">
                        <el-table-column
                                type="selection"
                                width="47">
                        </el-table-column>
                        <el-table-column
                                label="序号"
                                type="index"
                                width="62">
                        </el-table-column>
                        <el-table-column
                                prop="name"
                                label="域名">
                        </el-table-column>
                        <el-table-column
                                prop="status"
                                label="状态">
                        </el-table-column>
                        <el-table-column
                                label="操作"
                                width="250">
                            <template>
                                <el-button type="text">加入白名单</el-button>
                                <el-button type="text">编辑</el-button>
                                <el-button type="text">删除</el-button>

                            </template>
                        </el-table-column>
                    </el-table>
                </div>
                <el-pagination
                        @size-change="handleSizeChange"
                        @current-change="handleCurrentChange"
                        :current-page="currentPage"
                        :page-sizes="[100, 200, 300, 400]"
                        :page-size="100"
                        layout="total, sizes, prev, pager, next, jumper"
                        :total="400">
                </el-pagination>

            </el-tab-pane>
            <el-tab-pane label="拦截记录" name="second">
                样式同“白名单”
            </el-tab-pane>
        </el-tabs>
    </div>
    <!--无权限提示-->
    <div class="error-tip" style="display: none">
        <img src="../assets_dy/images/img-tip.png" alt="">
        <div class="tip">正在访问未授权的外部链接，如有嶷问请联系管理员</div>
    </div>
    <!--弹框：添加-->
    <el-dialog
            center
            title="白名单"
            custom-class="pop-add"
            :visible.sync="addPopVisible"
            width="440px">
        <div class="flex-box">
            <span class="lab">域名</span>
            <el-input v-model="value" size="small" placeholder="请输入不带http的域名，示例：baidu.com"></el-input>
        </div>
        <div slot="footer" class="dialog-footer">
            <el-button type="primary" plain size="small" @click="addPopVisible = false">取 消</el-button>
            <el-button type="primary" size="small" @click="addPopVisible = false">确 定</el-button>
        </div>
    </el-dialog>
    <!--弹框：导入-->
    <el-dialog
            center
            title="导入"
            custom-class="pop-upload"
            :visible.sync="uploadPopVisible"
            width="400px">
        <div class="flex-box">
            <el-upload
                    class="upload-box"
                    drag
                    action="https://jsonplaceholder.typicode.com/posts/"
                    :on-success="handleAvatarSuccess">
                <div v-if="uploadFile == ''">
                    <i class="icon-file icon-upload2"></i>
                    <div class="upload-tip">单击或拖放文件至此处进行上传，<br>
                        支持excel，10M以内。
                    </div>
                    <a href="#" class="btn-download">下载模板</a>
                </div>
                <div v-else>
                    <img class="icon-file" src="../assets_dy/images/img-file.png" alt="">
                    <!--                    <div class="upload-tip">{{uploadFile.name}}</div>-->
                    <a href="#" class="btn-download">重新上传</a>
                </div>

            </el-upload>
        </div>
        <div slot="footer" class="dialog-footer">
            <el-button type="primary" plain size="small" @click="uploadPopVisible = false">取 消</el-button>
            <el-button type="primary" size="small" @click="uploadPopVisible = false">确 定</el-button>
        </div>
    </el-dialog>

</div>

<script src="../assets_dy/js/vendor/jquery.min.js"></script>
<script src="../assets_dy/js/vue.js"></script>
<script src="../assets_dy/js/element-ui/index.js"></script>
<script>
    var vue = new Vue({
        el: '#whiteListManage',
        data: {
            activeName: 'first',
            value: '',
            tableData: [
                {
                    name: '1fkey.com',
                    status: '正常',
                },
                {
                    name: '1fkey.com',
                    status: '禁用',
                },
                {
                    name: '1fkey.com',
                    status: '正常',
                },
                {
                    name: '1fkey.com',
                    status: '已拦截',
                },
                {
                    name: '1fkey.com',
                    status: '已加入白名单',
                },
                {
                    name: '1fkey.com',
                    status: '正常',
                },
            ],
            multipleSelection: [],
            currentPage: 1,
            addPopVisible: false,
            uploadPopVisible: false,
            uploadFile: '',
        },
        mounted: function () {
        },
        methods: {
            handleTopTabClick(tab, event) {
                console.log(tab, event);
            },
            handleSelectionChange(val) {
                this.multipleSelection = val;
            },
            handleSizeChange(val) {
                console.log(`每页 ${val} 条`);
            },
            handleCurrentChange(val) {
                console.log(`当前页: ${val}`);
            },
            handleAvatarSuccess(res, file) {
                this.uploadFile = file;
            },
            //     删除确认弹框
            deleteConfirm() {
                this.$confirm('删除后不可恢复。', '确定删除吗？', {
                    confirmButtonText: '确定',
                    confirmButtonClass: 'bg-red',
                    cancelButtonText: '取消',
                    type: 'danger'
                }).then(() => {
                    this.$message({
                        type: 'success',
                        message: '删除成功!'
                    });
                }).catch(() => {
                    this.$message({
                        type: 'info',
                        message: '已取消删除'
                    });
                });
            }
        }
    })
</script>
</body>
</html>
