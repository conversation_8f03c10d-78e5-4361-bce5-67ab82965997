<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title>模块数据审核</title>
    <link rel="stylesheet" href="../../../static/assets/icomoon/style.css">
    <link rel="stylesheet" href="../../../static/assets/lib/select2/select2.min.css">
    <!--滚动条美化-->
    <link rel="stylesheet" href="../../../static/assets/lib/mCustomScrollBar/jquery.mCustomScrollbar.css">
    <link rel="stylesheet" href="../../../static/assets/css/common.css">
    <link rel="stylesheet" href="../../../static/assets/css/website/advanced.css">

</head>

<body>
<div class="w-1200 tab-check-page">
    <div class="page-content">
        <div class="tabs">
            <span class="current">指派给我</span>
            <span>我已审核</span>
            <span>待审核文章</span>
            <span>我提交的审核</span>
        </div>
        <!-- 指派给我 -->
        <div class="temp-list my-check">
            <div class="content-left fl">
                <form class="layui-form" action="">
                    <div class="custom-search">
                        <div class="layui-inline">
                            <input placeholder="搜索" name="kws" autocomplete="off" value="" class="layui-input">
                        </div>
                        <button type="button" class="layui-btn"><i class="layui-icon layui-icon-search"></i></button>
                    </div>
                    <div class="clear js-review-button">
                        <button type="button" class="layui-btn layui-btn-sm layui-btn-primary">审核通过</button>
                        <button type="button" class="layui-btn layui-btn-sm layui-btn-primary">审核不通过</button>
                    </div>
                    <div class="select-lists">
                        <div class="check-all">
                            <input type="checkbox" lay-skin="primary" id="c_all1" lay-filter="c_all1" title="全选" >
                        </div>
                        <ul class="lists c-child1-lists">
                            <li class="list">
                                <input type="checkbox" lay-skin="primary" class="typelist" lay-filter="c_child1" title="大学迎来首批返校学生">
                            </li>
                            <li class="list">
                                <input type="checkbox" lay-skin="primary" class="typelist" lay-filter="c_child1" title="大学迎来首批返校学生大学迎来首批返校学生大学迎来首批返校学生">
                            </li>
                            <li class="list">
                                <input type="checkbox" lay-skin="primary" class="typelist" lay-filter="c_child1" title="大学迎来首批返校学生">
                            </li>
                            <li class="list">
                                <input type="checkbox" lay-skin="primary" class="typelist" lay-filter="c_child1" title="大学迎来首批返校学生">
                            </li>
                            <li class="list">
                                <input type="checkbox" lay-skin="primary" class="typelist" lay-filter="c_child1" title="大学迎来首批返校学生">
                            </li>
                        </ul>
                    </div>
                </form>
            </div>
            <div class="content-center fl">
                <div class="info fs14 col333">
                    <span>所属页面：首页</span>
                    <span>应用模块：新闻公告</span>
                </div>
                <div class="iframe-content">
                    <iframe src="" frameborder="0">iframe区</iframe>
                </div>
            </div>
            <div class="content-right fl">
                <div class="step-lists">
                    <div class="step-list">
                        <div class="list-top">
                            <div class="list-avatar fl">
                                <img src="../../../static/assets/images/default_head_img.png" alt="">
                            </div>
                            <div class="list-info">
                                <div>
                                    <p class="list-name overHidden1 fl col333">罗辑</p>
                                    <button class="layui-btn list-status">提交审核</button>
                                </div>
                                <p class="list-time col666">2020-12-22 16:42</p>
                            </div>
                        </div>
                        <div class="list-line"></div>
                    </div>
                    <div class="step-list">
                        <div class="list-top">
                            <div class="list-avatar fl">
                                <img src="../../../static/assets/images/default_head_img.png" alt="">
                            </div>
                            <div class="list-info">
                                <div>
                                    <p class="list-name overHidden1 fl col333">罗辑</p>
                                    <button class="layui-btn list-status">审核中</button>
                                </div>
                                <p class="list-time col666">2020-12-22 16:42</p>
                            </div>
                        </div>
                        <div class="list-line"></div>
                    </div>
                    <div class="step-list list-disable">
                        <div class="list-top">
                            <div class="list-avatar fl">
                                <img src="../../../static/assets/images/default_head_img.png" alt="">
                            </div>
                            <div class="list-info">
                                <div>
                                    <p class="list-name overHidden1 fl col333">罗辑名字超长名字超长名字超长名字超长</p>
                                    <button class="layui-btn list-status">提交审核</button>
                                </div>
                                <p class="list-time col333">2020-12-22 16:42</p>
                            </div>
                        </div>
                        <div class="list-line"></div>
                    </div>
                    <div class="step-list">
                        <div class="list-top">
                            <div class="list-avatar fl">
                                <img src="../../../static/assets/images/default_head_img.png" alt="">
                            </div>
                            <div class="list-info">
                                <div>
                                    <p class="list-name overHidden1 fl col333">罗辑</p>
                                    <button class="layui-btn list-status">审核通过</button>
                                </div>
                                <p class="list-time col666">2020-12-22 16:42</p>
                            </div>
                        </div>
                        <div class="list-line">
                            <p class="step-tip col666">这篇排版不错</p>
                        </div>
                    </div>
                    <!-- 多人审核 -->
                    <div class="step-list moreone-step-list">
                        <div class="list-top">
                            <div class="list-avatar fl">
                                <i class="layui-icon layui-icon-group"></i>
                            </div>
                            <div class="list-info">
                                <div>
                                    <p class="list-name overHidden1 fl col333">多人会签</p>
                                    <i class="layui-icon layui-icon-up toggle-more-btn"></i>
                                    <button class="layui-btn list-status">审核中</button>
                                </div>
                            </div>
                        </div>
                        <div class="list-line">
                            <div class="more-list-box">
                                <div class="more-list-info">
                                    <div class="more-list-avatar fl">
                                        <img src="../../../static/assets/images/default_head_img.png" alt="">
                                    </div>
                                    <div>
                                        <p class="more-list-name overHidden1 fl col333">罗辑名字超长名字超长名字超长名字超长</p>
                                        <button class="layui-btn list-status">审核中</button>
                                    </div>
                                    <p class="more-list-time col333">2020-12-22 16:42</p>
                                    <p class="more-step-tip col666">这篇排版不错</p>
                                </div>
                                <div class="more-list-info">
                                    <div class="more-list-avatar fl">
                                        <img src="../../../static/assets/images/default_head_img.png" alt="">
                                    </div>
                                    <div>
                                        <p class="more-list-name overHidden1 fl col333">罗辑名字</p>
                                        <button class="layui-btn list-status">审核中</button>
                                    </div>
                                    <p class="more-list-time col333">2020-12-22 16:42</p>
                                </div>
                                <div class="more-list-info">
                                    <div class="more-list-avatar fl">
                                        <img src="../../../static/assets/images/default_head_img.png" alt="">
                                    </div>
                                    <div>
                                        <p class="more-list-name overHidden1 fl col333">罗辑</p>
                                        <button class="layui-btn list-status">审核中</button>
                                    </div>
                                    <p class="more-list-time col333">2020-12-22 16:42</p>
                                    <p class="more-step-tip col666">这篇排版不错</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="check-input-content">
                    <div class="input-content">
                        <textarea class="input-area" placeholder="审核意见" maxlength="512" onkeyup="count_words(this)"></textarea>
                        <div class="count-num col666 bg-fff">
                            <span class="number">0</span>
                            <span>/512</span>
                        </div>
                    </div>
                    <div class="btns">
                        <button class="layui-btn btn-pass">通过</button>
                        <button class="layui-btn btn-pass-no">不通过</button>
                    </div>
                </div>

            </div>
            <div class="clear"></div>
        </div>
        <div class="temp-list my-checked">
            <div class="content-left fl">
                <form class="layui-form" action="">
                    <div class="custom-search">
                        <div class="layui-inline">
                            <input placeholder="搜索" name="kws" autocomplete="off" value="" class="layui-input">
                        </div>
                        <button type="button" class="layui-btn"><i class="layui-icon layui-icon-search"></i></button>
                    </div>
                    <div class="select-lists">
                        <ul class="lists col666">
                            <li class="list">
                                <p class="">大学迎来首批返校学生</p>
                            </li>
                            <li class="list">
                                <p class="">大学迎来首批返校学生</p>
                            </li>
                            <li class="list">
                                <p class="">大学迎来首批返校学生</p>
                            </li>
                            <li class="list">
                                <p class="">大学迎来首批返校学生</p>
                            </li>
                            <li class="list">
                                <p class="">大学迎来首批返校学生</p>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>
            <div class="content-center fl">
                <div class="info fs14 col333">
                    <span>所属页面：首页</span>
                    <span>应用模块：新闻公告</span>
                    <a href="#" class="iframe-preview">预览</a>
                </div>
                <div class="iframe-content">
                    <iframe src="" frameborder="0">iframe区</iframe>
                    <!-- 通过标志 passed -->
                    <!-- 未通过标志 passed-no -->
                    <div class="checked-status passed-no">
                        <img class="status-pass" src="../../assets_dy/images/check/check-pass.png" alt="">
                        <img class="status-pass-no" src="../../assets_dy/images/check/check-pass-no.png" alt="">
                    </div>
                </div>
            </div>
            <div class="content-right fl">
                <div class="step-lists">
                    <div class="step-list">
                        <div class="list-top">
                            <div class="list-avatar fl">
                                <img src="../../../static/assets/images/default_head_img.png" alt="">
                            </div>
                            <div class="list-info">
                                <div>
                                    <p class="list-name overHidden1 fl col333">罗辑</p>
                                    <button class="layui-btn list-status">提交审核</button>
                                </div>
                                <p class="list-time col666">2020-12-22 16:42</p>
                            </div>
                        </div>
                        <div class="list-line"></div>
                    </div>
                    <div class="step-list">
                        <div class="list-top">
                            <div class="list-avatar fl">
                                <img src="../../../static/assets/images/default_head_img.png" alt="">
                            </div>
                            <div class="list-info">
                                <div>
                                    <p class="list-name overHidden1 fl col333">罗辑</p>
                                    <button class="layui-btn list-status">审核中</button>
                                </div>
                                <p class="list-time col666">2020-12-22 16:42</p>
                            </div>
                        </div>
                        <div class="list-line"></div>
                    </div>
                    <div class="step-list">
                        <div class="list-top">
                            <div class="list-avatar fl">
                                <img src="../../../static/assets/images/default_head_img.png" alt="">
                            </div>
                            <div class="list-info">
                                <div>
                                    <p class="list-name overHidden1 fl col333">罗辑</p>
                                    <button class="layui-btn list-status">审核通过</button>
                                </div>
                                <p class="list-time col666">2020-12-22 16:42</p>
                            </div>
                        </div>
                        <div class="list-line">
                            <p class="step-tip col666">这篇排版不错</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="clear"></div>
        </div>
        <div class="temp-list">
            <div class="content-left fl">
                <form class="layui-form" action="">
                    <div class="custom-search">
                        <div class="layui-inline">
                            <input placeholder="搜索" name="kws" autocomplete="off" value="" class="layui-input">
                        </div>
                        <button type="button" class="layui-btn"><i class="layui-icon layui-icon-search"></i></button>
                    </div>
                    <div class="clear js-review-button">
                        <button type="button" class="layui-btn layui-btn-sm layui-btn-primary">审核通过</button>
                        <button type="button" class="layui-btn layui-btn-sm layui-btn-primary">审核不通过</button>
                    </div>
                    <div class="select-lists">
                        <div class="check-all">
                            <input type="checkbox" lay-skin="primary" id="c_all2" lay-filter="c_all2" title="全选" >
                        </div>
                        <ul class="lists c-child2-lists">
                            <li class="list">
                                <input type="checkbox" lay-skin="primary" class="typelist" lay-filter="c_child2" title="大学迎来首批返校学生">
                            </li>
                            <li class="list">
                                <input type="checkbox" lay-skin="primary" class="typelist" lay-filter="c_child2" title="大学迎来首批返校学生大学迎来首批返校学生大学迎来首批返校学生">
                            </li>
                            <li class="list">
                                <input type="checkbox" lay-skin="primary" class="typelist" lay-filter="c_child2" title="大学迎来首批返校学生">
                            </li>
                            <li class="list">
                                <input type="checkbox" lay-skin="primary" class="typelist" lay-filter="c_child2" title="大学迎来首批返校学生">
                            </li>
                            <li class="list">
                                <input type="checkbox" lay-skin="primary" class="typelist" lay-filter="c_child2" title="大学迎来首批返校学生">
                            </li>
                        </ul>
                    </div>
                </form>
            </div>
            <div class="content-center fl">
                <div class="info fs14 col333">
                    <span>所属页面：首页</span>
                    <span>应用模块：新闻公告</span>
                </div>
                <div class="iframe-content">
                    <iframe src="" frameborder="0">iframe区</iframe>
                </div>
            </div>
            <div class="content-right fl">
                <div class="step-lists">
                    <div class="step-list">
                        <div class="list-top">
                            <div class="list-avatar fl">
                                <img src="../../../static/assets/images/default_head_img.png" alt="">
                            </div>
                            <div class="list-info">
                                <div>
                                    <p class="list-name overHidden1 fl col333">罗辑</p>
                                    <button class="layui-btn list-status">提交审核</button>
                                </div>
                                <p class="list-time col666">2020-12-22 16:42</p>
                            </div>
                        </div>
                        <div class="list-line"></div>
                    </div>
                    <div class="step-list">
                        <div class="list-top">
                            <div class="list-avatar fl">
                                <img src="../../../static/assets/images/default_head_img.png" alt="">
                            </div>
                            <div class="list-info">
                                <div>
                                    <p class="list-name overHidden1 fl col333">罗辑</p>
                                    <button class="layui-btn list-status">审核中</button>
                                </div>
                                <p class="list-time col666">2020-12-22 16:42</p>
                            </div>
                        </div>
                        <div class="list-line"></div>
                    </div>
                    <div class="step-list list-disable">
                        <div class="list-top">
                            <div class="list-avatar fl">
                                <img src="../../../static/assets/images/default_head_img.png" alt="">
                            </div>
                            <div class="list-info">
                                <div>
                                    <p class="list-name overHidden1 fl col333">罗辑名字超长名字超长名字超长名字超长</p>
                                    <button class="layui-btn list-status">提交审核</button>
                                </div>
                                <p class="list-time col333">2020-12-22 16:42</p>
                            </div>
                        </div>
                        <div class="list-line"></div>
                    </div>
                    <div class="step-list">
                        <div class="list-top">
                            <div class="list-avatar fl">
                                <img src="../../../static/assets/images/default_head_img.png" alt="">
                            </div>
                            <div class="list-info">
                                <div>
                                    <p class="list-name overHidden1 fl col333">罗辑</p>
                                    <button class="layui-btn list-status">审核通过</button>
                                </div>
                                <p class="list-time col666">2020-12-22 16:42</p>
                            </div>
                        </div>
                        <div class="list-line">
                            <p class="step-tip col666">这篇排版不错</p>
                        </div>
                    </div>
                </div>
                <div class="check-input-content">
                    <div class="input-content">
                        <textarea class="input-area" placeholder="审核意见" maxlength="512" onkeyup="count_words(this)"></textarea>
                        <div class="count-num col666 bg-fff">
                            <span class="number">0</span>
                            <span>/512</span>
                        </div>
                    </div>
                    <div class="btns">
                        <button class="layui-btn btn-pass">通过</button>
                        <button class="layui-btn btn-pass-no">不通过</button>
                    </div>
                </div>
            </div>
            <div class="clear"></div>
        </div>
        <div class="temp-list my-checked">
            <div class="content-left fl">
                <form class="layui-form" action="">
                    <div class="custom-search">
                        <div class="layui-inline">
                            <input placeholder="搜索" name="kws" autocomplete="off" value="" class="layui-input">
                        </div>
                        <button type="button" class="layui-btn"><i class="layui-icon layui-icon-search"></i></button>
                    </div>
                    <div class="select-lists txt-c">
                        <ul class="lists col666">
                            <li class="list">
                                <p class="">大学迎来首批返校学生</p>
                            </li>
                            <li class="list">
                                <p class="">大学迎来首批返校学生</p>
                            </li>
                            <li class="list">
                                <p class="">大学迎来首批返校学生</p>
                            </li>
                            <li class="list">
                                <p class="">大学迎来首批返校学生</p>
                            </li>
                            <li class="list">
                                <p class="">大学迎来首批返校学生</p>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>
            <div class="content-center fl">
                <div class="info fs14 col333">
                    <span>所属页面：首页</span>
                    <span>应用模块：新闻公告</span>
                </div>
                <div class="iframe-content">
                    <iframe src="http://www.baidu.com" frameborder="0">iframe区</iframe>
                    <!-- 通过标志 passed -->
                    <!-- 未通过标志 passed-no -->
                    <div class="checked-status passed">
                        <img class="status-pass" src="../../assets_dy/images/check/check-pass.png" alt="">
                        <img class="status-pass-no" src="../../assets_dy/images/check/check-pass-no.png" alt="">
                    </div>
                </div>
            </div>
            <div class="content-right fl">
                <div class="step-lists">
                    <div class="step-list">
                        <div class="list-top">
                            <div class="list-avatar fl">
                                <img src="../../../static/assets/images/default_head_img.png" alt="">
                            </div>
                            <div class="list-info">
                                <div>
                                    <p class="list-name overHidden1 fl col333">罗辑</p>
                                    <button class="layui-btn list-status">提交审核</button>
                                </div>
                                <p class="list-time col666">2020-12-22 16:42</p>
                            </div>
                        </div>
                        <div class="list-line"></div>
                    </div>
                    <div class="step-list">
                        <div class="list-top">
                            <div class="list-avatar fl">
                                <img src="../../../static/assets/images/default_head_img.png" alt="">
                            </div>
                            <div class="list-info">
                                <div>
                                    <p class="list-name overHidden1 fl col333">罗辑</p>
                                    <button class="layui-btn list-status">审核中</button>
                                </div>
                                <p class="list-time col666">2020-12-22 16:42</p>
                            </div>
                        </div>
                        <div class="list-line"></div>
                    </div>
                    <div class="step-list">
                        <div class="list-top">
                            <div class="list-avatar fl">
                                <img src="../../../static/assets/images/default_head_img.png" alt="">
                            </div>
                            <div class="list-info">
                                <div>
                                    <p class="list-name overHidden1 fl col333">罗辑</p>
                                    <button class="layui-btn list-status">审核通过</button>
                                </div>
                                <p class="list-time col666">2020-12-22 16:42</p>
                            </div>
                        </div>
                        <div class="list-line">
                            <p class="step-tip col666">这篇排版不错</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="clear"></div>
        </div>
    </div>
</div>

<script src="../../../static/assets/lib/jquery.min.js?v=0312"></script>
<script src="../../../static/assets/lib/mCustomScrollBar/jquery.mousewheel.min.js?v=0312"></script>
<script src="../../../static/assets/lib/mCustomScrollBar/jquery.mCustomScrollbar.min.js?v=0312"></script>
<script src="../../../static/assets/lib/select2/select2.min.js?v=0312"></script>
<script src="../../../static/assets/js/common.js?v=0312"></script>
<script src="../../../static/assets/js/dropzone.min.js"></script>
<script src="../../../static/assets/js/component.fileupload.js"></script>

<link rel="stylesheet" href="../../../static/assets/lib/layui/css/layui.css" media="all">
<script src="../../../static/assets/lib/layui/layui.js?v=020911" charset="utf-8"></script>

<script>
    $(function () {
        initTabs('body', {
            current: 0,
            nav: $('.page-content .tabs span'),
            navInfo: $('.page-content .temp-list'),
            curClass: 'current'
        })
    });

    function initTabs(ele, obj) {
        var dom = ele ? ele : 'body';
        var def = {
            current: 0,
            nav: $(dom).find('.eng-tabs span'),
            navInfo: $(dom).find('.eng-tabs-info'),
            curClass: 'current txt-theme'
        };
        var opt = obj ? obj : def;
        $(dom).find(opt.navInfo).eq(opt.current).show();
        opt.nav.on('click', function () {
            var index = $(this).index();
            if (opt.current != index) {
                opt.current = index;
                $(this).siblings().removeClass(opt.curClass);
                $(this).addClass(opt.curClass);
                $(this).parents(dom).find(opt.navInfo).hide();
                $(this).parents(dom).find(opt.navInfo).eq(index).show();
            }
        })
    }

    layui.use(['form', 'laypage'], function () {
        var form = layui.form,
            laypage = layui.laypage;
//全选
// 我指派的
        form.on('checkbox(c_all1)', function (data) {
            var a = data.elem.checked;
            if (a == true) {
                $(".c-child1-lists .typelist").prop("checked", true);
                form.render('checkbox');
                $('.c-child1-lists .list').addClass('active');
            } else {
                $(".c-child1-lists .typelist").prop("checked", false);
                form.render('checkbox');
                $('.c-child1-lists .list').removeClass('active');
            }
        });
// 待审核文章
        form.on('checkbox(c_all2)', function (data) {
            var a = data.elem.checked;
            if (a == true) {
                $(".c-child2-lists .typelist").prop("checked", true);
                form.render('checkbox');
                $('.c-child2-lists .list').addClass('active');
            } else {
                $(".c-child2-lists .typelist").prop("checked", false);
                form.render('checkbox');
                $('.c-child2-lists .list').removeClass('active');
            }
        });

        //判断全选状态
// 我指派的
        form.on('checkbox(c_child1)', function (data) {
            var a = data.elem.checked,
                _num = $(".c-child1-lists .typelist:checked").length;
            var parent = $(data.elem).parent('.list');
            if (a == false) {
                $("#c_all1").prop("checked", false);
                form.render('checkbox');
                $(parent).removeClass('active');
            } else {
                $(parent).addClass('active');
                var count = 0;
                for (var i = 0; i < $('.c-child1-lists .typelist').length; i++) {
                    if ($('.c-child1-lists .typelist')[i].checked == true) {
                        count++;
                    }
                    if (count == $('.c-child1-lists .typelist').length) {
                        $("#c_all1").prop("checked", true);
                    } else {
                        $("#c_all1").prop("checked", false);
                    }
                }
                form.render('checkbox');
            }
        });
        //判断全选状态
// 待审核文章
        form.on('checkbox(c_child2)', function (data) {
            var a = data.elem.checked,
                _num = $(".c-child2-lists .typelist:checked").length;
            var parent = $(data.elem).parent('.list');
            if (a == false) {
                $("#c_all2").prop("checked", false);
                form.render('checkbox');
                $(parent).removeClass('active');
            } else {
                $(parent).addClass('active');
                var count = 0;
                for (var i = 0; i < $('.c-child2-lists .typelist').length; i++) {
                    if ($('.c-child2-lists .typelist')[i].checked == true) {
                        count++;
                    }
                    if (count == $('.c-child2-lists .typelist').length) {
                        $("#c_all2").prop("checked", true);
                    } else {
                        $("#c_all2").prop("checked", false);
                    }
                }
                form.render('checkbox');
            }
        });
        // 分页
        laypage.render({
            elem: 'laypage',
            count: 100,
            layout: ['count', 'prev', 'page', 'next', 'limit', 'refresh', 'skip'],
            jump: function (obj) {
                console.log(obj)
            }
        });
    });

    /*显示弹框*/
    function showPop(div) {
        $('.pop').hide();
        $(div).show();
    }

    /*关闭删除弹框*/
    function hidePop() {
        $('.pop').hide();
    }
    // 限制字数
    function count_words(obj){
        var count = $(obj).val().trim().length;
        $(obj).next().find('.number').html(count);
    }

    $('.my-checked .select-lists .list').on('click',function () {
        $(this).addClass('active');
        $(this).siblings('.list').removeClass('active');
    })
    //    多人会签展开收起
    $('.toggle-more-btn').on('click', function () {
        var content = $(this).parents('.list-top').siblings('.list-line').find('.more-list-box');
        if($(this).hasClass('layui-icon-up')){
            $(content).slideUp();
            $(this).removeClass('layui-icon-up').addClass('layui-icon-down');
        }else{
            $(content).slideDown();
            $(this).addClass('layui-icon-up').removeClass('layui-icon-down');
        }
    })
</script>
</body>

</html>
