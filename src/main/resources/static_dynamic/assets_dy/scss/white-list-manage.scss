html, body, .page-white-manage {
  height: 100%;
}

.page-container {
  padding: 20px 24px 24px;
  position: relative;

  .top-tab-right {
    position: absolute;
    top: 20px;
    right: 24px;
    color: #8A8B99;
    font-size: 14px;
    line-height: 40px;
    z-index: 2;

    .icon-tip {
      font-size: 14px;
      margin-right: 4px;
      position: relative;
      top: 2px;
    }
  }

  .w-300 {
    width: 300px;
  }

  .el-tabs__header {
    margin-bottom: 16px;

    .el-tabs__item {
      font-size: 16px;
      color: rgba(0, 0, 0, 0.6);
      font-weight: 400;

      &.is-active {
        color: rgba(43, 103, 255, 1);
        font-weight: 600;
      }
    }

    .el-tabs__nav-wrap::after {
      height: 1px;
      background-color: rgba(240, 240, 240, 1);
    }
  }

  .filtrate-box {
    .el-button {
      font-size: 13px;
      padding: 0 17px;
      height: 32px;
      min-width: 80px;
      box-sizing: border-box;
    }
  }

  .table-box {
    margin-top: 16px;

    .el-table th.el-table__cell {
      background: #F5F5F5;

      & > .cell {
        color: rgba(0, 0, 0, 0.6);
        font-size: 14px;
        font-weight: 600;
      }
    }

    td .cell {
      color: #333;
    }
  }

  .el-pagination {
    margin-top: 24px;
    text-align: center;

    .el-pagination__total {
      color: rgba(0, 0, 0, 0.4);
    }

    .el-pager li.number {
      padding: 0 6px;
      background: #F5F5F5;
      border-radius: 2px;
      margin: 0 5px;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.8);
      font-weight: 400;

      &.active {
        background: #3D82F2;
        color: #fff;
      }
    }

    .el-input__inner {
      border-color: #E9E9E9;
      border-radius: 4px;
      color: rgba(0, 0, 0, 0.6);
    }
  }
}

.error-tip {
  text-align: center;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);

  & > img {
    width: 406px;
  }

  .tip {
    font-size: 18px;
    color: rgba(0, 0, 0, 0.8);
    font-weight: 600;
  }

}

.pop-add {
  .el-dialog__body {
    padding: 24px;
  }

  .flex-box {
    display: flex;
    align-items: center;

    .lab {
      display: inline-block;
      width: 60px;
      margin-right: 12px;
    }

    .el-input {
      flex: 1;
    }
  }
}

.pop-upload {
  .el-dialog__body {
    padding: 16px;

    .icon-file {
      display: inline-block;
      margin-bottom: 8px;
      font-size: 40px;
      width: 40px;
      text-align: center;
      color: rgba(0, 0, 0, 0.4);
    }

    .upload-tip {
      font-size: 13px;
      color: rgba(0, 0, 0, 0.4);
      line-height: 18px;
    }

    .btn-download {
      display: block;
      font-size: 13px;
      color: #2B67FF;
      cursor: pointer;
      margin-top: 8px;
    }

    .el-upload-dragger {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
    }
  }
}
