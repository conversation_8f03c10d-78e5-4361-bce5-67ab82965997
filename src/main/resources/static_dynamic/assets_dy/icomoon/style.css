@font-face {
  font-family: 'icomoon';
  src:  url('fonts/icomoon.eot?6vzok9');
  src:  url('fonts/icomoon.eot?6vzok9#iefix') format('embedded-opentype'),
    url('fonts/icomoon.ttf?6vzok9') format('truetype'),
    url('fonts/icomoon.woff?6vzok9') format('woff'),
    url('fonts/icomoon.svg?6vzok9#icomoon') format('svg');
  font-weight: normal;
  font-style: normal;
  font-display: block;
}

[class^="icon-"], [class*=" icon-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'icomoon' !important;
  speak: never;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-upload2:before {
  content: "\e918";
}
.icon-tip:before {
  content: "\e917";
}
.icon-wzk-text:before {
  content: "\e916";
}
.icon-ct-work:before {
  content: "\e90e";
}
.icon-ct-list:before {
  content: "\e90f";
}
.icon-ct-manage:before {
  content: "\e910";
}
.icon-ct-examine:before {
  content: "\e911";
}
.icon-ct-website:before {
  content: "\e912";
}
.icon-ct-design:before {
  content: "\e913";
}
.icon-ct-safe:before {
  content: "\e914";
}
.icon-icon-ct-custom:before {
  content: "\e915";
}
.icon-ct-close:before {
  content: "\e90b";
}
.icon-ct-add:before {
  content: "\e90c";
}
.icon-ct-drag:before {
  content: "\e90d";
}
.icon-ct-arrow:before {
  content: "\e900";
}
.icon-ct-statistics:before {
  content: "\e901";
}
.icon-ct-refresh:before {
  content: "\e902";
}
.icon-ct-menu:before {
  content: "\e903";
}
.icon-ct-module:before {
  content: "\e904";
}
.icon-ct-export:before {
  content: "\e905";
}
.icon-ct-setting:before {
  content: "\e906";
}
.icon-ct-audit:before {
  content: "\e907";
}
.icon-ct-custom:before {
  content: "\e908";
}
.icon-ct-search:before {
  content: "\e909";
}
.icon-ct-calendar:before {
  content: "\e90a";
}
