;(function ($) {
    //动画过度
    //使用
    /* $("#imgSrc").animate({
            width: "90%",
            height: "100%",
            fontSize: "10em",
            borderWidth: 10
        }, "slow", "easeInSine", function () {
            alert("移动了吗 ，哈哈");
        });

        */
    $.extend($.easing, {
        easeInSine: function (x, t, b, c, d) {
            return -c * Math.cos(t / d * (Math.PI / 2)) + c + b;
        }
    });
    $.fn.slider = function (settings) {

        settings = $.extend({}, $.fn.slider.defaults, settings);
        this.each(function () {
            var scrollobj = settings.movebox,//移动目标
                LeftBtn = settings.LeftBtn, //左按钮
                rightBtn = settings.rightBtn,//右按钮
                childW = $(settings.movechild).outerWidth(), //单元素的宽度
                showW = $(settings.moveContent).outerWidth(), // 一屏可以展示的宽
                allW = $(settings.movechild).outerWidth() * $(settings.movechild).length,//总li的宽

                movenum = settings.movenum || $(settings.moveContent).outerWidth() / $(settings.movechild).outerWidth(),//移动个数 否则按一屏的显示个数
                moveW = settings.movenum * $(settings.movechild).outerWidth() || $(settings.moveContent).outerWidth(), //移动距离

                maxL = allW - $(settings.moveContent).outerWidth();

            var hasMove=false;  //全局标识，初始化标识元素没有发生mousemove
            var timerUp = null;
            var timerL = null;
            //右箭头
            $(rightBtn).on("click", function () {
                console.log(moveW);
                $.fn.slider.defaults.initParam(settings);
                var allW = $(settings.movechild).outerWidth() * $(settings.movechild).length;//总li的宽
                var maxL = allW - $(settings.moveContent).outerWidth();
                var Le = parseInt($(settings.movebox).css("left"));
                var w = allW - Math.abs(Le) - moveW;
                var LL = $(settings.moveContent).outerWidth() - moveW;

                if (Math.abs(Le) < maxL) {
                    if (w > moveW) {
                        $(settings.movebox).css({
                            left: "-=" + moveW
                        })
                    } else {
                        $(settings.movebox).css({
                            left: -w + Le + LL
                        })
                    }
                }

            });
            //左箭头
            $(LeftBtn).on("click", function () {
                $.fn.slider.defaults.initParam(settings);
                var Le = parseInt($(settings.movebox).css("left"));
                if (Le < -moveW) {
                    $(settings.movebox).css({
                        left: "+=" + moveW
                    })

                } else {
                    $(settings.movebox).css({
                        left: 0
                    })
                }

            })


            $(rightBtn).on("mousedown",function(){

                hasMove=false;
                $.fn.slider.defaults.initParam(settings);
                var allW = $(settings.movechild).outerWidth() * $(settings.movechild).length;//总li的宽
                var maxL = allW - $(settings.moveContent).outerWidth();


                timerUp = setInterval( function () {
                    var Le = parseInt($(settings.movebox).css("left"));
                    var w = allW - Math.abs(Le) - moveW;
                    var LL = $(settings.moveContent).outerWidth() - moveW;
                    console.log(Le,maxL,LL);
                    if (Math.abs(Le) < maxL) {
                        if((maxL-Math.abs(Le))<childW){
                            $(settings.movebox).css({
                                left: -maxL
                            })
                        }else{
                            $(settings.movebox).css({
                                left: "-=" + childW
                            })
                        }
                    }

                } ,100)


            })
            $(rightBtn).on("mouseup",function(){
                clearInterval(timerUp);
                hasMove=true;
                // var allW = $(settings.movechild).outerWidth() * $(settings.movechild).length;//总li的宽
                // var maxL = allW - $(settings.moveContent).outerWidth();
                // var Le = parseInt($(settings.movebox).css("left"));
                // if((maxL-Le)%childW>0){
                //     $(settings.movebox).css({
                //         left: Math.ceil(Le+showW/childW)*childW
                //     })
                // }
            })
            $(rightBtn).mouseout(function() {
                clearInterval(timerUp);
                hasMove=true;
                // var allW = $(settings.movechild).outerWidth() * $(settings.movechild).length;//总li的宽
                // var maxL = allW - $(settings.moveContent).outerWidth();
                // var Le = parseInt($(settings.movebox).css("left"));
                // if((maxL-Le)%childW>0){
                //     $(settings.movebox).css({
                //         left: -Le+Math.ceil(Le/childW)*childW
                //     })
                // }
            });
            $(LeftBtn).on("mousedown",function(){

                hasMove=false;
                $.fn.slider.defaults.initParam(settings);
                var allW = $(settings.movechild).outerWidth() * $(settings.movechild).length;//总li的宽
                var maxL = allW - $(settings.moveContent).outerWidth();


                timerL = setInterval( function () {
                    var Le = parseInt($(settings.movebox).css("left"));
                    var w = allW - Math.abs(Le) - moveW;
                    var LL = $(settings.moveContent).outerWidth() - moveW;
                    console.log(Le,maxL,LL);
                    if(Math.abs(Le)<childW){
                        $(settings.movebox).css({
                            left: 0
                        })
                    }else{
                        $(settings.movebox).css({
                            left: "+=" + childW
                        })
                    }

                } ,100)


            })
            $(LeftBtn).on("mouseup",function(){
                clearInterval(timerL);
                hasMove=true;
                // var allW = $(settings.movechild).outerWidth() * $(settings.movechild).length;//总li的宽
                // var maxL = allW - $(settings.moveContent).outerWidth();
                // var Le = parseInt($(settings.movebox).css("left"));
                // if(Le%childW>0){
                // //if(Le%childW>allW%showW){
                //     $(settings.movebox).css({
                //         left: -Le-Math.ceil(Le/childW)*childW
                //     })
                //
                // }

            })
            $(LeftBtn).mouseout(function() {
                clearInterval(timerL);
                hasMove=true;
                // var allW = $(settings.movechild).outerWidth() * $(settings.movechild).length;//总li的宽
                // var maxL = allW - $(settings.moveContent).outerWidth();
                // var Le = parseInt($(settings.movebox).css("left"));
                // if(Le%childW>0){
                //     //if(Le%childW>allW%showW){
                //     $(settings.movebox).css({
                //         left: -Le-Math.ceil(Le/childW)*childW
                //     })
                //
                // }
            });

        })
    }

})(jQuery);

//设置默认
$.fn.slider.defaults = {
    moveContent: ".w_tabbox",
    movechild: ".w_tabbox li",
    movebox: ".w_tabul",
    LeftBtn: ".left",
    rightBtn: ".right",
    movenum: 4,
    callfn: null,
    initParam: function (settings) {
        // 一屏可以展示的宽
        showW = $(settings.moveContent).outerWidth();
        //总li的宽
        allW = $(settings.movechild).outerWidth() * $(settings.movechild).length;
        //移动个数 否则按一屏的显示个数
        movenum = settings.movenum || $(settings.moveContent).outerWidth() / $(settings.movechild).outerWidth();
        //移动距离
        moveW = settings.movenum * $(settings.movechild).outerWidth() || $(settings.moveContent).outerWidth();
        //最大移动
        maxL = allW - $(settings.moveContent).outerWidth();
    }


}