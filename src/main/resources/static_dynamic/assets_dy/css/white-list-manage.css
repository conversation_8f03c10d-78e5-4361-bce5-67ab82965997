html, body, .page-white-manage {
  height: 100%;
}

.page-container {
  padding: 20px 24px 24px;
  position: relative;
}

.page-container .top-tab-right {
  position: absolute;
  top: 20px;
  right: 24px;
  color: #8A8B99;
  font-size: 14px;
  line-height: 40px;
  z-index: 2;
}

.page-container .top-tab-right .icon-tip {
  font-size: 14px;
  margin-right: 4px;
  position: relative;
  top: 2px;
}

.page-container .w-300 {
  width: 300px;
}

.page-container .el-tabs__header {
  margin-bottom: 16px;
}

.page-container .el-tabs__header .el-tabs__item {
  font-size: 16px;
  color: rgba(0, 0, 0, 0.6);
  font-weight: 400;
}

.page-container .el-tabs__header .el-tabs__item.is-active {
  color: #2b67ff;
  font-weight: 600;
}

.page-container .el-tabs__header .el-tabs__nav-wrap::after {
  height: 1px;
  background-color: #f0f0f0;
}

.page-container .filtrate-box .el-button {
  font-size: 13px;
  padding: 0 17px;
  height: 32px;
  min-width: 80px;
  -webkit-box-sizing: border-box;
     -moz-box-sizing: border-box;
          box-sizing: border-box;
}

.page-container .table-box {
  margin-top: 16px;
}

.page-container .table-box .el-table th.el-table__cell {
  background: #F5F5F5;
}

.page-container .table-box .el-table th.el-table__cell > .cell {
  color: rgba(0, 0, 0, 0.6);
  font-size: 14px;
  font-weight: 600;
}

.page-container .table-box td .cell {
  color: #333;
}

.page-container .el-pagination {
  margin-top: 24px;
  text-align: center;
}

.page-container .el-pagination .el-pagination__total {
  color: rgba(0, 0, 0, 0.4);
}

.page-container .el-pagination .el-pager li.number {
  padding: 0 6px;
  background: #F5F5F5;
  border-radius: 2px;
  margin: 0 5px;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.8);
  font-weight: 400;
}

.page-container .el-pagination .el-pager li.number.active {
  background: #3D82F2;
  color: #fff;
}

.page-container .el-pagination .el-input__inner {
  border-color: #E9E9E9;
  border-radius: 4px;
  color: rgba(0, 0, 0, 0.6);
}

.error-tip {
  text-align: center;
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
      -ms-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
}

.error-tip > img {
  width: 406px;
}

.error-tip .tip {
  font-size: 18px;
  color: rgba(0, 0, 0, 0.8);
  font-weight: 600;
}

.pop-add .el-dialog__body {
  padding: 24px;
}

.pop-add .flex-box {
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
     -moz-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.pop-add .flex-box .lab {
  display: inline-block;
  width: 60px;
  margin-right: 12px;
}

.pop-add .flex-box .el-input {
  -webkit-box-flex: 1;
  -webkit-flex: 1;
     -moz-box-flex: 1;
      -ms-flex: 1;
          flex: 1;
}

.pop-upload .el-dialog__body {
  padding: 16px;
}

.pop-upload .el-dialog__body .icon-file {
  display: inline-block;
  margin-bottom: 8px;
  font-size: 40px;
  width: 40px;
  text-align: center;
  color: rgba(0, 0, 0, 0.4);
}

.pop-upload .el-dialog__body .upload-tip {
  font-size: 13px;
  color: rgba(0, 0, 0, 0.4);
  line-height: 18px;
}

.pop-upload .el-dialog__body .btn-download {
  display: block;
  font-size: 13px;
  color: #2B67FF;
  cursor: pointer;
  margin-top: 8px;
}

.pop-upload .el-dialog__body .el-upload-dragger {
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
     -moz-box-orient: vertical;
     -moz-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-align: center;
  -webkit-align-items: center;
     -moz-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
     -moz-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}
