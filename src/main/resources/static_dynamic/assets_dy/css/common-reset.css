/* CSS Document */
* {
  margin: 0;
  padding: 0;
}

body {
  font-family: "SF Pro Display", "Helvetica Neue", Helvetica, Tahoma, Arial, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
  -webkit-font-smoothing: antialiased;
}

body {
  letter-spacing: 0.04em;
}

@-moz-document url-prefix() {
  body {
    letter-spacing: 0;
  }
}

body:not(.article-content) table {
  width: 100%;
  border: 0;
  border-collapse: collapse;
  border-spacing: 0;
}

body:not(.article-content) table th {
  font-weight: normal;
}

body:not(.article-content) .noteContent table {
  width: auto;
  word-break: break-all;
}

ul,
ol,
li {
  list-style-type: none;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-weight: normal;
}

input,
select,
textarea {
  vertical-align: middle;
  color: #000;
  border: none;
  border: 0;
  outline: none;
  resize: none;
}

body a {
  color: inherit;
  text-decoration: none;
}

b,
i,
strong,
em {
  font-weight: normal;
  font-style: normal;
}

img {
  border: 0;
}

.col333 {
  color: #333333;
}

.col666 {
  color: #666666;
}

.col999 {
  color: #999999;
}

.colccc {
  color: #ccc;
}

.fr {
  float: right;
}

.fl {
  float: left;
}

.clearfix:after {
  content: "";
  display: block;
  clear: both;
}

.hide {
  display: none;
}

.overflowHide {
  overflow: hidden;
}

.overHidden1 {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.overHidden2 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.overHidden3 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}

.tl {
  text-align: left;
}

.tc {
  text-align: center;
}

.txt-r {
  text-align: right;
}

/*element-ui reset ----start */
.el-button--primary.is-plain {
  background: #fff;
  font-size: 13px;
  border-color: #2B67FF;
}

.el-button--small {
  border-radius: 4px;
}

.el-dialog {
  border-radius: 8px;
  -webkit-box-shadow: 0px 6px 24px 0px rgba(31, 35, 41, 0.1);
          box-shadow: 0px 6px 24px 0px rgba(31, 35, 41, 0.1);
}

.el-dialog .el-dialog__header {
  text-align: left;
  padding: 14px 54px 14px 24px;
  font-size: 16px;
  color: rgba(0, 0, 0, 0.8);
  font-weight: 600;
  border-bottom: 1px solid #E9E9E9;
}

.el-dialog .el-dialog__footer {
  padding: 16px;
  border-top: 1px solid #E9E9E9;
}

.el-dialog .el-dialog__footer .el-button + .el-button, .el-dialog .el-dialog__footer .el-checkbox.is-bordered + .el-checkbox.is-bordered {
  margin-left: 24px;
}

.el-message-box {
  border-radius: 8px;
  -webkit-box-shadow: 0px 6px 24px 0px rgba(31, 35, 41, 0.1);
          box-shadow: 0px 6px 24px 0px rgba(31, 35, 41, 0.1);
  padding: 0;
  border: none;
}

.el-message-box .el-message-box__header {
  padding: 24px 60px 24px 24px;
  font-size: 16px;
  color: rgba(0, 0, 0, 0.8);
  font-weight: 600;
}

.el-message-box .el-message-box__content {
  padding: 0 24px 32px;
}

.el-message-box .el-message-box__btns {
  padding: 0 24px 24px;
}

.el-message-box .el-message-box__btns .el-button {
  width: 80px;
}

.el-message-box .el-message-box__btns .el-button.bg-red {
  background: #f53f3f;
  border: none;
  height: 32px;
}

.el-message-box .el-message-box__btns .el-button + .el-button, .el-message-box .el-message-box__btns .el-checkbox.is-bordered + .el-checkbox.is-bordered {
  margin-left: 16px;
}

/*element-ui reset ----end */
