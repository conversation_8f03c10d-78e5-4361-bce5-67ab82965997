<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="keywords" content="#(idxTitle)"/>
    <meta name="description" content="#(intro)"/>
    <meta name="renderer" content="webkit">
    #if(plugs != null && plugs.size() > 3)
    #for(plug : plugs[3])
    #for(html : plug.url)
    #(html)
    #end
    #end
    #end
    <meta name="force-rendering" content="webkit"/>
    <meta http-equiv="X-UA-Compatible" content="IE=Edge,chrome=1"/>
    <meta name="viewport"
          content="width=device-width,initial-scale=1.0,minimum-scale=1.0,maximum-scale=1.0,user-scalable=0">
    <title>#(idxTitle)</title>

    #if(plugs != null)
        #for(plug : plugs[1])
            #if(plug.cssHref !=null)
            <link rel="stylesheet" href="#(plug.cssHref)">
            #end
        #end
    #end

    #if(plugs != null)
        #for(plug : plugs[2])
            #if(plug.cssHref !=null)
            <link rel="stylesheet" href="#(plug.cssHref)">
            #end
        #end
    #end
</head>
<body class="grid-bottom-spacer">
<div id="htContainer" style="height:100%" >
</div>

#if(plugs != null)
    #for(plug : plugs[0])
        #for(url : plug.url)
        <script src="#(url)"></script>
        #end
    #end
#end
<script src="#(portal)assets/lib/jquery.min.js"></script>
<script src="#(portal)assets/lib/layui/layui.js?v=020911"></script>
<script src="#(portal)assets/lib/vue.js"></script>

#if(plugs != null)
    #for(plug : plugs[1])
        #for(url : plug.url)
        <script src="#(url)"></script>
        #end
    #end
#end

<script type="text/javascript">
    var pageId = '#(pageId)';
    var websiteId = '#(websiteId)';
    var jsonId = '#(jsonId)';
    var leftNavId = '#(leftNavId)';
    var fromViewIndex = true;
    var p_wfwfid = '#(wfwfid)';
    var p_tipsData = '#(tips)';
    var p_webjson = JSON.stringify(#(webjson));
    var isLogin = '#(isLogin)';
    var isBeginnerGuide = '#(isBeginnerGuide)';
    var sfid = '#(sfid)';
    var pagePubId = '#(pagePubId)';
    var sversion = '#(version)';
    var globalType = '#(globalType)';
    var wxMiniUrl = '#(wxMiniUrl)';

    $(function(){
        if (isLogin === false &&  isBeginnerGuide == 1){
            updateLocalStorage();
        }
    });
    function updateLocalStorage() {
        var count = window.localStorage.getItem("visitCount");
        if (count === null) {
            window.localStorage.setItem("visitCount", "1");
        }else{
            var number = Number(count) + 1;
            window.localStorage.setItem("visitCount", number.toString());
        }
    }
    $("#htContainer").load('#(preview)');
</script>
#switch (globalType)
#case ('')
#case ('zh_CN')
#case ('en_US')
<script src="#(portal)assets/language/en.js"></script>
#case ('fr')
<script src="#(portal)assets/language/fr.js"></script>
#case ('lo')
<script src="#(portal)assets/language/lo.js"></script>
#case ('tha')
<script src="#(portal)assets/language/tha.js"></script>
#case ('zh_TW')
<script src="#(portal)assets/language/ch_hant.js"></script>
#default
#end

#if(plugs != null)
    #for(plug : plugs[2])
        #for(url : plug.url)
        <script src="#(url)"></script>
        #end
    #end
#end

#if(plugsHtml != null && plugsHtml.size() > 0)
    #for(plug : plugsHtml)
    <div>#(plug)</div>
    #end
#end
</body>
</html>
