/*
 * ............................................. 
 * 
 * 				    _ooOoo_ 
 * 		  	       o8888888o 
 * 	  	  	       88" . "88 
 *                 (| -_- |) 
 *                  O\ = /O 
 *              ____/`---*\____ 
 *               . * \\| |// `. 
 *             / \\||| : |||// \ 
 *           / _||||| -:- |||||- \ 
 *             | | \\\ - /// | | 
 *            | \_| **\---/** | | 
 *           \  .-\__ `-` ___/-. / 
 *            ___`. .* /--.--\ `. . __ 
 *        ."" *< `.___\_<|>_/___.* >*"". 
 *      | | : `- \`.;`\ _ /`;.`/ - ` : | | 
 *         \ \ `-. \_ __\ /__ _/ .-` / / 
 *======`-.____`-.___\_____/___.-`____.-*====== 
 * 
 * ............................................. 
 *              佛祖保佑 永无BUG 
 *
 * 佛曰: 
 * 写字楼里写字间，写字间里程序员； 
 * 程序人员写程序，又拿程序换酒钱。 
 * 酒醒只在网上坐，酒醉还来网下眠； 
 * 酒醉酒醒日复日，网上网下年复年。 
 * 但愿老死电脑间，不愿鞠躬老板前； 
 * 奔驰宝马贵者趣，公交自行程序员。 
 * 别人笑我忒疯癫，我笑自己命太贱； 
 * 不见满街漂亮妹，哪个归得程序员？
 *
 * 北纬30.√  <EMAIL>
 */
package com.github.model;

import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;

public class Page {
	
	//alias
	public static final String TABLE_ALIAS = "Page";
	public static final String ALIAS_ID = "id";
	public static final String ALIAS_WEBSITE_ID = "网站id";
	public static final String ALIAS_NAME = "页面名称";
	public static final String ALIAS_COVER = "页面封面";
	public static final String ALIAS_WEB_JSON_ID = "web_json";
	public static final String ALIAS_CONTENT = "静态html内容";
	public static final String ALIAS_CREATE_TIME = "创建时间";
	
	
	//columns START
	/** id   db_column: id */ 	
	private Integer id;
	private String publicId;

	/** 网站id   db_column: website_id */ 	
	private Integer websiteId;
	/** 页面名称   db_column: name */ 	
	private String name;
	/** 页面封面   db_column: cover */ 	
	private String cover;
	/** web_json   db_column: web_json_id */ 	
	private Integer webJsonId;
	/** 静态html内容   db_column: content */ 	
	private String content;
	/** 创建时间   db_column: create_time */ 	
	private java.util.Date createTime;
	//columns END
	private String template;
	/** 审核类型 0关闭审核 1不指定流程审核 2指定流程审核 */
	private Integer auditType;
	/** 2.0 pc 预览地址 */
	private String url;
	/** 2.0 app 预览地址 */
	private String appUrl;
	/** 是否需要登录后访问，网站的登录控制整个网站，这里控制当前网页 */
	private Integer login;

	//	额外字段
	private String encId;
	private String encWebsiteId;
	private String encWebJsonId;
	// 页面类型，目前用于判断是否只有移动端，pc时需要去iframe页面 1->是 0->否
	private Integer at;

	/** 审核类型 0关闭 1开启审核（无流程） 2开启审核（指定流程） */
	public static final Integer AUDIT_TYPE_OFF = 0;
	public static final Integer AUDIT_TYPE_NO_PROCESS = 1;
	public static final Integer AUDIT_TYPE_APPOINT_PROCESS = 2;

	/** 审核状态。0：未通过、1：已通过、2：待提交审核、3：审核中 */
	public static final Integer AUDIT_STATUS_NO_PASS = 0;
	public static final Integer AUDIT_STATUS_PASSED = 1;
	public static final Integer AUDIT_STATUS_WAIT_SUBMIT = 2;
	public static final Integer AUDIT_STATUS_IN_AUDIT = 3;

	public Page(){
	}

	public Page(
		Integer id
	){
		this.id = id;
	}

	public Page(Integer id,String name){
		this.id = id;
		this.name = name;
	}

	public Integer getAt() {
		return at;
	}

	public void setAt(Integer at) {
		this.at = at;
	}

	public String getPublicId() {
		return publicId;
	}

	public void setPublicId(String publicId) {
		this.publicId = publicId;
	}

	public void setId(Integer value) {
		this.id = value;
	}
	public Integer getId() {
		return this.id;
	}
	public void setWebsiteId(Integer value) {
		this.websiteId = value;
	}
	public Integer getWebsiteId() {
		return this.websiteId;
	}
	public void setName(String value) {
		this.name = value;
	}
	public String getName() {
		return this.name;
	}
	public void setCover(String value) {
		this.cover = value;
	}
	public String getCover() {
		return this.cover;
	}
	public void setWebJsonId(Integer value) {
		this.webJsonId = value;
	}
	public Integer getWebJsonId() {
		return this.webJsonId;
	}
	public void setContent(String value) {
		this.content = value;
	}
	public String getContent() {
		return this.content;
	}
	public void setCreateTime(java.util.Date value) {
		this.createTime = value;
	}
	public java.util.Date getCreateTime() {
		return this.createTime;
	}

	public String getTemplate() {
		return template;
	}

	public void setTemplate(String template) {
		this.template = template;
	}

	public Integer getAuditType() {
		return auditType;
	}

	public void setAuditType(Integer auditType) {
		this.auditType = auditType;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public String getAppUrl() {
		return appUrl;
	}

	public void setAppUrl(String appUrl) {
		this.appUrl = appUrl;
	}

	public Integer getLogin() {
		return login == null ? 0 : login;
	}

	public void setLogin(Integer login) {
		this.login = login;
	}

	public String getEncId() {
		return encId;
	}

	public void setEncId(String encId) {
		this.encId = encId;
	}

	public String getEncWebsiteId() {
		return encWebsiteId;
	}

	public void setEncWebsiteId(String encWebsiteId) {
		this.encWebsiteId = encWebsiteId;
	}

	public String getEncWebJsonId() {
		return encWebJsonId;
	}

	public void setEncWebJsonId(String encWebJsonId) {
		this.encWebJsonId = encWebJsonId;
	}

    public String toString() {
		return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
	}
	
	public int hashCode() {
		return new HashCodeBuilder()
			.append(getId())
			.toHashCode();
	}
	
	public boolean equals(Object obj) {
		if(obj instanceof Page == false) return false;
		if(this == obj) return true;
		Page other = (Page)obj;
		return new EqualsBuilder()
			.append(getId(),other.getId())
			.isEquals();
	}
}

