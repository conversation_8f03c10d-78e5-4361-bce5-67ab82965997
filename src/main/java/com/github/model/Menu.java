/*
 */
package com.github.model;

import org.apache.commons.lang.builder.*;

public class Menu {

    // alias
    public static final String TABLE_ALIAS = "Menu";
    public static final String ALIAS_ID = "id";
    public static final String ALIAS_NAME = "name";
    public static final String ALIAS_URL = "url";
    public static final String ALIAS_ROLE = "需要的权限";


    // columns START
    /**
     * id   db_column: id
     */
    private Integer id;
    /**
     * name   db_column: name
     */
    private String name;
    /**
     * url   db_column: url
     */
    private String url;
    /**
     * 需要的权限   db_column: role
     */
    private Integer role;
    /**
     * 类型：0->菜单，1->接口
     */
    private Byte type;
    // columns END

    public Menu() {
    }

    public Menu(Integer id) {
        this.id = id;
    }

    public void setId(Integer value) {
        this.id = value;
    }

    public Integer getId() {
        return this.id;
    }

    public void setName(String value) {
        this.name = value;
    }

    public String getName() {
        return this.name;
    }

    public void setUrl(String value) {
        this.url = value;
    }

    public String getUrl() {
        return this.url;
    }

    public void setRole(Integer value) {
        this.role = value;
    }

    public Integer getRole() {
        return this.role;
    }

    public Byte getType() {
        return type;
    }

    public Menu setType(Byte type) {
        this.type = type;
        return this;
    }

    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

    public int hashCode() {
        return new HashCodeBuilder()
                .append(getId())
                .toHashCode();
    }

    public boolean equals(Object obj) {
        if (obj instanceof Menu == false) return false;
        if (this == obj) return true;
        Menu other = (Menu) obj;
        return new EqualsBuilder()
                .append(getId(), other.getId())
                .isEquals();
    }
}

