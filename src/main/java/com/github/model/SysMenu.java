/*
 */
package com.github.model;

import org.apache.commons.lang.builder.*;

import java.util.List;

public class SysMenu {
	
	//alias
	public static final String TABLE_ALIAS = "SysMenu";
	public static final String ALIAS_ID = "id";
	public static final String ALIAS_PID = "pid";
	public static final String ALIAS_NAME = "name";
	public static final String ALIAS_INTRO = "intro";
	public static final String ALIAS_URL = "url";
	public static final String ALIAS_PAGE = "兼容跟网页匹配的业务";
	
	
	//columns START
	/** id   db_column: id */ 	
	private Integer id;
	/** pid   db_column: pid */ 	
	private Integer pid;
	/** name   db_column: name */ 	
	private String name;
	/** intro   db_column: intro */ 	
	private String intro;
	/** url   db_column: url */ 	
	private String url;
	/** 兼容跟网页匹配的业务   db_column: page */ 	
	private Integer page;

	private List<SysMenu> subs;
	//columns END

	public SysMenu(){
	}

	public SysMenu(
		Integer id
	){
		this.id = id;
	}

	public void setId(Integer value) {
		this.id = value;
	}
	public Integer getId() {
		return this.id;
	}
	public void setPid(Integer value) {
		this.pid = value;
	}
	public Integer getPid() {
		return this.pid;
	}
	public void setName(String value) {
		this.name = value;
	}
	public String getName() {
		return this.name;
	}
	public void setIntro(String value) {
		this.intro = value;
	}
	public String getIntro() {
		return this.intro;
	}
	public void setUrl(String value) {
		this.url = value;
	}
	public String getUrl() {
		return this.url;
	}
	public void setPage(Integer value) {
		this.page = value;
	}
	public Integer getPage() {
		return this.page;
	}

	public List<SysMenu> getSubs() {
		return subs;
	}

	public void setSubs(List<SysMenu> subs) {
		this.subs = subs;
	}

	public String toString() {
		return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
	}
	
	public int hashCode() {
		return new HashCodeBuilder()
			.append(getId())
			.toHashCode();
	}
	
	public boolean equals(Object obj) {
		if(obj instanceof SysMenu == false) return false;
		if(this == obj) return true;
		SysMenu other = (SysMenu)obj;
		return new EqualsBuilder()
			.append(getId(),other.getId())
			.isEquals();
	}
}

