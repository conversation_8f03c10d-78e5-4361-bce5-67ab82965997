/*
 * ............................................. 
 * 
 * 				    _ooOoo_ 
 * 		  	       o8888888o 
 * 	  	  	       88" . "88 
 *                 (| -_- |) 
 *                  O\ = /O 
 *              ____/`---*\____ 
 *               . * \\| |// `. 
 *             / \\||| : |||// \ 
 *           / _||||| -:- |||||- \ 
 *             | | \\\ - /// | | 
 *            | \_| **\---/** | | 
 *           \  .-\__ `-` ___/-. / 
 *            ___`. .* /--.--\ `. . __ 
 *        ."" *< `.___\_<|>_/___.* >*"". 
 *      | | : `- \`.;`\ _ /`;.`/ - ` : | | 
 *         \ \ `-. \_ __\ /__ _/ .-` / / 
 *======`-.____`-.___\_____/___.-`____.-*====== 
 * 
 * ............................................. 
 *              佛祖保佑 永无BUG 
 *
 * 佛曰: 
 * 写字楼里写字间，写字间里程序员； 
 * 程序人员写程序，又拿程序换酒钱。 
 * 酒醒只在网上坐，酒醉还来网下眠； 
 * 酒醉酒醒日复日，网上网下年复年。 
 * 但愿老死电脑间，不愿鞠躬老板前； 
 * 奔驰宝马贵者趣，公交自行程序员。 
 * 别人笑我忒疯癫，我笑自己命太贱； 
 * 不见满街漂亮妹，哪个归得程序员？
 *
 * 北纬30.√  <EMAIL>
 */
package com.github.model;

import org.apache.commons.lang.builder.*;

public class WebsiteMenu {
	
	//alias
	public static final String TABLE_ALIAS = "WebsiteMenu";
	public static final String ALIAS_ID = "id";
	public static final String ALIAS_WEBSITE_ID = "网站ID";
	public static final String ALIAS_NAME = "菜单名称";
	public static final String ALIAS_URL = "打开地址";
	public static final String ALIAS_STATUS = "状态";
	
	
	//columns START
	/** id   db_column: id */ 	
	private Integer id;
	/** 网站ID   db_column: website_id */ 	
	private Integer websiteId;
	/** 菜单名称   db_column: name */ 	
	private String name;
	/** 打开地址   db_column: url */ 	
	private String url;
	/** 状态   db_column: status */ 	
	private Integer status;
	//columns END

	public WebsiteMenu(){
	}

	public WebsiteMenu(
		Integer id
	){
		this.id = id;
	}

	public void setId(Integer value) {
		this.id = value;
	}
	public Integer getId() {
		return this.id;
	}
	public void setWebsiteId(Integer value) {
		this.websiteId = value;
	}
	public Integer getWebsiteId() {
		return this.websiteId;
	}
	public void setName(String value) {
		this.name = value;
	}
	public String getName() {
		return this.name;
	}
	public void setUrl(String value) {
		this.url = value;
	}
	public String getUrl() {
		return this.url;
	}
	public void setStatus(Integer value) {
		this.status = value;
	}
	public Integer getStatus() {
		return this.status;
	}

	public String toString() {
		return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
	}
	
	public int hashCode() {
		return new HashCodeBuilder()
			.append(getId())
			.toHashCode();
	}
	
	public boolean equals(Object obj) {
		if(obj instanceof WebsiteMenu == false) return false;
		if(this == obj) return true;
		WebsiteMenu other = (WebsiteMenu)obj;
		return new EqualsBuilder()
			.append(getId(),other.getId())
			.isEquals();
	}
}

