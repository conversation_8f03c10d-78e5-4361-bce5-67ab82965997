package com.github.model;


public class TemplateSwitchover {

    //alias
    public static final String ALIAS_ID = "id";
    public static final String ALIAS_TYPE = "模版类型";
    public static final String ALIAS_DESCRIPTION = "模版说明";
    public static final String ALIAS_STATUS = "状态 1上线 0下线";
    public static final String ALIAS_SEQUENCE = "顺序";


    //columns START
    private Integer id;
    private String type;
    private String description;
    private Integer status;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
}

