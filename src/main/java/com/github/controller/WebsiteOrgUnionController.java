package com.github.controller;

import com.alibaba.fastjson.JSON;
import com.github.model.Website;
import com.github.model.WebsiteOrgUnion;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.github.service.WebsiteOrgUnionService;
import com.github.service.WebsiteService;
import com.github.util.ExcelUtil;
import com.github.util.RestResponse;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.thymeleaf.util.StringUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @version v1.0
 * @author: liban
 * @since: 23/04/2021 11:45 AM
 * @description :给网站配置的联盟单位登录
 */

@Controller
@RequestMapping("website-org-union")
public class WebsiteOrgUnionController {

    @Resource
    private HttpServletResponse response;

    @Resource
    WebsiteOrgUnionService websiteOrgUnionService;
    @Resource
    WebsiteService websiteService;

    private Logger logger = LoggerFactory.getLogger(WebsiteOrgUnionController.class);

    @ResponseBody
    @RequestMapping("add")
    public RestResponse add(Integer id,String websiteId, Integer fid,String orgName) {
        Website website = websiteService.getWebsite(websiteId);
        WebsiteOrgUnion union = websiteOrgUnionService.getByWebsiteWfwfId(website.getId(), fid);

        WebsiteOrgUnion orgUnion = new WebsiteOrgUnion();
        orgUnion.setFid(fid);
        orgUnion.setWebsiteId(website.getId());
        orgUnion.setOrgName(orgName);
        orgUnion.setId(id);
        if (Objects.nonNull(union)) {
            return RestResponse.error("单位已经存在！");
        }
        try {
            websiteOrgUnionService.add(orgUnion);
        } catch (Exception e) {
            e.printStackTrace();
            return RestResponse.error("添加失败！");
        }

        return RestResponse.ok("添加成功！");
    }

    /**
     * 分页
     * @param websiteId
     * @param pageNum
     * @param pageSize
     * @param unionName 单位名称
     * @return
     */
    @ResponseBody
    @RequestMapping("get")
    @CrossOrigin
    public RestResponse getByWebsiteId(String websiteId,String unionName,
                                       @RequestParam(required = false, defaultValue = "1") Integer pageNum,
                                       @RequestParam(required = false, defaultValue = "10") Integer pageSize,
                                       HttpServletRequest request) {
        String referer = request.getHeader("Referer");
        if (!StringUtils.contains(referer, ".chaoxing.com")) {
            return RestResponse.error("暂无权限");
        }

        RestResponse restResponse = RestResponse.ok();
        Website website = websiteService.getWebsite(websiteId);
        try {
            Page page = PageHelper.startPage(pageNum, pageSize);
            websiteOrgUnionService.list(website.getId(),unionName);
            restResponse.setData(new PageInfo<WebsiteOrgUnion>(page));
        } catch (Exception e) {
            e.printStackTrace();
            restResponse.setCode(RestResponse.CODE_ERROR);
            restResponse.setMessage("查询失败！");
        }
        return restResponse;
    }

    @ResponseBody
    @RequestMapping("delete")
    public RestResponse delete(Integer id) {
        RestResponse restResponse = RestResponse.ok();
        try {
            websiteOrgUnionService.delete(id);
        } catch (Exception e) {
            e.printStackTrace();
            restResponse.setCode(RestResponse.CODE_ERROR);
            restResponse.setMessage("删除失败！");
        }
        return restResponse;
    }

    @ResponseBody
    @RequestMapping("batch-delete")
    public RestResponse delete(String dataJson) {
        RestResponse restResponse = RestResponse.ok();
        List<WebsiteOrgUnion> lists = JSON.parseArray(dataJson, WebsiteOrgUnion.class);
        try {
            List<Integer> ids = lists.stream().map(WebsiteOrgUnion::getId).collect(Collectors.toList());
            websiteOrgUnionService.batchDelete(ids);
        } catch (Exception e) {
            e.printStackTrace();
            restResponse.setCode(RestResponse.CODE_ERROR);
            restResponse.setMessage("删除失败！");
        }
        return restResponse;
    }

    @RequestMapping("export-tmp")
    @ResponseBody
    public void exportTmp() {

        OutputStream os = null;
        try {
            response.reset();
            // 设置强制下载不打开
            response.setContentType("application/force-download");
            // 设置文件名
            response.addHeader("Content-Disposition",
                    "attachment;fileName=" + "template.xlsx");

            List<Map<String, String>> headList = new ArrayList();
            String sheetName = "模板";

            headList.add(new HashMap() {{
                put("key", "fid");
                put("name", "单位fid");
            }});

            headList.add(new HashMap() {{
                put("key", "orgName");
                put("name", "单位名");
            }});

            SXSSFWorkbook xss = ExcelUtil.writeExcel(headList, null, sheetName);

            os = response.getOutputStream();
            xss.write(os);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (os != null) {
                try {
                    os.close();
                } catch (IOException e) {
                }
            }
        }
    }
    @RequestMapping("{websiteId}/import")
    @ResponseBody
    public RestResponse importData(HttpServletRequest request, @PathVariable("websiteId") String websiteId, @RequestParam("file") MultipartFile file) {
        try {
            if (file == null) {
                return RestResponse.error("上传文件为空");
            }

            Website website = websiteService.getWebsite(websiteId);
            List<List<String>> dataList = ExcelUtil.readExcelSingle(file);
            //保存数据
            List<WebsiteOrgUnion> orgUnions = new ArrayList<>();
            dataList.stream().forEach(t ->{
                WebsiteOrgUnion orgUnion = new WebsiteOrgUnion();
                orgUnion.setWebsiteId(website.getId());
                orgUnion.setFid(Integer.valueOf(t.get(0)));
                orgUnion.setOrgName(t.get(1));
                orgUnions.add(orgUnion);
            });

            websiteOrgUnionService.batchAdd(orgUnions);

            return RestResponse.ok();
        } catch (Exception e) {
            e.printStackTrace();
            return RestResponse.error();
        }
    }

}
