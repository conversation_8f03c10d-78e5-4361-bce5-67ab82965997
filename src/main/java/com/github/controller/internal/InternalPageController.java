package com.github.controller.internal;

import com.alibaba.fastjson.JSONObject;
import com.github.enums.ErrorCode;
import com.github.model.*;
import com.github.model.dto.OrganizationApplicationDTO;
import com.github.service.*;
import com.github.util.Constants;
import com.github.util.RegexUtil;
import com.github.util.RestResponse;
import com.github.util.exception.BusinessException;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @className InternalPageController
 * @Description TODO
 * <AUTHOR>
 * @Date 2022/10/26 17:51
 * @Version 1.0
 **/
@Controller
@RequestMapping
public class InternalPageController {
    @Resource
    private ApplicationService applicationService;
    @Resource
    private WebsiteService websiteService;
    @Resource
    private RoleService roleService;
    @Resource
    private WebJsonService webJsonService;
    @Resource
    private PageService pageService;
    @Resource
    private PageContentService pageContentService;
    @Resource
    private PageThemeRelService pageThemeRelService;
    @Resource
    private WebsiteIpsService websiteIpsService;
    @Resource
    private WebsitePlugsService websitePlugsService;
    @Resource
    private WebsiteDomainService websiteDomainService;
    @Resource
    private PageThemeService pageThemeService;
    @Resource
    private WebsiteV2Service websiteV2Service;
    @Resource
    private WebJsonRelService webJsonRelService;
    @Resource
    private PortalService portalService;
    @Resource
    private UserAppPermissionService userAppPermissionService;

    @Value("${version.updateTime}")
    private String versionUpdateTime;
    @Value("${test.version:0}")
    private Integer testVersion;
    @Value("${engine-inner.domain-name}")
    private String domainInner;
    @Resource
    private WebsiteConfigService websiteConfigService;

    @ResponseBody
    @GetMapping("page/{id}/top/bottom/msg")
    public Object topBottomMsg(@PathVariable Integer id, @RequestParam(defaultValue = "1") Integer index, @RequestParam(defaultValue = "0") Integer isMobile, String realDomain) {
        Page page = pageService.get(id);
        RestResponse restResponse = RestResponse.ok();
        // 处理为空
        if (Objects.isNull(page)) {
            return restResponse.nonTopBottom();
        }
        Website website = websiteService.get(page.getWebsiteId());
        if (Objects.isNull(website)) {
            return restResponse.nonTopBottom();
        }
        WebsiteConfig websiteConfig = websiteConfigService.get(website.getId());
        // 判断网站的域名和传入过来的pageId 是否匹配。 是同一个网站可以混用，如果网站，不允许混用。

        //判断是否显示头部底部
        WebJson webJsonHide = webJsonService.get(page.getWebJsonId());
        JSONObject result = Optional.ofNullable(JSONObject.parseObject(webJsonHide.getContent()).getJSONObject("setting")).orElse(new JSONObject());

        boolean hasHeader;
        boolean hasFooter;
        if (page.getTemplate().startsWith(Template.TEMPLATE_2)) {
            hasHeader = JSONObject.parseObject(webJsonHide.getContent()).getBooleanValue("hasHeader");
            hasFooter = JSONObject.parseObject(webJsonHide.getContent()).getBooleanValue("hasFooter");
        } else {
            hasHeader = result.getBooleanValue("hasHeader");
            hasFooter = result.getBooleanValue("hasFooter");
            if (Constants.STATUS_TRUE.equals(isMobile)){
                if (hasHeader && Objects.nonNull(result.getBoolean("showAppFooter"))){
                    hasHeader = result.getBoolean("showAppFooter");
                }
                if (hasFooter && Objects.nonNull(result.getBoolean("showAppHeader"))){
                    hasFooter = result.getBooleanValue("showAppHeader");
                }
            }
        }
        restResponse.put("hasHeader", hasHeader);
        restResponse.put("hasFooter", hasFooter);

        restResponse.put("defaultId", website.getHomePageId());
        Page homePage = pageService.get(website.getHomePageId());
        restResponse.put("defPubId", homePage.getPublicId());
        restResponse.put("pubId", page.getPublicId());
        restResponse.put("wfwfid", website.getWfwfid());
        restResponse.put("status", website.getStatus());
        // 未发布，30天内可见字段 visiable
        Date date = new Date();
        Date dateAdd = org.apache.commons.lang.time.DateUtils.addDays(website.getCreateTime(),30);
        if(date.after(dateAdd) && website.getStatus().equals(Website.UNPUBLISHED)){ // 30天之外,且未发布
            restResponse.put("visiable", false);
        }else {
            restResponse.put("visiable", true);
        }

        restResponse.put("websiteId", website.getId());
        restResponse.put("loginCurrentOrg", website.getLoginCurrentOrg());
        restResponse.put("globalType", website.getGlobalType());

        String color = RegexUtil.getGroup1MatchContent("\"themeColor\":\"(.+?)\"", webJsonHide.getContent());

        restResponse.put("name", page.getName());
        restResponse.put("color", color);
        restResponse.put("aUrl", websiteService.offUrl(isMobile, website));
        // 返回上下线时间
        restResponse.put("upOffTime", websiteConfig==null?"":websiteConfig.getUpOffTime());
        restResponse.put("ckLogin", website.getCheckLogin());
        // 是否强制校验域名, 测试环境默认不校验域名
        if (testVersion == 1) {
            restResponse.put("ckDm", false);
        } else {
            restResponse.put("ckDm", website.getForceCheckDomain());
        }
        // 带有多语言版本的父子网站，子网站不做域名校验。
        if (website.getMainSite() != null) {
            restResponse.put("ckDm", false);
        }
        restResponse.put("ltUrl", website.getLogoutUrl());
        restResponse.put("dom", website.getDomain());
        WebsiteIps websiteIps = websiteIpsService.getByWebsiteId(website.getId());
        // 白名单列表
        if (Objects.nonNull(websiteIps) && Constants.STATUS_TRUE.equals(websiteIps.getCheck())) {
            restResponse.put("websiteIps", websiteIps.getIps());
        }
        List<WebsiteDomain> wds = websiteDomainService.getListByWebsiteId(website.getId());
        if (CollectionUtils.isNotEmpty(wds)) {
            restResponse.put("doE", wds.get(0).getDomain());
            restResponse.put("doEs", wds.stream().map(WebsiteDomain::getDomain).collect(Collectors.toList()));
        } else {
            restResponse.put("doE", "");
            restResponse.put("doEs", new ArrayList<>());
        }

        //当头部为空
        String top = "";
        String bottom = "";

        if (index == 0) {
            String topStr = WebJsonService.dealTopAndBottom(webJsonHide, "headModule");
            top += topStr;
        }

        if (index == 0) {
            String bottomStr = WebJsonService.dealTopAndBottom(webJsonHide, "footModule");
            bottom += bottomStr;
        }
        restResponse.put("top", top);
        restResponse.put("bottom", bottom);
        List<String> plugsHtml = websitePlugsService.getHtmlByIdAndType(page.getWebsiteId(), WebsitePlugs.TYPE_MORE_DETAIL, page.getId());
        restResponse.put("plugsHtml", plugsHtml);

        //是否主题包
        Boolean hasPageTheme = false;
        PageThemeRel rel = pageThemeRelService.getByPageId(website.getHomePageId());
        if (Objects.nonNull(rel)) {
            hasPageTheme = true;
        }
        restResponse.put("hasPt", hasPageTheme);
        if(!ObjectUtils.isEmpty(websiteConfig)){
            restResponse.put("customDomainForEditor",websiteConfig.getCustomDomainEditor());
            JSONObject otherConfig = websiteConfig.getOtherConfig();
            if (otherConfig != null && otherConfig.containsKey("useWpsPreview")) {
                restResponse.put("useWpsPreview", otherConfig.getBooleanValue("useWpsPreview"));
            } else {
                restResponse.put("useWpsPreview", false);
            }
        }else {
            restResponse.put("customDomainForEditor",false);
            restResponse.put("useWpsPreview", false);
        }
        restResponse.put("loginType", website.getLoginType());
        return restResponse;
    }

    /**
     * 网站下所有page页面 和page下的appIds
     *
     * @param webId
     * @return
     */
    @ResponseBody
    @GetMapping("internal/page/list-by-web-id/{webId}")
    public RestResponse listPages(@PathVariable Integer webId) {
        List<Page> list = pageService.getByWebsiteId(webId);
        Website website = websiteService.get(webId);
        // appId和alias关系
        List<Integer> appIds = new ArrayList();

        // 一次查出所有webjson
        List<Integer> jsonIds = list.stream().map(Page::getWebJsonId).collect(Collectors.toList());
        List<WebJson> webJsons = webJsonService.listByIds(jsonIds);
        Map<Integer, WebJson> webJsonMap = webJsons.stream().collect(Collectors.toMap(WebJson::getId, webjson -> webjson));
        // 每个page的appIds
        Map<Integer, List> pageAppIdMsap = list.stream().collect(Collectors.toMap(Page::getId, p -> {
            WebJson json = webJsonMap.get(p.getWebJsonId());
            List<Integer> appIdsSet = webJsonService.listAppIds(json.getContent(), json.getId());
            appIds.addAll(appIdsSet);
            return appIdsSet;
        }));

        RestResponse ok = RestResponse.ok().put("map", pageAppIdMsap).put("pages", list);
        if (CollectionUtils.isEmpty(appIds)) {
            return ok;
        }
        List<OrganizationApplicationDTO> alias = websiteService.listByApplicationIdsAndWebId(appIds, webId, website.getWfwfid());
        return ok.put("alias", alias);
    }
 /**
     * 网站下所有page页面 和page下的appIds
     *
     * @param webId
     * @return
     */
    @ResponseBody
    @GetMapping("internal/page/list-by-web-id/{webId}/{uid}")
    public RestResponse listPages(@PathVariable Integer webId, @PathVariable Integer uid) {
        Website website = websiteService.get(webId);
        if (roleService.isSuperAdmin(uid.toString()) || uid.equals(website.getCreateUser()) || userAppPermissionService.checkWebsitePermissionByFidUid(website, website.getWfwfid(), uid)) {
            return this.listPages(webId);
        }
        return RestResponse.error("没有网站修改权限");
    }

    @GetMapping("internal/page-theme/get/{pageId}")
    @ResponseBody
    public RestResponse get(@PathVariable Integer pageId, @RequestParam(required = false, defaultValue = "0") Integer appId,
                            @RequestParam(required = false, defaultValue = "0") Integer wfwfid) {
        Page page = pageService.get(pageId);
        Website website = websiteService.getById(page.getWebsiteId());
        if (PageTheme.ptTypeDltMode(website.getPageThemeType())) { // 默认
            return RestResponse.ok(null);
        }

        PageThemeRel rel = pageThemeRelService.getByPageId(website.getHomePageId());
        if (Objects.isNull(rel)) {
            return RestResponse.ok(null);
        }

        //主页的主题包【可能是源网站的】
        PageTheme pTheme = pageThemeService.get(rel.getPageThemeId());
        if (PageTheme.ptTypeMyMode(website.getPageThemeType()) && !website.getHomePageId().equals(pTheme.getPageId())) { //自定义，不用源网站的主题包
            return RestResponse.ok(null);
        }

        PageTheme pageTheme;
        if (pageId.equals(website.getHomePageId())) { // 是主页
            pageTheme = pTheme;
        } else { // 不是主页，需要找对应的子页面主题包配置
            List<Page> pages = pageService.getByWebsiteId(page.getWebsiteId());
            pages = pages.stream().filter(p -> !StringUtils.equals(p.getTemplate(), Constants.LOGIN_TEMPLATE)).collect(Collectors.toList());
            int i;
            for (i = 0; i < pages.size(); i++) {
                if (pages.get(i).getId().equals(pageId)) {
                    break;
                }
            }

            // 目标主题包page
            Page aidPage;
            Integer websiteId;
            if (Objects.nonNull(website.getOrigin()) && website.getOrigin() != 0) { //使用源网站配置
                Website themeWebsite = websiteService.get(website.getOrigin());
                websiteId = themeWebsite.getId();

                List<Page> themePages = pageService.getByWebsiteId(websiteId);
                themePages = themePages.stream().filter(p -> !StringUtils.equals(p.getTemplate(), Constants.LOGIN_TEMPLATE)).collect(Collectors.toList());
                aidPage = themePages.get(i);
            } else { // 不存在源网站，直接使用自己的配置
                aidPage = pages.get(i);
            }

            List<PageTheme> themes = pageThemeService.listByTypeOrUid(null, pTheme.getId(), aidPage.getId());
            if (CollectionUtils.isEmpty(themes)) {
                return RestResponse.ok(null);
            } else {
                pageTheme = themes.get(0);
            }
        }


        int index = 0;
        if (!Constants.STATUS_FALSE.equals(appId)) {
            WebJson webJson = webJsonService.getByPageId(pageId);
            List<Application> apps = applicationService.getListByFid(wfwfid, pageId);
            List<Integer> appIdList = apps.stream().map(Application::getId).collect(Collectors.toList());
            List<Integer> webAppIds = webJsonService.listAppIdsPageThemeUse(webJson.getContent(), webJson.getId());
            for (Integer appId2 : webAppIds) {
                if (appIdList.contains(appId2)) {
                    // 找到这个appId的位置
                    if (Objects.equals(appId, appId2)) {
                        break;
                    }
                    index++;
                }
            }
        }
        return RestResponse.ok().put("pageTheme", pageTheme).put("index", index);
    }


    /**
     * @param id
     * @return
     */
    @GetMapping("internal/page/pubid/{id}")
    @ResponseBody
    public RestResponse getPageByPubId(@PathVariable String id) {
        Page page = pageService.getByPubId(id);
        return RestResponse.ok(page.getId());
    }

    /**
     * @return
     */
    @GetMapping("internal/website-page")
    @ResponseBody
    public RestResponse getWebsiteAndPageByPubId(String w, String p) {
        Website site = websiteService.getWebsite(w);
        Page page = websiteV2Service.getPage(p);
        return RestResponse.ok().put("page", page).put("website", site);
    }

    /**
     * @return
     */
    @GetMapping("internal/page/{p}")
    @ResponseBody
    public RestResponse getWebsiteAndPageByPubId(@PathVariable Integer p) {
        Page page = pageService.get(p);
        return RestResponse.ok(page);
    }

    /**
     * 根据pageId查询网站信息、页面信息
     * @return
     */
    @GetMapping("internal/web-page/{p}")
    @ResponseBody
    public RestResponse getWebsiteAndPageByPageId(@PathVariable Integer p) {
        Page page = pageService.get(p);
        if(page == null){
            return RestResponse.error("页面不存在");
        }
        Website website = websiteService.get(page.getWebsiteId());
        return RestResponse.ok().put("page", page).put("website", website);
    }

    @GetMapping("internal/page/list-by-fid/{wfwfid}")
    @ResponseBody
    public RestResponse listPageByFid(@PathVariable Integer wfwfid) {
        return RestResponse.ok(pageService.getByWfwfid(wfwfid));
    }

    @RequestMapping("internal/page/page-json")
    @ResponseBody
    public RestResponse getPageJsonIdMap(@RequestParam String pageIdListStr) {
        List<Integer> pageIdList = Arrays.asList(pageIdListStr.split(",")).stream().map(s -> Integer.parseInt(s)).collect(Collectors.toList());
        List<Page> pageList = pageService.getListByIds(pageIdList);
        Map<Integer, Integer> pageJsonIdMap = pageList.stream().collect(Collectors.toMap(Page::getId, Page::getWebJsonId, (k1, k2) -> k2));
        return RestResponse.ok(pageJsonIdMap);
    }

    /**
     * engine2 或者其他内部项目调用，通过域名获取到网站信息，用于接口校验
     *
     * @param domain
     * @return
     */
    @RequestMapping("internal/website/id")
    @ResponseBody
    public RestResponse getWebsiteId(String domain) {
        Website website = portalService.getWebsite(domain);
        return RestResponse.ok(website.getId());
    }

    /**
     * 2.0内部ssr调用,通过pageId查homePageId
     *
     * @param id page的publicId
     * @return
     */
    @RequestMapping("internal/page/{id}/home")
    @ResponseBody
    public RestResponse getHomePageId(@PathVariable String id) {
        Page page = pageService.getByPubId(id);
        Optional.ofNullable(page).orElseThrow(() -> new BusinessException(ErrorCode.CODE_NULL_POINTER.getCode()));
        Website website = websiteService.get(page.getWebsiteId());
        WebsiteConfig websiteConfig = websiteConfigService.get(page.getWebsiteId());
        boolean custom = false;
        if(null != websiteConfig){
            custom = websiteConfig.getCustomDomainEditor();
        }
        Page homePage = pageService.get(website.getHomePageId());
        return RestResponse.ok().put("publicId",homePage.getPublicId()).put("customDomainEditor", custom);
    }

    /**
     * 获取页面主题色和插件信息
     *
     * @param id
     * @return
     */
    @ResponseBody
    @GetMapping("page/{id}/msg")
    public Object msg(@PathVariable Integer id) {
        Page page = pageService.get(id);
        RestResponse restResponse = RestResponse.ok();
        // 处理为空
        if (Objects.isNull(page)) {
            return restResponse.nonTopBottom();
        }
        Website website = websiteService.get(page.getWebsiteId());
        String color = RegexUtil.getGroup1MatchContent("\"themeColor\":\"(.+?)\"", webJsonService.get(page.getWebJsonId()).getContent());
        restResponse.put("color", color);
        List<String> plugsHtml = websitePlugsService.getHtmlByIdAndType(page.getWebsiteId(), WebsitePlugs.TYPE_MORE_DETAIL, page.getId());
        restResponse.put("plugsHtml", plugsHtml);

        // 网站自定义笔记编辑器配置信息
        WebsiteConfig websiteConfig = websiteConfigService.get(website.getId());
        if (!ObjectUtils.isEmpty(websiteConfig)) {
            restResponse.put("customDomainForEditor", websiteConfig.getCustomDomainEditor());
            JSONObject otherConfig = websiteConfig.getOtherConfig();
            if (otherConfig != null && otherConfig.containsKey("useWpsPreview")) {
                restResponse.put("useWpsPreview", otherConfig.getBooleanValue("useWpsPreview"));
            } else {
                restResponse.put("useWpsPreview", false);
            }
        } else {
            restResponse.put("customDomainForEditor", false);
            restResponse.put("useWpsPreview", false);
        }
        return restResponse;
    }

    /**
     * 网站设置信息
     *
     * @param id
     * @return
     */
    @ResponseBody
    @GetMapping("page/{id}/website-msg")
    public Object websiteMsg(@PathVariable Integer id, Integer isMobile) {
        Page page = pageService.get(id);
        RestResponse restResponse = RestResponse.ok();
        // 处理为空
        if (Objects.isNull(page)) {
            return restResponse.nonTopBottom();
        }

        Website website = websiteService.get(page.getWebsiteId());

        // 判断网站的域名和传入过来的pageId 是否匹配。 是同一个网站可以混用，如果网站，不允许混用。

        //判断是否显示头部底部
        WebJson webJsonHide = webJsonService.get(page.getWebJsonId());
        JSONObject result = Optional.ofNullable(JSONObject.parseObject(webJsonHide.getContent()).getJSONObject("setting")).orElse(new JSONObject());

        boolean hasHeader;
        boolean hasFooter;
        if (page.getTemplate().startsWith(Template.TEMPLATE_2)) {
            hasHeader = JSONObject.parseObject(webJsonHide.getContent()).getBooleanValue("hasHeader");
            hasFooter = JSONObject.parseObject(webJsonHide.getContent()).getBooleanValue("hasFooter");
        } else {
            hasHeader = result.getBooleanValue("hasHeader");
            hasFooter = result.getBooleanValue("hasFooter");
            if (Constants.STATUS_TRUE.equals(isMobile)){
                if (hasHeader && Objects.nonNull(result.getBoolean("showAppFooter"))){
                    hasHeader = result.getBoolean("showAppFooter");
                }
                if (hasFooter && Objects.nonNull(result.getBoolean("showAppHeader"))){
                    hasFooter = result.getBooleanValue("showAppHeader");
                }
            }
        }
        restResponse.put("hasHeader", hasHeader);
        restResponse.put("hasFooter", hasFooter);

        restResponse.put("defaultId", website.getHomePageId());
        restResponse.put("pubId", page.getPublicId());
        restResponse.put("wfwfid", website.getWfwfid());
        restResponse.put("cdnVersion", website.getCdnVersion());
        restResponse.put("websiteId", website.getId());
        restResponse.put("globalType", website.getGlobalType());

        String color = RegexUtil.getGroup1MatchContent("\"themeColor\":\"(.+?)\"", webJsonHide.getContent());

        restResponse.put("name", page.getName());
        restResponse.put("color", color);
        restResponse.put("aUrl", websiteService.offUrl(isMobile, website));
        restResponse.put("ckLogin", website.getCheckLogin());
        // 是否强制校验域名, 测试环境默认不校验域名
        if (testVersion == 1) {
            restResponse.put("ckDm", false);
        } else {
            restResponse.put("ckDm", website.getForceCheckDomain());
        }
        restResponse.put("ltUrl", website.getLogoutUrl());
        restResponse.put("dom", website.getDomain());
        WebsiteIps websiteIps = websiteIpsService.getByWebsiteId(website.getId());
        // 白名单列表
        if (Objects.nonNull(websiteIps) && Constants.STATUS_TRUE.equals(websiteIps.getCheck())) {
            restResponse.put("websiteIps", websiteIps.getIps());
        }
        WebsiteDomain wd = websiteDomainService.getByWebsiteId(website.getId());
        if (null != wd) {
            restResponse.put("doE", wd.getDomain());
        } else {
            restResponse.put("doE", "");
        }
        return restResponse;
    }


    /**
     * 判断是否大屏网站
     * @return
     */
    @RequestMapping("internal/website/{webId}/is-screen")
    @ResponseBody
    public RestResponse isBsApp(@PathVariable Integer webId) {
        Website website = websiteService.get(webId);
        Optional.ofNullable(website).orElseThrow(() -> new BusinessException(ErrorCode.CODE_NULL_POINTER.getCode()));
        return RestResponse.ok(Website.WEB_TYPE_BIG_SCREEN.equals(website.getWebType()));
    }


    /**
     * 根据pageId 查询到所有的应用及数据
     * 这里不配置跨域， 由nginx统一配置。
     * @param head 0 需要头和底，1 不需要头，2 不需要底，3 头底都不需要
     * @param id
     * @param mobile 是否移动端 pc端0 移动端 1
     * @param w websiteId
     * @return
     */
//    @ResponseBody
//    @RequestMapping("v2/{id}/all-request")
//    public RestResponse pageContent(@PathVariable Integer id, Integer sfid, String sversion, @RequestParam(defaultValue = "0") Integer head, HttpServletRequest request, @RequestParam(defaultValue = "0") Integer mobile) {
//    }


    /**
     * 兑换 webPubId、p_wfwfid
     * @param p page的publicId
     * @param v website构建的version
     * @return
     */
    @RequestMapping("v2/page/{p}/{v}/chargeId")
    @ResponseBody
    public RestResponse beat(@PathVariable String p, @PathVariable String v) {
        Page page = pageService.getByPubId(p);
        Optional.ofNullable(page).orElseThrow(() -> new BusinessException(ErrorCode.CODE_NULL_POINTER.getCode()));
        Website website = websiteService.get(page.getWebsiteId());
        if (StringUtils.equals(pageService.getVersion(website, page), v)) {
            return RestResponse.ok().put("webPubId", website.getPublicId()).put("p_wfwfid", website.getWfwfid());
        }
        return RestResponse.error("参数不匹配");
    }


    /**
     * 获取页面对应的webjson内容 【内部使用的】
     *
     * @param id
     * @return
     */
    @GetMapping("page/{id}/webjson")
    @ResponseBody
    public RestResponse webjson(@PathVariable Integer id) {
        RestResponse restResponse = RestResponse.ok();
        Page page = pageService.get(id);
        WebJson webJson = webJsonService.get(page.getWebJsonId());
        restResponse.setData(webJson);
        return restResponse;
    }

    /**
     * 根据websiteId查首页信息
     */
    @GetMapping("internal/home-page/{websiteId}")
    @ResponseBody
    public RestResponse getHomePageByWebsiteId(@PathVariable Integer websiteId) {
        Website website = websiteService.get(websiteId);
        return RestResponse.ok(pageService.get(website.getHomePageId()));
    }

    /**
     * 根据pageId查该网站的类型   1：1.0   2：2.0   3：大屏
     */
    @GetMapping("internal/page/mh-type/{p}")
    @ResponseBody
    public RestResponse geMhTypeByPageId(@PathVariable Integer p) {
        Page page = pageService.get(p);
        if (page != null) {
            if (Template.isBsTemplate(page.getTemplate())) {
                return RestResponse.ok(3);
            } else if (Template.isV2Tempalte(page.getTemplate())) {
                return RestResponse.ok(2);
            }
            return RestResponse.ok(1);
        }
        return RestResponse.error();
    }

    /**
     * 获取应用的详情页pageId
     */
    @GetMapping("internal/page/get-detail-page-id")
    @ResponseBody
    public RestResponse getDetailPageId(@RequestParam String appPubId, @RequestParam Integer pageId) {
        Page page = pageService.get(pageId);
        if (page == null) {
            return RestResponse.error("页面不存在");
        }
        return RestResponse.ok(webJsonRelService.getDetailPageId(appPubId, page.getWebJsonId()));
    }

    /**
     * 获取应用的列表页pageId
     */
    @GetMapping("internal/page/get-list-page-id")
    @ResponseBody
    public RestResponse getListPageId(@RequestParam String appPubId, @RequestParam Integer pageId) {
        Page page = pageService.get(pageId);
        if (page == null) {
            return RestResponse.error("页面不存在");
        }
        return RestResponse.ok(webJsonRelService.getListPageId(appPubId, page.getWebJsonId()));
    }

    /**
     * 获取应用名称
     */
    @GetMapping("internal/application/list-org-app")
    @ResponseBody
    public RestResponse getDetailPageId(String appIds) {
        if (StringUtils.isNotBlank(appIds)) {
            List<Integer> appIdList = new ArrayList<>();
            for (String appId : appIds.split(",")) {
                appIdList.add(Integer.parseInt(appId));
            }
            return RestResponse.ok(applicationService.listOrgByIds(appIdList));
        }
        return RestResponse.error();
    }
}
