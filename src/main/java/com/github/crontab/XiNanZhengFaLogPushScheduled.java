package com.github.crontab;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.cache.redis.lock.RedisLock;
import com.github.model.Page;
import com.github.model.Website;
import com.github.model.WebsiteWfwClone;
import com.github.model.WebsiteWfwCloneDetail;
import com.github.model.dto.WebVisitDataDto;
import com.github.model.extend.DataScan;
import com.github.model.vo.WebsiteWfwCloneVo;
import com.github.service.EsDataService;
import com.github.service.WebsiteDomainService;
import com.github.service.WebsiteService;
import com.github.util.RestResponse;
import com.github.util.SpringContextHolder;
import org.apache.commons.collections4.CollectionUtils;
import org.redisson.Redisson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.Resource;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 西南政法日志推送
 */
@Component
public class XiNanZhengFaLogPushScheduled {
    @Resource
    private Redisson redisson;
    @Resource
    private EsDataService esDataService;
    @Resource
    private WebsiteService websiteService;
    @Resource
    @Value("${engine-inner.domain-name}")
    private String innerEngineDomain;

    @Value("${isgray:false}")
    private Boolean isGray;
    @Resource
    private RestTemplate restTemplate;
    private Integer WEBSITE_ID = 28168;
    private static Integer BATCH_SIZE = 2000;
    private final String lockKey = "push:xnzf_log_push";
    Logger logger = LoggerFactory.getLogger(XiNanZhengFaLogPushScheduled.class);
    @Scheduled(cron = "0 0 2 * * ?") //每天23点执行 推送前一天的数据
    public void dealErrorData(){
        if(isGray || SpringContextHolder.isMirror()){
            return;
        }
        RedisLock redisLock = new RedisLock(redisson, lockKey);
        if (redisLock.tryLock()) {
            try {
                logger.error("西南政法日志推送开始");
                //获取数据- 按小时获取
                List<Long> timestampList = timestampList();
                for (int i = 0; i < timestampList.size()-1; i++) {
                    Long start = timestampList.get(i);
                    Long end = timestampList.get(i+1);
                    JSONObject logsInRange = fetchData(start, end);
                    Integer total = logsInRange.getInteger("total");
                    logger.error("{} - {} 批次数据总量{}",start,end,total);
                    if(total.equals(0)){
                        continue;
                    }else {
                        pushLogs(logsInRange);
                    }
                }
                logger.error("西南政法日志推送结束");
            }catch (Exception e){
                logger.error("西南政法日志推送获取数据异常：{}",e);
            }finally {
                redisLock.unlock();
            }
        }

    }


    /**
     * 获取昨日每小时时间戳列表 返回列表包含24个时间戳
     * @return
     */
    private List<Long> timestampList(){
        List<Long> timestamps = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now().withMinute(0).withSecond(0).withNano(0).minusDays(1);
        for (int i = 0; i < 24; i++) {
            LocalDateTime start = now.withHour(i);
            LocalDateTime end = start.plusHours(1);
            long startTimestamp = start.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
            long endTimestamp = end.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli() - 1;
            timestamps.add(startTimestamp);
            timestamps.add(endTimestamp);
        }
        return timestamps;
    }

    private JSONObject fetchData(Long start,Long end){
        Map<String, Object> map = esDataService.webVisitData(WEBSITE_ID, start, end);
        JSONObject res = new JSONObject();
        res.put("total",map.get("total"));
        JSONArray results = new JSONArray();
        res.put("results",results);
        List<DataScan> visitDataList = (List<DataScan>) map.get("result");
        //获取instanceIds 用于批量查询引擎名称
        List<Integer> instanceIds = visitDataList.parallelStream().map(DataScan::getInstanceId).collect(Collectors.toList());
        Map<String, String> engineName = getEngineName(instanceIds);
        visitDataList.parallelStream().forEach(dataScan->{
            WebVisitDataDto webVisitDataDto = new WebVisitDataDto();
            BeanUtils.copyProperties(dataScan,webVisitDataDto);
            Integer instanceId = dataScan.getInstanceId();
            if(instanceId != null){
                try {
                    webVisitDataDto.setEngineName(removeHtmlTags(engineName.get(String.valueOf(instanceId))));
                }catch (Exception e){
                    webVisitDataDto.setEngineName("");
                }
            }
            results.add(webVisitDataDto);
        });
        return res;
    }

    private Map<String,String> getEngineName(List<Integer> instanceIds){
        Map<String,String> nameMap = new HashMap<>();
        try {
            UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(innerEngineDomain + "/engine2/internal/engine-instance/name/get");
            builder.queryParam("engineInstanceIds",instanceIds);
            com.alibaba.fastjson.JSONObject instanceRes = restTemplate.getForObject(builder.toUriString(), com.alibaba.fastjson.JSONObject.class);
            if(instanceRes.getString("code").equals("1")){
                nameMap = instanceRes.getObject("data", Map.class);
            }
        }catch (Exception e){
            return nameMap;
        }
        return nameMap;
    }
    private static String removeHtmlTags(String name) {
        if(name == null){
            return "";
        }
        return name.replaceAll("<[^>]*>", "");
    }


    private void pushLogs(JSONObject res){
        DateTimeFormatter ftf = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        JSONArray results = res.getJSONArray("results");
        //接口方要求 - 每批次推送数据量不超过2000
        Integer batch = (int)Math.ceil(results.size() / Double.valueOf(BATCH_SIZE));
        for (Integer i = 0; i < batch; i++) {
            JSONArray datas = new JSONArray();
            Integer batchEnd = (i*BATCH_SIZE + BATCH_SIZE) > results.size()? results.size() : (i*BATCH_SIZE + BATCH_SIZE);
            for (int j = i*BATCH_SIZE; j < batchEnd; j++) {
                JSONObject item = results.getJSONObject(j);
                JSONObject data = new JSONObject();
                if(Objects.isNull(item)){
                    continue;
                }
                if(!org.springframework.util.StringUtils.isEmpty(item.getString("uid"))){
                    data.put("uid",item.getInteger("uid"));
                }
                if(!org.springframework.util.StringUtils.isEmpty(item.getString("time"))){
                    Long time = item.getLong("time");
                    String formatTime = ftf.format(LocalDateTime.ofInstant(Instant.ofEpochMilli(time), ZoneId.systemDefault()));
                    data.put("time", formatTime);
                }
                if(!org.springframework.util.StringUtils.isEmpty(item.getString("deviceType"))){
                    data.put("deviceType",item.getInteger("deviceType"));
                }
                if(!org.springframework.util.StringUtils.isEmpty(item.getString("title"))){
                    data.put("title",item.getString("title"));
                }
                if(!org.springframework.util.StringUtils.isEmpty(item.getString("dataId")) && item.getInteger("dataId")!=0){
                    data.put("dataId",item.getInteger("dataId"));
                }
                if(!org.springframework.util.StringUtils.isEmpty(item.getString("typeId")) && item.getInteger("typeId")!=0){
                    data.put("typeId",item.getInteger("typeId"));
                }
                if(!org.springframework.util.StringUtils.isEmpty(item.getString("engineName"))){
                    data.put("engineName",item.getString("engineName"));
                }
                if(!StringUtils.isEmpty(item.getString("websiteId"))){
                    data.put("websiteId",item.getInteger("websiteId"));
                }
                datas.add(data);
            }
            push(datas);
        }
    }

    private void push(JSONArray datas){
        MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
        map.add("aid", "135");
        map.add("tableName", "dbcenter_doorvisit_135");
        map.add("type", "1");
        map.add("enc", "a2eaacdc2bf78f9e6251fbb21870027c");
        map.add("datas", datas.toJSONString());
        String url = "https://wisdom.chaoxing.com/dbcenter/api/push.action";
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);
        HttpEntity<MultiValueMap<String, String>> httpEntity = new HttpEntity<>(map, headers);
        try {
            ResponseEntity<JSONObject> responseEntity = restTemplate.exchange(url, HttpMethod.POST, httpEntity, JSONObject.class);
            JSONObject pushRes = responseEntity.getBody();
            if(pushRes.getString("statCode").equals("SUCCESS")){
            }else {
                logger.error("西南政法-推送失败；起始id:{},结束id:{},原因:{}",
                        datas.getJSONObject(0).getString("uid"),
                        datas.getJSONObject(datas.size()-1).getString("uid"),
                        pushRes.getString("message"));
            }
        }catch (Exception e){
            logger.error("西南政法-推送失败---{}",e);
        }

    }

}

