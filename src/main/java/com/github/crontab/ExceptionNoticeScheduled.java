package com.github.crontab;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.github.cache.redis.lock.RedisLock;
import com.github.mapper.WebsiteMapper;
import com.github.model.AbnormalInfo;
import com.github.model.Website;
import com.github.model.WebsiteConfig;
import com.github.model.vo.MessageVo;
import com.github.service.*;
import com.github.util.Constants;
import org.apache.commons.lang.StringUtils;
import org.redisson.Redisson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.*;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.client.RestTemplate;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.net.InetAddress;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @Author: zs
 * @Date: Created in 2022/8/8
 * @Description: 外部数据源超时异常通知
 */
@Component
public class ExceptionNoticeScheduled {

    @Value("${engine-inner.domain-name}")
    private String engineInnerDomainName;

    private String abnormalInfoUrl = ""; // 异常信息获取地址

    private String fusingUrl = ""; // 主动熔断地址
    private String existUrl = ""; // 是否熔断地址

    private final String lockKey = "abnormal:notice_scheduled";

    private final String abnormalInfoKey = "abnormal:info_60_m";

    private final String alreadyHandleIdsKey = "abnormal:already_handle_ids";

    private final String sendPhonePrefix = "abnormal:send_10m:";

    private final String abnormalCountPrefix = "abnormal:count_";

    private final Logger logger = LoggerFactory.getLogger(ExceptionNoticeScheduled.class);

    private final DateTimeFormatter format = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    private final DateTimeFormatter hourFormat = DateTimeFormatter.ofPattern("MM-dd:HH");

    private static final String WEB_SITE_ID_REGEX = "(\\\\\"websiteId\\\\\":)(.*?)(,)";

    private static final String WEB_SITE_ID_REGEX2 = "(\"websiteId\":)(.*?)(,)";

    // 统计 timeout 分钟范围内的接口超时次数
    private final static int timeout = 60;

    @Resource
    private Redisson redisson;

    @Resource
    private RestTemplate restTemplate;

    @Resource
    private WebsiteMapper websiteMapper;

    @Resource
    private UserService userService;

    @Resource
    private DictService dictService;

    @Resource
    private ChaoXingSmsService smsService;

    @Resource
    private RedisTemplate<String, AbnormalInfo> redisTemplate;

    @Resource
    private RedisTemplate<String, String> redisTemplateForString;
    @Resource
    private WebsiteConfigService websiteConfigService;
    @Resource
    private CxSpaceService cxSpaceService;

    @PostConstruct
    public void init() {
        abnormalInfoUrl = engineInnerDomainName + Constants.HEADER_ENGINE_V + "/request-abnormal-info/list-by-time?startTime=%s&endTime=%s";
        fusingUrl = engineInnerDomainName + Constants.HEADER_ENGINE_V + "/hystrix/add?url=%s";
        existUrl = engineInnerDomainName + Constants.HEADER_ENGINE_V + "/hystrix/exist?url=%s";
    }

    @Value("${isgray:false}")
    private Boolean isgray;

    @Value("${test.version:0}")
    private Integer testVersion;

    @Scheduled(cron = "0 0/1 * * * ?")
    public void task() {
        if (!isgray && testVersion == 0) {
            RedisLock redisLock = new RedisLock(redisson, lockKey);
            if (redisLock.tryLock()) {
                try {
                    LocalDateTime now = LocalDateTime.now();
                    String startTime = now.minusMinutes(2).format(format);
                    String endTime = now.format(format);
                    // 获取最近2分钟的异常信息列表
                    JSONObject resultObj = restTemplate.getForObject(String.format(abnormalInfoUrl, startTime, endTime), JSONObject.class);
                    if (resultObj != null && resultObj.containsKey("code") && resultObj.getInteger("code") == 1) {
                        List<Map<String, String>> resultList = resultObj.getObject("data", new TypeReference<List<Map<String, String>>>() {
                        });
                        if (resultList != null) {
                            // 处理主动熔断，两分钟内url出现20次超时则进行熔断
                            Map<String, Integer> urlCountMap = new HashMap<>();
                            StringBuilder sb = new StringBuilder();
                            for (Map<String, String> info : resultList) {
                                String url = info.get("url");
                                String exceptionInfo = info.get("exceptionInfo");
                                if (exceptionInfo.contains("Read timed out") || exceptionInfo.contains("connect timed out")) {
                                    int count = urlCountMap.getOrDefault(url, 0) + 1;
                                    if (count == 20) {
                                    /*restTemplate.getForObject(String.format(fusingUrl, url), JSONObject.class);
                                    logger.error("[{}] 2 分钟内超时 5 次，进行熔断操作", url);*/
                                        String webSiteId = getWebSiteIdByReg(exceptionInfo);
                                        sb.append("【url：").append(url);
                                        if (!"".equals(webSiteId)) {
                                            Website website = websiteMapper.get(Integer.parseInt(webSiteId));
                                            if (website != null) {
                                                sb.append("，websiteId：").append(webSiteId).append("，domain：").append("https://").append(website.getDomain()).append(".mh.chaoxing.com");
                                            }
                                        }
                                        sb.append("】");
                                    }
                                    urlCountMap.put(url, count);
                                }
                            }
                            if (StringUtils.isNotBlank(sb.toString())) {
                                sb.append("外接数据源2分钟内超时次数达到20次。");
                                try {
                                    InetAddress localHost = InetAddress.getLocalHost();
                                    sb.append(localHost.getHostAddress());
                                } catch (Exception e) {
                                    e.printStackTrace();
                                }
                                sentNotice("27450959,59044502,48114985,173808984,26691608,78448689", sb.toString());
                            }

                            // 统计超时次数
                            SetOperations<String, String> setOps = redisTemplateForString.opsForSet();
                            Set<String> members = setOps.members(alreadyHandleIdsKey);
                            // 过滤掉已经处理的数据
                            resultList = resultList.stream().filter(result -> !members.contains(result.get("id"))).collect(Collectors.toList());
                            resultList.parallelStream().forEach(info -> {
                                try {
                                    String url = info.get("url");
                                    String exceptionInfo = info.get("exceptionInfo");
                                    String createTime = info.get("createTime");

                                    // 只处理超时异常
                                    if (exceptionInfo.contains("Read timed out") || exceptionInfo.contains("connect timed out")) {
                                        String webSiteId = getWebSiteIdByReg(exceptionInfo);
                                        if (!"".equals(webSiteId)) {
                                            Website website = websiteMapper.get(Integer.parseInt(webSiteId));
                                            Integer createUser = website.getCreateUser();
                                            String userInfoStr = userService.getUserAll(String.valueOf(createUser));
                                            JSONObject userInfo = JSONObject.parseObject(userInfoStr);
                                            String phone = userInfo.getString("phone");
                                            String realName = userInfo.getString("realname");
                                            if (StringUtils.isBlank(realName)) {
                                                realName = String.valueOf(website.getCreateUser());
                                            }
                                            // 获取网站域名
                                            String domain = "https://" + website.getDomain() + ".mh.chaoxing.com";

                                            AbnormalInfo abnormalInfo = new AbnormalInfo().setUrl(url)
                                                    //.setExceptionInfo(exceptionInfo)
                                                    .setCreateTime(createTime).setWebSiteName(website.getName()).setDomain(domain).setCreateUserName(realName).setCreateUserPhone(phone);
                                            addAbnormalInfoToRedis(abnormalInfo);

                                            if (handleAbnormalCount(webSiteId, createTime) && handleNoticeStatus(webSiteId)) {
                                                // 给网站创建人发站内信
                                                sentNotice(createUser, website.getName(), domain, url);

                                                // 给网站创建人发
//                                            sendMessage(phone, website.getName(), domain);
                                                // 给门户产品发
//                                            sendMessage("***********", website.getName(), domain);
                                            }
                                        }
                                    }
                                } catch (Exception e) {
                                    logger.error("", e);
                                }
                            });
                            // 将已处理过的数据加入缓存
                            redisTemplateForString.delete(alreadyHandleIdsKey);
                            if (!CollectionUtils.isEmpty(resultList)) {
                                String[] ids = resultList.stream().map(result -> result.get("id")).toArray(String[]::new);
                                setOps.add(alreadyHandleIdsKey, ids);
                            }
                        }
                    }
                    // 清除过期数据
                    handleExpiredData(now);
                } catch (Exception e) {
                    logger.error("异常通知定时任务执行异常", e);
                } finally {
                    redisLock.unlock();
                }
            }
        }
    }

    private boolean handleNoticeStatus(String webSiteId) {
        WebsiteConfig websiteConfig = websiteConfigService.get(Integer.valueOf(webSiteId));
        if (ObjectUtils.isEmpty(websiteConfig)) {
            return false;
        } else {
            return Boolean.TRUE.equals(websiteConfig.getIsNotice());
        }
    }

    /**
     * 分页查询异常信息
     */
    public Map<String, Object> getAbnormalInfoList(Integer pageNum, Integer pageSize) {
        int startIndex = (pageNum - 1) * pageSize;
        int endIndex = startIndex + pageSize;
        ZSetOperations<String, AbnormalInfo> zSetOps = redisTemplate.opsForZSet();
        Long count = zSetOps.zCard(abnormalInfoKey);
        List<AbnormalInfo> abnormalInfoList = new ArrayList<>();
        Set<ZSetOperations.TypedTuple<AbnormalInfo>> tuples = zSetOps.reverseRangeWithScores(abnormalInfoKey, startIndex, endIndex);
        if (tuples != null) {
            tuples.forEach(tuple -> {
                long score = new BigDecimal(tuple.getScore()).longValue();
                long timestamp = Long.parseLong(String.valueOf(score).substring(0, 13));
                int num = Integer.parseInt(String.valueOf(score).substring(13, 15));
                String createTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(timestamp), ZoneOffset.ofHours(8)).format(format);
                AbnormalInfo abnormalInfo = tuple.getValue();
                if (abnormalInfo != null) {
                    abnormalInfo.setCreateTime(createTime);
                    abnormalInfo.setNum(num);
                    abnormalInfoList.add(abnormalInfo);
                }
            });
        }

        abnormalInfoList.parallelStream().forEach(abnormalInfo -> {
            JSONObject response = restTemplate.getForObject(String.format(existUrl, abnormalInfo.getUrl()), JSONObject.class);
            if (response != null && response.get("data") != null && String.valueOf(response.get("data")).equals("true")) {
                abnormalInfo.setStatus((byte) 1);
            } else {
                abnormalInfo.setStatus((byte) 0);
            }
        });

        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("rows", abnormalInfoList);
        resultMap.put("total", count);
        return resultMap;
    }

    /**
     * 清除 timeout 分钟之前的数据
     */
    private void handleExpiredData(LocalDateTime now) {
        long score = now.minusMinutes(timeout).toInstant(ZoneOffset.ofHours(8)).toEpochMilli();
        redisTemplate.opsForZSet().removeRangeByScore(abnormalInfoKey, 0, score);
    }

    /**
     * 将异常信息存入redis
     */
    private void addAbnormalInfoToRedis(AbnormalInfo abnormalInfo) {
        synchronized (abnormalInfo.getUrl().intern()) {
            long score = LocalDateTime.parse(abnormalInfo.getCreateTime(), format).toInstant(ZoneOffset.ofHours(8)).toEpochMilli();
            // 不存时间，从score中获取
            abnormalInfo.setCreateTime("");
            ZSetOperations<String, AbnormalInfo> zSetOps = redisTemplate.opsForZSet();
            Double storeScore = zSetOps.score(abnormalInfoKey, abnormalInfo);
            if (storeScore == null) {
                // 为空则表示第一次出现该异常
                score = Long.parseLong(score + "01"); // score的前13位为时间戳，后两位为异常发生的次数
                zSetOps.add(abnormalInfoKey, abnormalInfo, score);
            } else {
                long longScore = new BigDecimal(storeScore).longValue();
                // 不为空则表示已经出现过该异常，则更新时间戳，并增加次数
                String num = String.valueOf(longScore).substring(13, 15);
                // 最大支持99
                if (!"99".equals(num)) {
                    num = String.format("%02d", Integer.parseInt(num) + 1);
                }
                // 一小时内触发超时达到指定次数，进行主动熔断

                // 取消主动操作，改为超管后台人工熔断

                /*String timeOutNum = "10";
                Dict dict = dictService.getDictByCode("time_out_num");
                if (dict != null) {
                    timeOutNum = String.format("%02d", Integer.parseInt(dict.getValue()));
                }
                if (timeOutNum.equals(num)) {
                    // JSONObject resultObj = restTemplate.getForObject(String.format(fusingUrl, abnormalInfo.getUrl()), JSONObject.class);
                    // logger.info("[{}]熔断结果：{}", abnormalInfo.getUrl(), resultObj);
                    logger.error("[{}]当前超时次数[{}]，以达到阈值[{}]，进行熔断操作", abnormalInfo.getUrl(), num, timeOutNum);
                }*/
                score = Long.parseLong(score + num);
                // 计算差值，增加分数
                zSetOps.incrementScore(abnormalInfoKey, abnormalInfo, score - longScore);
            }
        }
    }

    /**
     * 正则获取websiteId
     */
    private String getWebSiteIdByReg(String exceptionInfo) {
        Pattern pattern = Pattern.compile(WEB_SITE_ID_REGEX);
        Matcher matcher = pattern.matcher(exceptionInfo);
        if (matcher.find()) {
            return matcher.group(2);
        }
        Pattern pattern2 = Pattern.compile(WEB_SITE_ID_REGEX2);
        Matcher matcher2 = pattern2.matcher(exceptionInfo);
        if (matcher2.find()) {
            return matcher2.group(2);
        }
        return "";
    }

    /**
     * 异常次数处理，一小时内出现3次异常发送短息
     */
    private boolean handleAbnormalCount(String webSiteId, String createTime) {
        // 当前这个小时的缓存key
        String suffix = LocalDateTime.parse(createTime, format).format(hourFormat);
        String hashKey = abnormalCountPrefix + suffix;
        // 判断当前小时的缓存key是否存在，不存在则创建缓存并设置1小时的过期时间
        Boolean hasKey = redisTemplateForString.hasKey(hashKey);
        HashOperations<String, String, String> hashOps = redisTemplateForString.opsForHash();
        Long count = hashOps.increment(hashKey, webSiteId, 1);
        // 设置过期时间
        if (Boolean.FALSE.equals(hasKey)) {
            redisTemplateForString.expire(hashKey, 5, TimeUnit.MINUTES);
        }
        // 当前小时段第一次出现3次异常，需要发送短信通知
        return count == 3;
    }

    /**
     * 发送短信
     */
    private void sendMessage(String phone, String webSiteName, String domain) {
        if (StringUtils.isNotBlank(phone)) {
            ValueOperations<String, AbnormalInfo> stringOps = redisTemplate.opsForValue();
            // 10分钟之内只发一次消息
            if (Boolean.TRUE.equals(stringOps.setIfAbsent(sendPhonePrefix + phone, null, 10, TimeUnit.MINUTES))) {
                // String result = smsService.sendExceptionMessage(phone, webSiteName, domain);
                logger.error("ExceptionNoticeScheduled,给[{}]发送短信,网站名称:[{}],网站域名:[{}]", phone, webSiteName, domain);
            }
        }
    }

    /**
     * 发送站内信
     *
     * @param uid
     * @param websiteName
     * @param domain
     */
    private void sentNotice(Integer uid, String websiteName, String domain, String url) {
        MessageVo messageVo = new MessageVo();
        messageVo.setTitle("网站接口超时通知");
        messageVo.setContent(String.format("目前检测到您的网站：%s，域名：%s 存在外接数据源：%s 请求超时，请您及时检查网站内容是否显示正常。如有疑问请联系门户产品人员和外接数据源提供者。", websiteName, domain, url));
        messageVo.setSendUid(String.valueOf(26691608));
        messageVo.setRecvUids(String.valueOf(uid));
        cxSpaceService.sendMessage(messageVo, null);
    }

    private void sentNotice(String uid, String msg) {
        MessageVo messageVo = new MessageVo();
        messageVo.setTitle("网站接口超时通知");
        messageVo.setContent(msg);
        messageVo.setSendUid(String.valueOf(26691608));
        messageVo.setRecvUids(uid);
        cxSpaceService.sendMessage(messageVo, null);
    }

}
