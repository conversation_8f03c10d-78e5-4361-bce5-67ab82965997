package com.github.interceptor;

import com.github.enums.ErrorCode;
import com.github.model.Page;
import com.github.service.PageService;
import com.github.service.RoleService;
import com.github.service.UserAppPermissionService;
import com.github.util.Constants;
import com.github.util.CookieUtils;
import com.github.util.RestResponse;
import com.github.util.exception.BusinessException;
import org.apache.commons.lang.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.util.Objects;
import java.util.Optional;

/**
 * 仅拦截了1.0的admin相关操作
 * 根据入参有websiteId 或 PageId 判断是否网站管理员
 */
@Aspect
@Component
public class V1AdminAspect {

    @Resource
    private PageService pageService;
    @Resource
    private UserAppPermissionService userAppPermissionService;
    @Resource
    private RoleService roleService;
    public static final String PAGE_THEME_NAME = "pageTheme";
    public static final String WEBSITE_ID = "websiteId";
    public static final String PAGE_ID = "pageId";

    @Pointcut("execution(* com.github.controller.auth.PageThemeAuthController.*(..))")
    public void pointcut() {
    }

    @Around("pointcut()")
    public Object handle(ProceedingJoinPoint pjp) throws Throwable {
        if (this.isAdmin(pjp)) {
            return pjp.proceed();
        }

        Signature signature = pjp.getSignature();
        if (signature instanceof MethodSignature) {
            MethodSignature methodSignature = (MethodSignature) signature;
            // 被切的方法
            Method method = methodSignature.getMethod();
            // 返回类型
            Class<?> methodReturnType = method.getReturnType();
            // 实例化
            Object o = methodReturnType.newInstance();
            if (o instanceof String) {
                return "error/401";
            } else {
                return RestResponse.error("非法访问");
            }
        }
        return pjp.proceed();
    }

    public boolean isAdmin(ProceedingJoinPoint joinPoint) throws Exception {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attributes.getRequest();

        // 主题包直接放行
        boolean pageTheme = request.getRequestURL().toString().contains(PAGE_THEME_NAME);
        Integer uid = CookieUtils.getCookieInt(request, Constants.COOKIE_UID);
        if (pageTheme && roleService.isSuperAdmin(String.valueOf(uid))) {
            return true;
        }

        // 正常参数匹配
        String[] argNames = ((MethodSignature) joinPoint.getSignature()).getParameterNames(); // 参数名
        Object[] args = joinPoint.getArgs(); // 参数值
        Integer websiteId = Constants.STATUS_FALSE;
        Integer pageId = Constants.STATUS_FALSE;
        for (int i = 0; i < argNames.length; i++) { // 取w来判断是否管理员
            if (StringUtils.equals(WEBSITE_ID, argNames[i])) {
                if (Objects.isNull(args[i])) {
                    return false;
                }
                websiteId = Integer.valueOf(String.valueOf(args[i]));
                break;
            } else if (StringUtils.equals(PAGE_ID, argNames[i])) {
                if (Objects.isNull(args[i])) {
                    return false;
                }
                pageId = Integer.valueOf(String.valueOf(args[i]));
                Page page = pageService.get(pageId);
                Optional.ofNullable(page).orElseThrow(() -> new BusinessException(ErrorCode.CODE_DATA_ERROR.getCode()));
                websiteId = page.getWebsiteId();
                break;
            }
        }
        if (Constants.STATUS_FALSE.equals(websiteId)) {
            websiteId = CookieUtils.getCookieInt(request, Constants.COOKIE_CURRENT_WEB_ID);
        }
        if (Constants.STATUS_FALSE.equals(pageId)){
            pageId = null;
        }
        // auth目录已验证cookie, 如果传了pageId做入参，按pageId来判断权限
        return userAppPermissionService.isWebsitePageAdmin(uid, websiteId, pageId, true);
    }
}
