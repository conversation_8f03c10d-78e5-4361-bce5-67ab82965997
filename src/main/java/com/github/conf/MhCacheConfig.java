package com.github.conf;

import com.github.benmanes.caffeine.cache.CaffeineSpec;
import com.github.cache.layering.LayeringCacheManager;
import com.github.cache.redis.serializer.KryoRedisSerializer;
import com.github.cache.setting.FirstCacheSetting;
import com.github.cache.setting.SecondaryCacheSetting;
import com.github.util.Constants;
import org.redisson.api.RedissonClient;
import org.redisson.spring.cache.CacheConfig;
import org.redisson.spring.cache.RedissonSpringCacheManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.cache.CacheProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.CachingConfigurerSupport;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheWriter;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.RedisSerializationContext;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.springframework.util.StringUtils;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Configuration
@EnableConfigurationProperties(CacheProperties.class)
public class MhCacheConfig extends CachingConfigurerSupport {

    // redis缓存的有效时间单位是秒
    @Value("${redis.default.expiration}")
    private long redisDefaultExpiration;

    // 查询缓存有效时间
    @Value("${select.cache.timeout}")
    private long selectCacheTimeout;
    // 查询缓存自动刷新时间
    @Value("${select.cache.refresh}")
    private long selectCacheRefresh;

    @Value("${project.ismirror:false}")
    private boolean isMirror;

    @Autowired
    private CacheProperties cacheProperties;

    @Bean(name = "redisTemplate")
    public RedisTemplate<Object, Object> redisTemplate(RedisConnectionFactory redisConnectionFactory) {
        RedisTemplate<Object, Object> template = new RedisTemplate<Object, Object>();
        KryoRedisSerializer serializer = new KryoRedisSerializer<>(Object.class);
        // value值的序列化采用fastJsonRedisSerializer
        template.setValueSerializer(serializer);
        // hash Map 对象序列化采用String方式进行，
        template.setHashValueSerializer(new StringRedisSerializer());

        // key的序列化采用StringRedisSerializer
        template.setKeySerializer(new StringRedisSerializer());
        template.setHashKeySerializer(new StringRedisSerializer());

        template.setConnectionFactory(redisConnectionFactory);
        return template;
    }

    @Bean
    @Primary
    public CacheManager cacheManager(RedissonClient redisson, RedisConnectionFactory redisConnectionFactory, RedisCacheConfiguration redisCacheConfiguration) {
        RedisCacheWriter redisCacheWriter = RedisCacheWriter.nonLockingRedisCacheWriter(redisConnectionFactory);

        LayeringCacheManager layeringCacheManager = new LayeringCacheManager(redisson ,redisCacheWriter,redisCacheConfiguration);
        // Caffeine缓存设置
        setFirstCacheConfig(layeringCacheManager);
        // redis缓存设置
        setSecondaryCacheConfig(layeringCacheManager);
        // 允许存null，防止缓存穿透
        layeringCacheManager.setAllowNullValues(true);
        return layeringCacheManager;
    }

//    @Bean
//    public CacheManager cacheManager(RedissonClient redisson) {
//        Map<String, CacheConfig> config = new HashMap<String, CacheConfig>();
//        // 创建一个名称为"ObjectCache"的缓存，过期时间ttl为60分钟，同时最长空闲时maxIdleTime为12分钟。
//        config.put(Constants.CACHE_NAME, new CacheConfig(2 * 60 * 60 * 1000, 12 * 60 * 1000));
//        return new RedissonSpringCacheManager(redisson,config);
//    }

    /**
     *  设置@cacheable 序列化方式
     *  @cacheable 设置过期时间2小时
     *  @return
     */
    @Bean
    public RedisCacheConfiguration redisCacheConfiguration(){
        KryoRedisSerializer serializer = new KryoRedisSerializer<>(Object.class);
        RedisCacheConfiguration configuration = RedisCacheConfiguration.defaultCacheConfig();

        // 修改默认配置的序列化， 缓存空值，使用前缀等
        configuration = configuration.serializeValuesWith(RedisSerializationContext.SerializationPair.fromSerializer(serializer)).entryTtl(Duration.ofHours(2));
        configuration = configuration.entryTtl(Duration.ofSeconds(redisDefaultExpiration));
        return configuration;
    }


    private void setFirstCacheConfig(LayeringCacheManager layeringCacheManager) {
        // 设置默认的一级缓存配置
        String specification = this.cacheProperties.getCaffeine().getSpec();
        if (StringUtils.hasText(specification)) {
            layeringCacheManager.setCaffeineSpec(CaffeineSpec.parse(specification));
        }

        // 设置每个一级缓存的过期时间和自动刷新时间
        Map<String, FirstCacheSetting> firstCacheSettings = new HashMap<String, FirstCacheSetting>();
        firstCacheSettings.put(Constants.CACHE_NAME, new FirstCacheSetting("initialCapacity=1000,maximumSize=20000,expireAfterWrite="+(redisDefaultExpiration-selectCacheRefresh)+"s"));
        layeringCacheManager.setFirstCacheSettings(firstCacheSettings);
    }

    private void setSecondaryCacheConfig(LayeringCacheManager layeringCacheManager) {
        //这里可以设置一个默认的过期时间 单位是秒
        layeringCacheManager.setSecondaryCacheDefaultExpiration(redisDefaultExpiration);

        // 设置每个二级缓存的过期时间和自动刷新时间
        Map<String, SecondaryCacheSetting> secondaryCacheSettings = new HashMap<>();
        secondaryCacheSettings.put(Constants.CACHE_NAME, new SecondaryCacheSetting(selectCacheTimeout, selectCacheRefresh,false, false));
        layeringCacheManager.setSecondaryCacheSettings(secondaryCacheSettings);
    }

}
