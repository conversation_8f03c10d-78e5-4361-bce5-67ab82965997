package com.github.cache.redis.queue;

import org.redisson.api.RBlockingDeque;
import org.redisson.api.RDelayedQueue;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * @className RedissonQueue
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/7/7 16:37
 * @Version 1.0
 **/
@Component
public class RedissonQueue {

    @Resource
    RedissonClient redisson;

    /**
     * 添加数据到延迟队列中
     * @param value
     * @param delay
     * @param timeUnit
     * @param <T>
     */
    public <T> void delayQueue(T value, Long delay, TimeUnit timeUnit,String name){
        RBlockingDeque<Object> blockingDeque = redisson.getBlockingDeque(name);
        RDelayedQueue<Object> delayedQueue = redisson.getDelayedQueue(blockingDeque);
        delayedQueue.offer(value, delay, timeUnit);
    }

    public <T> T getQueue(String name) throws InterruptedException {
        RBlockingDeque<Object> blockingDeque = redisson.getBlockingDeque(name);
//        T value = (T) blockingDeque.takeFirst();
        T value = (T) blockingDeque.poll();
        return value;
    }
}
