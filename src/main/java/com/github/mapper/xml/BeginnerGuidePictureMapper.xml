<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.github.mapper.BeginnerGuideMapper">

    <resultMap id="BaseResultMap" type="com.github.model.BeginnerGuidePicture">
        <result property="id" column="id"/>
        <result property="websiteId" column="website_id"/>
        <result property="pictureType" column="picture_type"/>
        <result property="pictureUrl" column="picture_url"/>
    </resultMap>

    <insert id="add" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO t_beginner_guide_picture (
        	id,
        	website_id,
        	picture_type,
        	picture_url
        ) VALUES (
        	#{id},
        	#{websiteId},
        	#{pictureType},
        	#{pictureUrl}
        )
	</insert>

    <delete id="deleteByWebsiteId">
        DELETE FROM t_beginner_guide_picture WHERE
        website_id = #{websiteId}
	</delete>

    <delete id="deleteById">
        DELETE FROM t_beginner_guide_picture WHERE
        id = #{id}
	</delete>


    <select id="getByWebsiteId" resultMap="BaseResultMap">
        SELECT *
		    FROM t_beginner_guide_picture
	        WHERE
		        website_id = #{websiteId}
		    AND
		        picture_type = #{type}
    </select>

</mapper>
