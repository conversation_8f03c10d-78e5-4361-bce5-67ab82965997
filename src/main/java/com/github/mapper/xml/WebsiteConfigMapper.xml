<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.github.mapper.WebsiteConfigMapper">

    <resultMap id="BaseResultMap" type="com.github.model.WebsiteConfig">
        <result property="id" column="id"/>
        <result property="websiteId" column="website_id"/>
        <result property="customDomainEditor" column="custom_domain_editor"/>
        <result property="openQuery" column="open_query"/>
        <result property="unitPwdLogin" column="unit_pwd_login"/>
        <result property="v2TestWebsite" column="v2_test_website"/>
        <result property="isNotice" column="is_notice"/>
        <result property="visitCheck" column="visit_check"/>
        <result property="offStatus" column="off_status"/>
        <result property="upOffTime" column="up_off_time"/>
        <result property="dataCountMode" column="data_count_mode"/>
        <result property="shareCover" column="share_cover"/>
        <result property="quoteTopBottom" column="quote_top_bottom"/>
        <result property="maxOnlineDev" column="max_online_dev"/>
        <result property="wxMiniUrl" column="wx_mini_url"/>
        <result property="otherConfig" column="other_config" typeHandler="com.github.conf.MybatisJsonHandler"/>
    </resultMap>

    <!-- 用于select查询公用抽取的列 -->
    <sql id="com.github.mapper.WebsiteConfigMapper.columns">
	<![CDATA[
        id, website_id, custom_domain_editor,open_query,unit_pwd_login,v2_test_website,is_notice, visit_check, off_status,
            data_count_mode, share_cover,quote_top_bottom, max_online_dev, up_off_time, wx_mini_url, other_config
        ]]>
	</sql>

    <insert id="saveOrUpdate" >
        <selectKey keyProperty="count" resultType="int" order="BEFORE">
            select count(*) from t_website_config where website_id = #{websiteId}
        </selectKey>
        <if test="count > 0">
            update t_website_config
            set custom_domain_editor = #{customDomainEditor},
                open_query = #{openQuery},
                visit_check = #{visitCheck},
                v2_test_website = #{v2TestWebsite},
                unit_pwd_login = #{unitPwdLogin},
                off_status = #{offStatus},
                up_off_time = #{upOffTime},
                is_notice=#{isNotice},
                data_count_mode = #{dataCountMode},
                share_cover = #{shareCover},
                max_online_dev = #{maxOnlineDev},
                wx_mini_url = #{wxMiniUrl},
                quote_top_bottom = #{quoteTopBottom},
                other_config = #{otherConfig}
            where website_id = #{websiteId}
        </if>
        <if test="count==0">
            insert into t_website_config(
                website_id,
                custom_domain_editor,
                open_query,
                v2_test_website,
                unit_pwd_login,
                off_status,
                up_off_time,
                visit_check,
                is_notice,
                data_count_mode,
                share_cover,
                max_online_dev,
                wx_mini_url,
                quote_top_bottom,
                other_config
            )values(
                 #{websiteId},
                 #{customDomainEditor},
                 #{openQuery},
                 #{v2TestWebsite},
                 #{unitPwdLogin},
                 #{offStatus},
                 #{upOffTime},
                 #{visitCheck},
                 #{isNotice},
                 #{dataCountMode},
                 #{shareCover},
                 #{maxOnlineDev},
                 #{wxMiniUrl},
                 #{quoteTopBottom},
                 #{otherConfig}
            )
        </if>
    </insert>

    <select id="get" resultMap="BaseResultMap">
        select <include refid="com.github.mapper.WebsiteConfigMapper.columns"></include>
        from t_website_config
        where website_id = #{websiteId}
        limit 1
    </select>

    <select id="list" resultMap="BaseResultMap">
        select <include refid="com.github.mapper.WebsiteConfigMapper.columns"></include>
        from t_website_config
        where website_id IN
        <foreach collection="websiteIds" index="key" item="websiteId" separator="," open="(" close=")">
            #{websiteId}
        </foreach>
    </select>
</mapper>
