<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.github.mapper.UserAppPermissionNewMapper">

	<resultMap id="BaseResultMap" type="com.github.model.UserAppPermissionNew">
        <result property="id" column="id"/>
        <result property="wfwfid" column="wfwfid"/>
        <result property="uid" column="uid"/>
        <result property="name" column="name"/>
        <result property="apps" column="apps"/>
        <result property="pageId" column="page_id"/>
        <result property="webId" column="web_id"/>
        <result property="roleFlag" column="role_flag"/>
        <result property="type" column="type"/>
		<result property="mhRoleId" column="mh_role_id"/>
	</resultMap>
	
	<!-- 用于select查询公用抽取的列 -->
	<sql id="com.github.mapper.UserAppPermissionNewMapper.columns">
	<![CDATA[
		id, wfwfid, uid, name, apps,page_id,web_id,role_flag,type,mh_role_id
	]]>
	</sql>

	<!-- useGeneratedKeys="true" keyProperty="xxx" for sqlserver and mysql -->
	<!-- useGeneratedKeys="true" keyProperty="xxx" for sqlserver and mysql -->
	<insert id="add" useGeneratedKeys="true" keyProperty="id">
		<![CDATA[
        INSERT INTO t_user_app_permission_new (
        	id,
        	wfwfid,
        	uid,
        	name,
        	apps,
        	page_id,
        	web_id,
        	type,
        	role_flag,
			mh_role_id
        ) VALUES (
        	#{id},
        	#{wfwfid},
        	#{uid},
        	#{name},
        	#{apps},
        	#{pageId},
        	#{webId},
        	#{type},
        	#{roleFlag},
			#{mhRoleId}
        )
    ]]>
	</insert>

	<delete id="delete">
		<![CDATA[
        DELETE FROM t_user_app_permission_new WHERE
        id = #{id}
    ]]>
	</delete>

	<delete id="deleteByFidUidWid">
		DELETE FROM t_user_app_permission_new WHERE
		wfwfid = #{fid} and uid = #{uid} and web_id = #{webId} and type = #{type}
	</delete>
    <delete id="deleteByUidAndNotInPageIdList">
        DELETE FROM t_user_app_permission_new WHERE
        uid = #{uid} and web_id = #{websiteId} and page_id not in
        <foreach collection="pageIdList" index="key" item="value" open="(" close=")" separator=",">
            #{value}
        </foreach>
    </delete>

    <select id="get" resultMap="BaseResultMap">
		SELECT <include refid="com.github.mapper.UserAppPermissionNewMapper.columns" />
		<![CDATA[
		    FROM t_user_app_permission_new
	        WHERE
		        id = #{id}
	    ]]>
	</select>

	<select id="deleteByPageId">
		DELETE FROM t_user_app_permission_new
	    WHERE page_id = #{id}
	</select>

	<update id="update" >
    <![CDATA[
        UPDATE t_user_app_permission_new SET
	        wfwfid = #{wfwfid}, 
	        uid = #{uid}, 
	        name = #{name},
	        apps = #{apps},
	        type = #{type},
	        role_flag = #{roleFlag}
        WHERE
	        id = #{id} 
    ]]>
	</update>

	<select id="getByFidUid" resultMap="BaseResultMap">
		SELECT <include refid="com.github.mapper.UserAppPermissionNewMapper.columns" />
		<![CDATA[
		    FROM t_user_app_permission_new
	        WHERE
		        wfwfid = #{fid} and uid = #{uid} and page_id = #{pageId} and web_id = #{webId} and type = #{type} limit 1
	    ]]>
	</select>

	<select id="getByFidUidWid" resultMap="BaseResultMap">
		SELECT <include refid="com.github.mapper.UserAppPermissionNewMapper.columns" />
		<![CDATA[
		    FROM t_user_app_permission_new
	        WHERE
		        wfwfid = #{fid} and uid = #{uid} and web_id = #{webId} and type = #{type}
	    ]]>
	</select>

	<select id="listByWebId" resultMap="BaseResultMap">
		SELECT <include refid="com.github.mapper.UserAppPermissionNewMapper.columns" /> FROM t_user_app_permission_new
		where web_id = #{websiteId}
	</select>

	<select id="getListAppPermission" resultMap="BaseResultMap">
		SELECT <include refid="com.github.mapper.UserAppPermissionNewMapper.columns" /> FROM t_user_app_permission_new
		where web_id = #{websiteId} and type = #{type}
	</select>

	<select id="getListAppPermissionNoRole" resultMap="BaseResultMap">
		SELECT <include refid="com.github.mapper.UserAppPermissionNewMapper.columns" /> FROM t_user_app_permission_new
		where web_id = #{websiteId} and type = #{type} and mh_role_id is null
	</select>

	<select id="getListAppPermissionDistinc" resultMap="BaseResultMap">
		SELECT <include refid="com.github.mapper.UserAppPermissionNewMapper.columns" /> FROM t_user_app_permission_new
		where web_id = #{websiteId} and type = #{type} group by uid
	</select>

	<select id="getListAppPermissionSuperDistinc" resultMap="BaseResultMap">
		SELECT <include refid="com.github.mapper.UserAppPermissionNewMapper.columns" /> FROM t_user_app_permission_new
		where web_id = #{websiteId} and role_flag in (1,2) and type = #{type} group by uid
	</select>

	<select id="getByFidUidWidPageId" resultMap="BaseResultMap">
		SELECT <include refid="com.github.mapper.UserAppPermissionNewMapper.columns" />
		<![CDATA[
		    FROM t_user_app_permission_new
	        WHERE
		        wfwfid = #{fid} and uid = #{uid} and web_id = #{webId} and page_id = #{pageId} and type = #{type}
	    ]]>
	</select>

    <select id="getListByWfwfid" resultMap="BaseResultMap">
		SELECT <include refid="com.github.mapper.UserAppPermissionNewMapper.columns" />
		<![CDATA[
		    FROM t_user_app_permission_new
	        WHERE
		        wfwfid = #{fid} and uid = #{uid} and type = #{type}
	    ]]>
	</select>
	<select id="getByFidURoleIdWebsiteId" resultMap="BaseResultMap">
		SELECT <include refid="com.github.mapper.UserAppPermissionNewMapper.columns" />
		<![CDATA[
		    FROM t_user_app_permission_new
	        WHERE
		        wfwfid = #{fid} and mh_role_id = #{mhRoleId} and web_id = #{websiteId} and type = #{type}
	    ]]>
	</select>
	<select id="getListByWfwfidRoleId" resultMap="BaseResultMap">
		SELECT <include refid="com.github.mapper.UserAppPermissionNewMapper.columns" />
		<![CDATA[
		    FROM t_user_app_permission_new
	        WHERE
		        wfwfid = #{fid} and mh_role_id = #{mhRoleId} and type = #{type}
	    ]]>
	</select>

	<select id="getByFidWid" resultMap="BaseResultMap">
		SELECT <include refid="com.github.mapper.UserAppPermissionNewMapper.columns" />
		<![CDATA[
		    FROM t_user_app_permission_new
	        WHERE
		        wfwfid = #{fid} and web_id = #{websiteId} and type = #{type}
	    ]]>
	</select>

    <select id="getSuperAdminUap" resultMap="BaseResultMap">
        SELECT <include refid="com.github.mapper.UserAppPermissionNewMapper.columns" />
        from t_user_app_permission_new
        where uid = #{uid} and web_id = #{websiteId} and role_flag &lt;= 2 limit 1
    </select>
    <update id="updateByFidUidWidRoleFlag">
		UPDATE t_user_app_permission_new
		SET uid = #{newCreateUser}, `name` = #{newCreateUserName}
		WHERE
		wfwfid = #{wfwfid}
		AND uid = #{oldCreateUser}
		AND web_id = #{websiteId}
		AND role_flag = #{roleFlag}
		and type = #{type}
	</update>
	<update id="updateByFidUid">
		UPDATE t_user_app_permission_new
		SET `name` = #{name}
		WHERE
		wfwfid = #{wfwfid}
		AND uid = #{uid}
	</update>
    <update id="updateUidByFid">
        update  t_user_app_permission_new set uid = #{newUid} where wfwfid = #{fid} and uid = #{oldUid}
    </update>
    <update id="updateUidByWebId">
        update  t_user_app_permission_new set uid = #{newUid} where web_id = #{webId} and uid = #{oldUid}
    </update>
    <update id="updateApps">
        update  t_user_app_permission_new set apps = #{apps} where id = #{id}
    </update>
    <select id="getByWfwfidAndWebsiteIdAndUid" resultMap="BaseResultMap">
        SELECT <include refid="com.github.mapper.UserAppPermissionNewMapper.columns" />
        from t_user_app_permission_new
        where wfwfid = #{wfwfid} and web_id = #{websiteId} and uid = #{uid} limit 1
    </select>
    <select id="getApps" resultMap="BaseResultMap">
        select id, apps from t_user_app_permission_new
    </select>
</mapper>
