<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.github.mapper.WebsiteMenuMapper">

	<resultMap id="BaseResultMap" type="com.github.model.WebsiteMenu">
        <result property="id" column="id"/>
        <result property="websiteId" column="website_id"/>
        <result property="name" column="name"/>
        <result property="url" column="url"/>
        <result property="status" column="status"/>
	</resultMap>
	
	<!-- 用于select查询公用抽取的列 -->
	<sql id="com.github.mapper.WebsiteMenuMapper.columns">
	<![CDATA[
		id, website_id, name, url, status
	]]>
	</sql>

	<!-- useGeneratedKeys="true" keyProperty="xxx" for sqlserver and mysql -->
	<insert id="add" useGeneratedKeys="true" keyProperty="id">
    <![CDATA[
        INSERT INTO t_website_menu (
        	id, 
        	website_id, 
        	name, 
        	url, 
        	status
        ) VALUES (
        	#{id}, 
        	#{websiteId}, 
        	#{name}, 
        	#{url}, 
        	#{status}
        )
    ]]>
	</insert>
    
	<update id="update" >
    <![CDATA[
        UPDATE t_website_menu SET
	        website_id = #{websiteId}, 
	        name = #{name}, 
	        url = #{url}, 
	        status = #{status}
        WHERE 
	        id = #{id} 
    ]]>
	</update>

    <delete id="delete">
    <![CDATA[
        DELETE FROM t_website_menu WHERE
        id = #{id} 
    ]]>
    </delete>
    
    <select id="get" resultMap="BaseResultMap">
		SELECT <include refid="com.github.mapper.WebsiteMenuMapper.columns" />
	    <![CDATA[
		    FROM t_website_menu 
	        WHERE 
		        id = #{id} 
	    ]]>
	</select>

	<select id="getList" resultMap="BaseResultMap">
		SELECT <include refid="com.github.mapper.WebsiteMenuMapper.columns" /> FROM t_website_menu
	</select>

	<select id="getWebsiteMenuList" resultMap="BaseResultMap">
		SELECT <include refid="com.github.mapper.WebsiteMenuMapper.columns" /> FROM t_website_menu
        <where>
           website_id = #{websiteId} and status = 1 limit 20
        </where>
	</select>
	<!--***************************************************************-->

</mapper>
