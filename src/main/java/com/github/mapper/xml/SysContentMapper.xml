<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.github.mapper.SysContentMapper">

	<resultMap id="BaseResultMap" type="com.github.model.SysContent">
        <result property="id" column="id"/>
        <result property="moduleId" column="module_id"/>
        <result property="seq" column="seq"/>
        <result property="typeId" column="type_id"/>
        <result property="appId" column="app_id"/>
        <result property="path" column="path"/>
        <result property="publicId" column="public_id"/>
	</resultMap>

    <!-- 用于select查询公用抽取的列 -->
	<sql id="com.github.mapper.SysContentMapper.columns">
	<![CDATA[
		id,  module_id, seq, type_id, app_id, `path`, public_id
	]]>
	</sql>
    <select id="getByContentId" resultMap="BaseResultMap">
        SELECT <include refid="com.github.mapper.SysContentMapper.columns" />
        <![CDATA[
		    FROM t_sys_module
	        WHERE
		        module_id = #{contentId}
	    ]]>
    </select>

	<!-- useGeneratedKeys="true" keyProperty="xxx" for sqlserver and mysql -->
	<insert id="add" useGeneratedKeys="true" keyProperty="id">
    <![CDATA[
        INSERT INTO t_sys_module (
        	id, 
        	type_id,
        	app_id,
        	path,
            seq,
            public_id,
            module_id
        ) VALUES (
        	#{id}, 
        	#{typeId},
        	#{appId}, 
        	#{path},
            #{seq},
            #{publicId},
            #{moduleId}
        )
    ]]>
	</insert>
    
	<update id="update">
    <![CDATA[
        UPDATE t_sys_module SET
	        path = #{path},
	        type_id = #{typeId},
            seq = #{seq},
        WHERE
            public_id = #{publicId}
    ]]>
	</update>

    <delete id="delete">
    <![CDATA[
        delete from t_sys_module
        WHERE
            public_id = #{publicId}
    ]]>
    </delete>

    <!--                            刷数据的                                -->
    <!--***************************************************************-->

</mapper>
