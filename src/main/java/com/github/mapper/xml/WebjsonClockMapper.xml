<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.github.mapper.WebjsonClockMapper">

	<resultMap id="BaseResultMap" type="com.github.model.WebjsonClock">
        <result property="id" column="id"/>
        <result property="webId" column="web_id"/>
        <result property="jsonId" column="json_id"/>
        <result property="status" column="status"/>
        <result property="uid" column="uid"/>
        <result property="pageId" column="page_id"/>
	</resultMap>
	
	<!-- 用于select查询公用抽取的列 -->
	<sql id="com.github.mapper.WebjsonClockMapper.columns">
	<![CDATA[
		id, web_id, json_id, status, uid, page_id
	]]>
	</sql>

	<!-- useGeneratedKeys="true" keyProperty="xxx" for sqlserver and mysql -->
	<insert id="add" useGeneratedKeys="true" keyProperty="id">
    <![CDATA[
        INSERT INTO t_webjson_clock (
        	id, 
        	web_id, 
        	json_id, 
        	status, 
        	uid,
			page_id
        ) VALUES (
        	#{id}, 
        	#{webId}, 
        	#{jsonId}, 
        	#{status}, 
        	#{uid},
        	#{pageId}
        )
    ]]>
	</insert>
    
	<update id="update" >
    <![CDATA[
        UPDATE t_webjson_clock SET
	        web_id = #{webId}, 
	        json_id = #{jsonId}, 
	        status = #{status}, 
	        uid = #{uid},
            page_id = #{pageId}
        WHERE 
	        id = #{id} 
    ]]>
	</update>

    <delete id="delete">
    <![CDATA[
        DELETE FROM t_webjson_clock WHERE
        id = #{id} 
    ]]>
    </delete>

	<select id="get" resultMap="BaseResultMap">
		SELECT <include refid="com.github.mapper.WebjsonClockMapper.columns" />
	    <![CDATA[
		    FROM t_webjson_clock 
	        WHERE 
		        id = #{id} 
	    ]]>
	</select>

	<select id="getList" resultMap="BaseResultMap">
		SELECT <include refid="com.github.mapper.WebjsonClockMapper.columns" /> FROM t_webjson_clock
	</select>

	<select id="getWebjsonClockList" resultMap="BaseResultMap">
		SELECT <include refid="com.github.mapper.WebjsonClockMapper.columns" /> FROM t_webjson_clock
        <where>
            ...
        </where>
	</select>
	<select id="getByJsonId" resultMap="BaseResultMap">
		SELECT <include refid="com.github.mapper.WebjsonClockMapper.columns" /> FROM t_webjson_clock
		where json_id = #{jsonId}
	</select>

	<delete id="deleteByJsonId">
		DELETE FROM t_webjson_clock WHERE json_id = #{jsonId}
	</delete>
</mapper>
