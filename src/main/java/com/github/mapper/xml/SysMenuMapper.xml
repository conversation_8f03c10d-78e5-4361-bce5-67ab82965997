<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.github.mapper.SysMenuMapper">

	<resultMap id="BaseResultMap" type="com.github.model.SysMenu">
        <result property="id" column="id"/>
        <result property="pid" column="pid"/>
        <result property="name" column="name"/>
        <result property="intro" column="intro"/>
        <result property="url" column="url"/>
        <result property="page" column="page"/>
		<association property="subs" column="id" select="com.github.mapper.SysMenuMapper.getByPid"></association>
	</resultMap>
	
	<!-- 用于select查询公用抽取的列 -->
	<sql id="com.github.mapper.SysMenuMapper.columns">
	<![CDATA[
		id, pid, name, intro, url, page
	]]>
	</sql>

	<!-- useGeneratedKeys="true" keyProperty="xxx" for sqlserver and mysql -->
	<insert id="add" useGeneratedKeys="true" keyProperty="id">
    <![CDATA[
        INSERT INTO t_sys_menu (
        	id, 
        	pid, 
        	name, 
        	intro, 
        	url, 
        	page
        ) VALUES (
        	#{id}, 
        	#{pid}, 
        	#{name}, 
        	#{intro}, 
        	#{url}, 
        	#{page}
        )
    ]]>
	</insert>
    
	<update id="update" >
    <![CDATA[
        UPDATE t_sys_menu SET
	        pid = #{pid}, 
	        name = #{name}, 
	        intro = #{intro}, 
	        url = #{url}, 
	        page = #{page}
        WHERE 
	        id = #{id} 
    ]]>
	</update>

    <delete id="delete">
    <![CDATA[
        DELETE FROM t_sys_menu WHERE
        id = #{id} 
    ]]>
    </delete>
    
    <select id="get" resultMap="BaseResultMap">
		SELECT <include refid="com.github.mapper.SysMenuMapper.columns" />
	    <![CDATA[
		    FROM t_sys_menu 
	        WHERE 
		        id = #{id} 
	    ]]>
	</select>

	<select id="getList" resultMap="BaseResultMap">
		SELECT <include refid="com.github.mapper.SysMenuMapper.columns" /> FROM t_sys_menu
	</select>

	<select id="getSysMenuList" resultMap="BaseResultMap">
		SELECT <include refid="com.github.mapper.SysMenuMapper.columns" /> FROM t_sys_menu
        <where>
            ...
        </where>
	</select>

	<select id="getTops" resultMap="BaseResultMap">
		SELECT <include refid="com.github.mapper.SysMenuMapper.columns" /> FROM t_sys_menu
        where pid = 0
		<if test="isBigScreen==true">
			and type = 1
		</if>
		<if test="isBigScreen==false">
			<![CDATA[ and type >= 1 ]]>
		</if>
	</select>

	<select id="getByPid" resultMap="BaseResultMap">
		SELECT <include refid="com.github.mapper.SysMenuMapper.columns" /> FROM t_sys_menu
        where pid = #{pid}
	</select>

</mapper>
