<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.github.mapper.PageThemeMapper">

	<resultMap id="BaseResultMap" type="com.github.model.PageTheme">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="content" column="content"/>
        <result property="des" column="des"/>
        <result property="createUid" column="create_uid"/>
        <result property="type" column="type"/>
        <result property="status" column="status"/>
        <result property="pageId" column="page_id"/>
        <result property="coverUrl" column="cover_url"/>
	</resultMap>
	
	<!-- 用于select查询公用抽取的列 -->
	<sql id="com.github.mapper.PageThemeMapper.columns">
	<![CDATA[
        tpt.id, tpt.name, tpt.content, tpt.des, tpt.create_uid, tpt.`type`, tpt.status, tptr.page_id, tpt.cover_url
        ]]>
	</sql>

	<select id="listByTypeOrUid" resultMap="BaseResultMap">
		SELECT
        <include refid="com.github.mapper.PageThemeMapper.columns"/>
		FROM t_page_theme tpt
		join t_page_theme_rel tptr on tpt.id = tptr.page_theme_id
		WHERE
        tptr.page_id = #{pageId}
		<if test="type != null">
			and tpt.type = #{type}
		</if>
		and tpt.status = 1
		and tptr.can_choose = 1
	</select>
	<!-- useGeneratedKeys="true" keyProperty="xxx" for sqlserver and mysql -->
	<insert id="add" useGeneratedKeys="true" keyProperty="id">
    <![CDATA[
        INSERT INTO t_page_theme (
        	id, 
        	name, 
        	content, 
        	des, 
        	create_uid, 
        	type,
        	cover_url,
        	status
        ) VALUES (
        	#{id}, 
        	#{name}, 
        	#{content}, 
        	#{des}, 
        	#{createUid}, 
        	#{type},
        	#{coverUrl},
        	#{status}
        )
    ]]>
	</insert>
    
	<update id="update" >
    <![CDATA[
        UPDATE t_page_theme SET
	        name = #{name}, 
	        content = #{content}, 
	        des = #{des}, 
	        create_uid = #{createUid}, 
	        cover_url = #{coverUrl},
	        status = #{status},
	        type = #{type}
        WHERE 
	        id = #{id} 
    ]]>
	</update>

    <delete id="delete">
        <![CDATA[
        update t_page_theme set status = 0 WHERE
        id = #{id} 
    ]]>
    </delete>
    
    <select id="get" resultMap="BaseResultMap">
		SELECT <include refid="com.github.mapper.PageThemeMapper.columns" />
	    <![CDATA[
		    FROM t_page_theme  tpt
		    join t_page_theme_rel tptr on tpt.id = tptr.page_theme_id
	        WHERE 
		        tpt.id = #{id} and tptr.can_choose = 1
	    ]]>
	</select>

    <insert id="batchAdd" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO t_page_theme (name, content, des, create_uid, type, status, cover_url) VALUES
        <foreach collection="list" index="index" item="item" open="" close="" separator=",">
            (
            #{item.name},
            #{item.content},
            #{item.des},
            #{item.createUid},
            #{item.type},
            #{item.status},
            #{item.coverUrl}
            )
        </foreach>
    </insert>
	<!--***************************************************************-->

</mapper>
