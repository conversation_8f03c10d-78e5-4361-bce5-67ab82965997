package com.github.service;

import com.github.util.CookieUtils;
import com.github.util.HttpServletResponseUtils;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 登录特殊处理
 */
@Service
public class LoginExtendHandlerService {

    @Resource
    private RestTemplate restTemplate;

    //电子资源对应关系
    private Map wisdowMap = new HashMap() {
        {
            put("24149", "1128"); // 中央民族大学
            put("3175", "2108");  //上海师范大学天华学院
        }
    };

    public boolean needHandler(String wfwfid) {
        return wisdowMap.containsKey(wfwfid);
    }

    public void wisdomLib(HttpServletResponse response, String wfwfid) {
        try {
            MultiValueMap<String, Object> params = new LinkedMultiValueMap<>();
            String aid = MapUtils.getString(wisdowMap, wfwfid);
            if (StringUtils.isBlank(aid)) {
                return;
            }
            String enc = DigestUtils.md5Hex("[" + aid + "][" + DateFormatUtils.format(new Date(), "yyyy-MM-dd") + "][&*(h}*&HIU]");
            HttpEntity<MultiValueMap<String, Object>> httpEntity = new HttpEntity<>(params, null);
            ResponseEntity<String> responseEntity = restTemplate.exchange(String.format("http://wisdom.chaoxing.com/wisdomlogin.action?schoolid=%s&enc=%s", aid, enc), HttpMethod.GET, httpEntity, String.class);
            HttpServletResponseUtils.writeBackCookie(responseEntity.getHeaders(), response, null);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
