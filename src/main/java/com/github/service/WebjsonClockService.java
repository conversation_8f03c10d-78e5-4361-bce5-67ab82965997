/*
 */
package com.github.service;

import com.github.mapper.WebjsonClockMapper;
import com.github.model.Page;
import com.github.model.WebjsonClock;
import com.github.model.WebjsonTemp;
import com.github.util.Constants;
import org.springframework.aop.framework.AopContext;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;


@Service
public class WebjsonClockService {

    @Resource private WebjsonClockMapper webjsonClockMapper;
    @Resource
    private PageService pageService;
    @Resource
    private WebjsonTempService webjsonTempService;

    /** 添加 */
    @CachePut(value="objectCache", key="(#root.targetClass.getSimpleName()).concat(':id:').concat(#webjsonClock.id)")
    public WebjsonClock add(WebjsonClock webjsonClock) {
        this.webjsonClockMapper.add(webjsonClock);
        return webjsonClock;
    }
    /** 删除 */
    @CacheEvict(value="objectCache", key="(#root.targetClass.getSimpleName()).concat(':id:').concat(#id)")
    public void delete(Integer id) {
        this.webjsonClockMapper.delete(id);
    }
    /** 修改 */
    @CacheEvict(value="objectCache", key="(#root.targetClass.getSimpleName()).concat(':id:').concat(#webjsonClock.id)")
    public WebjsonClock update(WebjsonClock webjsonClock) {
        this.webjsonClockMapper.update(webjsonClock);
        return webjsonClock;
    }
    /** 查看 - 从Cache中获取对象 */
    @Cacheable(value="objectCache", key="(#root.targetClass.getSimpleName()).concat(':id:').concat(#id)", unless="#result eq null")
    public WebjsonClock get(Integer id) {
        return this.webjsonClockMapper.get(id);
    }
    /** 获取列表 */
    public List<WebjsonClock> getList() {
        return this.webjsonClockMapper.getList();
    }
    /** 获取条件列表 */
    public List<WebjsonClock> getWebjsonClockList(WebjsonClock webjsonClock) {
        return this.webjsonClockMapper.getWebjsonClockList(webjsonClock);
    }

    public WebjsonClock getByJsonId(Integer jsonId) {
        return webjsonClockMapper.getByJsonId(jsonId);
    }
    /* --------------------------------------------------- */

    /**
     * 是否因为开启审核被锁
     * @param clock
     * @return
     */
    public boolean isLockByAudit(WebjsonClock clock) {
        if (clock == null) {
            return false;
        }
        boolean isLockByAudit = Constants.STATUS_AUDIT_LATER.equals(clock.getStatus()) || Constants.STATUS_IN_AUDIT.equals(clock.getStatus());
        return isLockByAudit;
    }

    /**
     * 是否因为开启审核被指定uid锁
     * @param clock
     * @return
     */
    public boolean isLockByUidInAudit(WebjsonClock clock, Integer uid) {
        if (clock == null) {
            return false;
        }
        boolean isInAudit = Constants.STATUS_AUDIT_LATER.equals(clock.getStatus()) || Constants.STATUS_IN_AUDIT.equals(clock.getStatus());
        boolean isCurrentUid = clock.getUid().equals(uid);
        return isInAudit && isCurrentUid;
    }

    public void addOrUpdate(Integer webJsonId, Integer websiteId, Integer status, Integer uid, Integer pageId) throws Exception {
        WebjsonClock webjsonClock = getByJsonId(webJsonId);
        if (webjsonClock == null) {
            WebjsonClock tmpWebJsonClock = new WebjsonClock(websiteId, webJsonId, status, uid, pageId);
            ((WebjsonClockService) AopContext.currentProxy()).add(tmpWebJsonClock);
        } else if (Constants.STATUS_TRUE.equals(webjsonClock.getStatus())) {
            if (webjsonClock.getUid().equals(uid)) {
                // 自己开的普通锁定
                webjsonClock.setStatus(status);
                ((WebjsonClockService) AopContext.currentProxy()).update(webjsonClock);
            } else {
                throw new Exception("页面已被其他管理员锁定，不能修改");
            }
        } else if (Constants.STATUS_AUDIT_LATER.equals(webjsonClock.getStatus())) {
            // 之前锁状态是稍后审核锁定，当前是提交审核，并且是本人操作，将锁状态改成提交审核
            if (webjsonClock.getUid().equals(uid)) {
                webjsonClock.setStatus(status);
                ((WebjsonClockService) AopContext.currentProxy()).update(webjsonClock);
            } else {
                throw new Exception("页面已被其他管理员修改进入审核流程，审核完成前不能修改");
            }
        } else if (Constants.STATUS_IN_AUDIT.equals(webjsonClock.getStatus())) {
            throw new Exception("页面已在审核中，审核完成前不能修改");
        }
    }

    public void deleteByJsonId(Integer jsonId) {
        webjsonClockMapper.deleteByJsonId(jsonId);
    }

    /**
     * @param pageId
     * @param jsonId
     * @param uid
     * @throws Exception
     */
    @Transactional(rollbackFor = Exception.class)
    public void unlockAndRollback(Integer pageId, Integer jsonId, Integer uid) throws Exception {
        WebjsonClock clock = getByJsonId(jsonId);
        if (null == clock) {
            throw new Exception("未被锁定");
        }
        if (!clock.getUid().equals(uid)) {
            throw new Exception("已被别人锁定");
        }
        WebjsonTemp webjsonTemp = webjsonTempService.get(jsonId);
        if (Page.AUDIT_STATUS_IN_AUDIT.equals(webjsonTemp.getAuditStatus())) {
            throw new Exception("审核中不能解锁");
        }
        ((WebjsonClockService) AopContext.currentProxy()).delete(clock.getId());
        webjsonTempService.delete(jsonId);
    }
}

