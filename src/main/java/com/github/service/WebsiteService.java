/*
 * .............................................
 *
 * 				    _ooOoo_
 * 		  	       o8888888o
 * 	  	  	       88" . "88
 *                 (| -_- |)
 *                  O\ = /O
 *              ____/`---*\____
 *               . * \\| |// `.
 *             / \\||| : |||// \
 *           / _||||| -:- |||||- \
 *             | | \\\ - /// | |
 *            | \_| **\---/** | |
 *           \  .-\__ `-` ___/-. /
 *            ___`. .* /--.--\ `. . __
 *        ."" *< `.___\_<|>_/___.* >*"".
 *      | | : `- \`.;`\ _ /`;.`/ - ` : | |
 *         \ \ `-. \_ __\ /__ _/ .-` / /
 *======`-.____`-.___\_____/___.-`____.-*======
 *
 * .............................................
 *              佛祖保佑 永无BUG
 *
 * 佛曰:
 * 写字楼里写字间，写字间里程序员；
 * 程序人员写程序，又拿程序换酒钱。
 * 酒醒只在网上坐，酒醉还来网下眠；
 * 酒醉酒醒日复日，网上网下年复年。
 * 但愿老死电脑间，不愿鞠躬老板前；
 * 奔驰宝马贵者趣，公交自行程序员。
 * 别人笑我忒疯癫，我笑自己命太贱；
 * 不见满街漂亮妹，哪个归得程序员？
 *
 * 北纬30.√  <EMAIL>
 */
package com.github.service;

import com.alibaba.fastjson.JSONObject;
import com.github.cache.redis.lock.RedisLock;
import com.github.enums.ErrorCode;
import com.github.mapper.JobMapper;
import com.github.mapper.OrganizationApplicationMapper;
import com.github.mapper.OriginRelMapper;
import com.github.mapper.WebsiteMapper;
import com.github.model.*;
import com.github.model.dto.OrganizationApplicationDTO;
import com.github.model.vo.WebsiteVO;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.github.util.*;
import com.github.util.exception.BusinessException;
import org.apache.commons.lang.RandomStringUtils;
import org.apache.commons.lang.StringUtils;
import org.redisson.Redisson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.Cache;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.text.ParseException;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;


@Service
public class WebsiteService {

    @Resource
    private WebsiteMapper websiteMapper;
    @Lazy
    @Resource
    private WebsiteCloneService websiteCloneService;
    @Lazy
    @Resource
    private WebsiteConfigService websiteConfigService;
    @Lazy
    @Resource
    private TemplateService templateService;
    @Lazy
    @Resource
    private PageService pageService;
    @Resource
    private CacheService cacheService;
    @Resource
    private WebJsonRelService webJsonRelService;
    @Lazy
    @Resource
    private WebsiteV2Service websiteV2Service;
    @Lazy
    @Resource
    private PageRichService pageRichService;
    @Resource
    private Redisson redisson;
    @Lazy
    @Resource
    private UserAppPermissionService userAppPermissionService;
    @Lazy
    @Resource
    private WebJsonService webJsonService;
    @Resource
    private UserAppPermissionNewService userAppPermissionNewService;
    @Resource
    private OrganizationApplicationMapper organizationApplicationMapper;
    @Lazy
    @Resource
    private ApplicationService applicationService;
    @Resource
    private WebsitePlugsService websitePlugsService;
    @Resource
    private WebsitePagesService websitePagesService;
    @Lazy
    @Resource
    private WebsiteDomainService websiteDomainService;
    @Value("${engine-inner.domain-name}")
    private String innerDomain;
    @Value("${spring.web.upload-location}")
    private String uploadLocation;
    @Value("${ip.show.website.all:false}")
    private boolean showWebsiteAll;
    @Resource
    private JobMapper jobMapper;
    @Resource
    private OriginRelMapper originRelMapper;
    @Resource
    private UuidUtils uuidUtils;
    @Value("${test.version:0}")
    private Integer testVersion;
    @Resource
    private RestTemplate restTemplate;
    @Value("${engine-inner.domain-name}")
    private String domainInner;
    @Resource
    private HttpServletRequest request;

    @Value("${server.servlet.context-path}")
    private String contextPath;
    private static final String UPLOAD_CONTEXT = "upload/portal";
    private static final String ENGINE2_CHECK_LOGIN_CACHE_URI = "/context/cache/refresh/objectCache:PortalService:checkLogin:domain:";
    private final Logger logger = LoggerFactory.getLogger(WebsiteService.class);
    @Value("${isgray:false}")
    private Boolean gray;
    /**
     * 添加
     */
    public Website add(Website website) {
        website.setPublicId(uuidUtils.init());
        this.websiteMapper.add(website);
        return website;
    }
    /**
     * 添加
     */
    public Integer countByWfwfid(Integer wfwfid) {
        return websiteMapper.countByWfwfid(wfwfid);
    }

    /**
     * 删除
     */
    @CacheEvict(value = "objectCache", key = "(#root.targetClass.getSimpleName()).concat(':id:').concat(#id)")
    public void delete(Integer id) {
        this.websiteMapper.delete(id);
    }

    /**
     * 软删除
     */
    @CacheEvict(value = "objectCache", key = "(#root.targetClass.getSimpleName()).concat(':id:').concat(#id)")
    public void softDelete(Integer id) {
        this.websiteMapper.softDelete(id);
    }

    /**
     * 修改
     */
    @Caching(evict={
            @CacheEvict(value = "objectCache", key = "(#root.targetClass.getSimpleName()).concat(':id:').concat(#website.id)"),
            @CacheEvict(value = "objectCache", key = "(#root.targetClass.getSimpleName()).concat(':pubId:').concat(#website.publicId)")
    })
    public Website update(Website website) {
        String loginUrl = website.getLoginUrl();
        if(StringUtils.isNotBlank(loginUrl) && loginUrl.length() > 500){
            throw new BusinessException(ErrorCode.CODE_PARAMS_ERROR.getCode());
        }

        Website oldWebsite = this.get(website.getId());

        this.websiteMapper.update(website);

        // 清除超星获取域名对应网站缓存
        String key = "WebsiteService:interceptor-domain:" + website.getDomain();
        cacheService.removeCacheKey("objectCache", key);

        if (StringUtils.isNotEmpty(website.getDomain())) {
            String keyDel = "PortalService:checkLogin:domain:" + website.getDomain();
            cacheService.removeCacheKey("objectCache", keyDel);
        }
        if (StringUtils.isNotEmpty(website.getDomainExternal())) {
            String keyDel = "PortalService:checkLogin:domain:" + website.getDomainExternal();
            cacheService.removeCacheKey("objectCache", keyDel);
        }

        // 清除第三方域名网站缓存
        if (StringUtils.isNotBlank(website.getDomainExternal())) {
            String key3rd = "WebsiteService:external-domain:" + website.getDomainExternal();
            cacheService.removeCacheKey("objectCache", key3rd);
        }

        // 自有域名变化，清楚旧域名缓存
        if(!StringUtils.equals(oldWebsite.getDomainExternal(), website.getDomainExternal())){
            String key3rd = "WebsiteService:external-domain:" + oldWebsite.getDomainExternal();
            cacheService.removeCacheKey("objectCache", key3rd);
        }

        return website;
    }


    @Caching(evict={
            @CacheEvict(value = "objectCache", key = "(#root.targetClass.getSimpleName()).concat(':id:').concat(#website.id)"),
            @CacheEvict(value = "objectCache", key = "(#root.targetClass.getSimpleName()).concat(':pubId:').concat(#website.publicId)")
    })
    public void updatePdType(Website website) {
        String loginUrl = website.getLoginUrl();
        if(StringUtils.isNotBlank(loginUrl) && loginUrl.length() > 500){
            throw new BusinessException(ErrorCode.CODE_PARAMS_ERROR.getCode());
        }
        this.websiteMapper.updatePdType(website);
        // 清除超星获取域名对应网站缓存
        String key = "WebsiteService:interceptor-domain:" + website.getDomain();
        cacheService.removeCacheKey("objectCache", key);
        String key2 = "PortalService:getFidByServerName2:" + website.getDomain();
        cacheService.removeCacheKey("objectCache", key2);

        if (StringUtils.isNotEmpty(website.getDomain())) {
            String keyDel = "PortalService:checkLogin:domain:" + website.getDomain();
            cacheService.removeCacheKey("objectCache", keyDel);
        }
        if (StringUtils.isNotEmpty(website.getDomainExternal())) {
            String keyDel = "PortalService:checkLogin:domain:" + website.getDomainExternal();
            cacheService.removeCacheKey("objectCache", keyDel);
        }

        // 清除第三方域名网站缓存
        if (StringUtils.isNotBlank(website.getDomainExternal())) {
            String key3rd = "WebsiteService:external-domain:" + website.getDomainExternal();
            cacheService.removeCacheKey("objectCache", key3rd);
        }
    }


    /**
     * 更新cdn-version 版本
     *
     * @param websiteId
     */
    @CacheEvict(value = "objectCache", key = "(#root.targetClass.getSimpleName()).concat(':id:').concat(#websiteId)")
    public void updateVersion(Integer websiteId, Page page) {
        if (!Objects.equals(page.getWebsiteId(), websiteId)) {
            throw new BusinessException(ErrorCode.CODE_PAGE_WEBSITE_PARAM_REL_ERROR.getCode());
        }
        Website website = this.websiteMapper.get(websiteId);
        Optional.ofNullable(website).orElseThrow(() -> new BusinessException(ErrorCode.CODE_DATA_ERROR.getCode()));

        WebsiteDomain websiteDomain = websiteDomainService.getByWebsiteId(websiteId);
        if(Objects.nonNull(websiteDomain)){
            website.setDomainExternal(websiteDomain.getDomain());
        }
        this.websiteMapper.updateCdnVersion(websiteId);

        // 清除超星获取域名对应网站缓存
        String key = "WebsiteService:interceptor-domain:" + website.getDomain();
        cacheService.removeCacheKey("objectCache", key);

        if (StringUtils.isNotEmpty(website.getDomain())) {
            String keyDel = "PortalService:checkLogin:domain:" + website.getDomain();
            cacheService.removeCacheKey("objectCache", keyDel);
        }
        if (StringUtils.isNotEmpty(website.getDomainExternal())) {
            String keyDel = "PortalService:checkLogin:domain:" + website.getDomainExternal();
            cacheService.removeCacheKey("objectCache", keyDel);
        }

        // 清除第三方域名网站缓存
        if (StringUtils.isNotBlank(website.getDomainExternal())) {
            String key3rd = "WebsiteService:external-domain:" + website.getDomainExternal();
            cacheService.removeCacheKey("objectCache", key3rd);
        }
        cacheService.removeCacheKey("objectCache", "WebsiteService:pubId:"+website.getPublicId());

        webJsonService.clearSavePageCache(page);
    }

    @Cacheable(value = "objectCache", key = "(#root.targetClass.getSimpleName()).concat(':pubId:').concat(#pubId)")
    public Website getByPubId(String pubId) {
        return this.websiteMapper.getByPubId(pubId);
    }

    public Integer updatePubIds(Map<Integer, String> keyMap) {
        return websiteMapper.updatePubIds(keyMap);
    }

    public Integer getMaxId() {
        return websiteMapper.getMaxId();
    }

    /**
     * 查看 - 从Cache中获取对象
     */
    @Cacheable(value = "objectCache", key = "(#root.targetClass.getSimpleName()).concat(':id:').concat(#id)")
    public Website get(Integer id) {
        return this.websiteMapper.get(id);
    }

    @Cacheable(value = "objectCache", key = "(#root.targetClass.getSimpleName()).concat(':id:').concat(#id)")
    public Website getById(Integer id) {
        return this.websiteMapper.get(id);
    }

    public Website getNoCache(Integer id) {
        return this.websiteMapper.get(id);
    }

    /**
     * 获取列表
     */
    public List<Website> getList() {
        return this.websiteMapper.getList();
    }

    /**
     * 获取条件列表
     */
    public List<Website> getWebsiteList(Website website) {
        return this.websiteMapper.getWebsiteList(website);
    }

    public Website getByDomain(String domain) {
        return this.websiteMapper.getByDomain(domain);
    }

    /**
     * 全库查找，不管网站状态
     * @param domain
     * @return
     */
    public Website getByDomainAllStatus(String domain) {
        return this.websiteMapper.getByDomainAllStatus(domain);
    }

    /**
     * 根据主站域名和语言种类获取网站
     *
     * @param mainsiteDomain
     * @param globalType
     * @return
     */
    public Website getByMainsiteDomainAndGlobalType(String mainsiteDomain, String globalType) {
        Website mainWebsite = websiteMapper.getByDomain(mainsiteDomain);
        if(null == mainWebsite){
            throw new BusinessException(ErrorCode.CODE_WEBSITE_NULL_ERROR.getCode());
        }
        if (Website.LANGUAGE_ZH.equals(globalType)) {
            // 中文就是主站
            return mainWebsite;
        } else {
            List<Website> childWebsiteList = websiteMapper.findChildWebsite(mainWebsite.getId());
            return childWebsiteList.stream().filter(childWebsite -> childWebsite.getGlobalType().equals(globalType)).findFirst().orElse(null);
        }
    }

    /**
     * 镜像获取多语言网站
     * @param websiteId
     * @param globalType
     * @return
     */
    public Website getByMainSiteAndGlobalType(Integer websiteId, String globalType) {
        List<Website> childWebsiteList = websiteMapper.findChildWebsite(websiteId);
        if (CollectionUtils.isEmpty(childWebsiteList)) {
            return null;
        }
        return childWebsiteList.stream().filter(childWebsite -> childWebsite.getGlobalType().equals(globalType)).findFirst().orElse(null);
    }

    @Cacheable(value = "objectCache", key = "(#root.targetClass.getSimpleName()).concat(':interceptor-domain:').concat(#domain)")
    public Website getByDomain4Interceptor(String domain) {
        return this.websiteMapper.getByDomain(domain);
    }

    @Cacheable(value = "objectCache", key = "(#root.targetClass.getSimpleName()).concat(':external-domain:').concat(#domain)")
    public Website getByDomainExternal(String domain) {
        return this.websiteMapper.getByDomainExternal(domain);
    }


    /**
     * 查询机构的网站列表
     */
    public List<Website> getByWfwfid(Integer wfwfid) {
        return websiteMapper.getByWfwfid(wfwfid, Website.STATUS_ONLINE);
    }

    /**
     * 新增网站
     */
    public Integer add(Website target, Integer templateId, Integer originPageId) {
        Template template = templateService.get(templateId);
        Integer sourceWebsiteId = template.getWebsiteId();
        return cloneWebsite(sourceWebsiteId, target, originPageId, null, null);
    }

    /**
     * @param sourceWebsiteId
     * @param target
     * @param originPageId
     * @param clonePageId     克隆网站下的那个pageId。只克隆这个pageId
     * @return
     */
    public Integer cloneWebsite(Integer sourceWebsiteId, Website target, Integer originPageId, Integer clonePageId, Boolean isSaveOrigin) {
        RedisLock redisLock = new RedisLock(redisson, Constants.CLONE_WEBSITE_PRE_KEY + target.getDomain());
        Integer webCount = websiteMapper.countByWfwfid(target.getWfwfid());
        if (webCount > Constants.MAX_WEBSITE_NUM) {
            throw new BusinessException(ErrorCode.CODE_WEBSITE_MAX.getCode());
        }

        if (redisLock.tryLock()) {
            Assert.isNull(this.getByDomain(target.getDomain()), "非法创建站点.");

            target.setOrigin(sourceWebsiteId);
            // homePageId和cover在克隆完主页之后赋值
            // 设置克隆过程中，默认登录类型为1
            target.setLoginType(Website.LOGIN_TYPE_NO_AUTH);
            target.setVersions(Website.VERSION_1);
            Website sourceWebsite = ((WebsiteService) AopContext.currentProxy()).get(sourceWebsiteId);
            target.setPageThemeType(sourceWebsite.getPageThemeType());
            target.setWebType(sourceWebsite.getWebType());
            target.setLoginCurrentOrg(sourceWebsite.getLoginCurrentOrg()); // 复用模版里面的loginCurrentOrg
            this.add(target);

            // 保存克隆关系
            if (isSaveOrigin != null && isSaveOrigin) {
                // 存储克隆关系数据
                OriginRel originRel = new OriginRel(target.getWfwfid(), target.getId(), sourceWebsiteId, OriginRel.TYPE_WEBSITE);
                originRelMapper.add(originRel);
            }

            // 克隆网站
            this.clone(sourceWebsiteId, target, originPageId, clonePageId, isSaveOrigin);

            redisLock.unlock();
            return target.getId();
        }
        return target.getId();
    }

    /**
     * 替换网站的模板
     *
     * @param templateId
     * @param target
     * @param originPageId
     * @return
     */
//    @Transactional
    public Integer replaceWebsite(Integer templateId, Website target, Integer originPageId) {
        RedisLock redisLock = new RedisLock(redisson, Constants.CLONE_WEBSITE_PRE_KEY + target.getDomain());
        if (redisLock.tryLock()) {
            Template template = templateService.get(templateId);
            Integer sourceWebsiteId = template.getWebsiteId();

            List<Page> list = pageService.getByWebsiteId(target.getId());

            // 克隆网站
            this.clone(sourceWebsiteId, target, originPageId, null, null);

            // 删除该website下之前的page 和 webjson , 应用对应的数据做保留处理，首页可以获取整个网站下的应用来使用。
            for (Page p : list) {
                pageService.delete(p.getId());
                webJsonService.delete(p.getWebJsonId());
            }
            redisLock.unlock();
            return target.getId();
        }
        return target.getId();
    }

    /**
     * 网站克隆
     */
    public void clone(Integer sourceWebsiteId, Website targetWebsite, Integer originPageId, Integer clonePageId, Boolean isSaveOrigin) {
        WebsitePages websitePages;
        if (Website.WEB_TYPE_BIG_SCREEN.equals(targetWebsite.getWebType())){
            websitePages = websitePagesService.get(sourceWebsiteId);
            if (Objects.nonNull(websitePages)){
                websitePages.setWebId(targetWebsite.getId());
            }
        } else {
            websitePages = null;
        }

        // 克隆websiteConfig
        WebsiteConfig websiteConfig = websiteConfigService.get(sourceWebsiteId);
        if (websiteConfig != null) {
            websiteConfig.setId(null);
        } else {
            websiteConfig = new WebsiteConfig();
        }
        websiteConfig.setWebsiteId(targetWebsite.getId());
        JSONObject otherConfig = websiteConfig.getOtherConfig();
        if (otherConfig == null) {
            otherConfig = new JSONObject();
        }
        // 首页是否克隆完成
        otherConfig.put("homePageClone", false);
        websiteConfig.setOtherConfig(otherConfig);
        websiteConfigService.saveOrUpdate(websiteConfig);

        Website sourceWebsite = websiteMapper.get(sourceWebsiteId);
        Integer homePageId = sourceWebsite.getHomePageId();

        List<Page> pages = pageService.listByWebsiteId(sourceWebsiteId);
        List<Page> commonPages = new ArrayList<>();
        List<Page> orgSubPages = new ArrayList<>();
        Page homePage = null;

        // 找subPages相关的subPageIdList，仅处理包含在此列表中的subPage
        List<String> subPageIdList = websiteV2Service.getSubPageIdList(pages);

        boolean isObtainClonePage = false;
        boolean isBigScreenSmartMachine = false;
        for (Page page : pages) {
            if (page.getTemplate().startsWith(Template.TEMPLATE_2_SUB)) {
                if (!CollectionUtils.isEmpty(subPageIdList) && subPageIdList.contains(page.getPublicId())){ // 包含在子页面关系中才走后续逻辑,未命中的page不克隆，舍弃掉
                    orgSubPages.add(page);
                }
            } else {
                if (homePageId.equals(page.getId())) {
                    homePage = page;
                } else {
                    commonPages.add(page);
                }
                if (Objects.equals("big_screen_smartmachine", page.getTemplate())) {
                    isBigScreenSmartMachine = true;
                }

                if (null != clonePageId && clonePageId.equals(page.getId())) {
                    isObtainClonePage = true;
                    homePage = page;
                }
            }
        }

        if (!isObtainClonePage) {
            if (isBigScreenSmartMachine) {
                WebsitePlugs websitePlugs = websitePlugsService.get(-1);
                //克隆，不能直接修改缓存对象
                WebsitePlugs addWebsitePlug = new WebsitePlugs();
                BeanUtils.copyProperties(websitePlugs, addWebsitePlug);
                addWebsitePlug.setId(null);
                addWebsitePlug.setWebsiteId(targetWebsite.getId());
                websitePlugsService.add(addWebsitePlug);
            } else { // 其他模板，克隆网站的插件。
                List<WebsitePlugs> listPlugs = websitePlugsService.getPlugList(sourceWebsiteId,null);
                if (!CollectionUtils.isEmpty(listPlugs)) {
                    for (WebsitePlugs wp : listPlugs) {
                        WebsitePlugs addWebsitePlug = new WebsitePlugs();
                        BeanUtils.copyProperties(wp, addWebsitePlug);
                        addWebsitePlug.setId(null);
                        addWebsitePlug.setWebsiteId(targetWebsite.getId());
                        websitePlugsService.add(addWebsitePlug);
                    }
                }
            }
        } else {

        }
        // 克隆指定的网页ID (homePage 可能是指定网站页面)
        if(gray){
           logger.error("克隆首页：{}", originPageId);
        }
        Map<Integer, Page> alreadyClonePage = new ConcurrentHashMap<>();   // 已经克隆的page, key->克隆前的pageId，value->克隆后的page
        Page page = pageService.clone(homePage, targetWebsite.getId(), true, originPageId, isSaveOrigin, null);
        alreadyClonePage.put(homePage.getId(), page);

        // 大屏维护pageId关系的map
        Map<String, String> pageIdMap = new ConcurrentHashMap<>();
        pageIdMap.put(homePage.getPublicId(), page.getPublicId());

        // 首页克隆完之后克隆子页面头部
        List<Page> subHeaderPages = commonPages.stream().filter(p -> Objects.equals(p.getTemplate(), "template_2_header")).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(subHeaderPages)) {
            subHeaderPages.parallelStream().forEach(subHeaderPage -> {
                Page targetPage = pageService.clone(subHeaderPage, targetWebsite.getId(), true, originPageId, isSaveOrigin, targetWebsite.getHomePageId());
                alreadyClonePage.put(subHeaderPage.getId(), targetPage);
                // 克隆2.0的更多详情页，修改其他首页webjson结构的的subPages关系
                this.dealWebsiteIndexSubPage(sourceWebsite, targetWebsite, targetPage, orgSubPages, alreadyClonePage);
                pageIdMap.put(subHeaderPage.getPublicId(), targetPage.getPublicId());
            });
        }
        websiteV2Service.replaceBsInnerUriByPage(pageIdMap, page, page.getId());

        // 首页克隆完成之后更新状态
        otherConfig.put("homePageClone", true);
        websiteConfig.setOtherConfig(otherConfig);
        websiteConfigService.saveOrUpdate(websiteConfig);

        // 克隆2.0的更多详情页，修改首页webjson结构的的subPages关系
        this.dealWebsiteIndexSubPage(sourceWebsite, targetWebsite, page, orgSubPages, alreadyClonePage);
        // 替换大屏的前端维护关系
        this.dealWebsitePages(targetWebsite, homePage, page, websitePages);


        targetWebsite.setHomePageId(page.getId());
        targetWebsite.setCover(sourceWebsite.getCover());
        if (targetWebsite.getProductType() == null || targetWebsite.getProductType() == 0) { // productType 为空的时候，才进行替换，要考虑替换模板的情况。
            targetWebsite.setProductType(sourceWebsite.getProductType());
        }
        // 处理登录地址和退出地址
        String loginUrl = sourceWebsite.getLoginUrl();
        String logoutUrl = sourceWebsite.getLogoutUrl();
        // 设置登录地址变更的处理 - 默认替换refer。

        String websiteDomain = "http%3A%2F%2F" + targetWebsite.getDomain() + ".mh.chaoxing.com";
        if (SpringContextHolder.isMirror()) {
//            String domain = Utils.portalDomain(request);
            websiteDomain = contextPath + "page/" + targetWebsite.getHomePageId() + "/show";
            try {
                websiteDomain = URLEncoder.encode(websiteDomain, "UTF-8");
            } catch (UnsupportedEncodingException e) {
                e.printStackTrace();
            }
        }
        if (!sourceWebsite.getLou()) {
            if (StringUtils.isNotBlank(loginUrl)) {
                if (loginUrl.contains("refer=")) {
                    loginUrl = loginUrl.substring(0, loginUrl.indexOf("refer="));
                    targetWebsite.setLoginUrl(loginUrl + "refer=" + websiteDomain);
                } else {
                    targetWebsite.setLoginUrl(loginUrl);
                }
            }
        } else {
            targetWebsite.setLoginUrl(loginUrl);
        }

        if (StringUtils.isNotBlank(logoutUrl) && logoutUrl.contains("refer=")) {
            logoutUrl = logoutUrl.substring(0, logoutUrl.indexOf("refer="));
            targetWebsite.setLogoutUrl(logoutUrl + "refer=" + websiteDomain);
        }
        // 设置loginOriginUrl。
        targetWebsite.setLou(sourceWebsite.getLou());
        targetWebsite.setLoginType(sourceWebsite.getLoginType());

        // 处理登录和退出地址的参数
        String loginUrlTmp = targetWebsite.getLoginUrl();
        if (StringUtils.isNotBlank(loginUrlTmp) && loginUrlTmp.contains("websiteId=")) {
            loginUrlTmp = loginUrlTmp.replaceAll("websiteId=([\\d]+)", "websiteId=" + targetWebsite.getId());
            targetWebsite.setLoginUrl(loginUrlTmp);
        }
        String logoutUrlTmp = targetWebsite.getLogoutUrl();
        if (StringUtils.isNotBlank(logoutUrlTmp) && logoutUrlTmp.contains("websiteId=")) {
            logoutUrlTmp = logoutUrlTmp.replaceAll("websiteId=([\\d]+)", "websiteId=" + targetWebsite.getId());
            targetWebsite.setLogoutUrl(logoutUrlTmp);
        }

        // 克隆站点其他页面
        commonPages = commonPages.stream().filter(p -> !Objects.equals(p.getTemplate(), "template_2_header")).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(commonPages) && !isObtainClonePage) {
            commonPages.parallelStream().forEach(_page -> {
                if(gray){
                    logger.error("克隆子页面：{}, {}", originPageId, targetWebsite.getHomePageId());
                }

                Page targetPage = pageService.clone(_page, targetWebsite.getId(), true, originPageId, isSaveOrigin, targetWebsite.getHomePageId());
                alreadyClonePage.put(_page.getId(), targetPage);
                // 克隆2.0的更多详情页，修改其他首页webjson结构的的subPages关系
                this.dealWebsiteIndexSubPage(sourceWebsite, targetWebsite, targetPage, orgSubPages, alreadyClonePage);
                // 替换大屏的前端维护关系
                this.dealWebsitePages(targetWebsite, _page, targetPage, websitePages);
                pageIdMap.put(_page.getPublicId(), targetPage.getPublicId());
            });
        }

        // 大屏和2.0的内部跳转地址，需要替换pagePublicId
        websiteV2Service.replaceBsInnerUri(targetWebsite.getWebType(), pageIdMap, targetWebsite);


        // 不为空，最后添加大屏页面关系
        if (Objects.nonNull(websitePages)){
            websitePagesService.add(websitePages);
        }
        
        // 网站登录地址修改,只修改网站对象里的url，下一步更新到数据库
        this.replaceUrlAndParams(targetWebsite, sourceWebsite);

        // 克隆完成
        targetWebsite.setStatus(Constants.STATUS_TRUE);

        // 单独修改webType
        targetWebsite.setWebType(sourceWebsite.getWebType());
        updateWebType(targetWebsite.getId(), targetWebsite.getWebType());

        ((WebsiteService) AopContext.currentProxy()).update(targetWebsite);
    }

    // 替换大屏的前端维护关系
    private void dealWebsitePages(Website targetWebsite, Page sourcePage, Page tarPage, WebsitePages websitePages) {
        if(Website.WEB_TYPE_BIG_SCREEN.equals(targetWebsite.getWebType()) && Objects.nonNull(websitePages)){
            if (StringUtils.isNotEmpty(websitePages.getPageListJson())){
                websitePages.setPageListJson(websitePages.getPageListJson().replace(sourcePage.getPublicId(), tarPage.getPublicId()));
            }
        }
    }

    /**
     * 按page克隆、处理子页面关系, 克隆子页面的富文本
     * @param sourceWebsite
     * @param targetWebsite
     * @param page 子页面的首页
     * @param orgSubPages 所有（列表、详情）page
     */
    private void dealWebsiteIndexSubPage(Website sourceWebsite, Website targetWebsite, Page page, List<Page> orgSubPages, Map<Integer, Page> alreadyClonePage) {
        if (Website.WEB_TYPE_2.equals(sourceWebsite.getWebType())){
            WebJsonRel webJsonRel = new WebJsonRel();
            WebJsonRel orgWebJsonRel = webJsonRelService.get(page.getWebJsonId());
            if(Objects.nonNull(orgWebJsonRel)){
                BeanUtils.copyProperties(orgWebJsonRel, webJsonRel);
                webJsonRel.setId(page.getWebJsonId());
            }


            logger.error("pageId:{}", page.getId());

            WebJson IndexWebJson = webJsonService.get(page.getWebJsonId());
            List<String> subIds;
            if (Objects.isNull(webJsonRel.getId())){ // 老版本，取webjson
                subIds = WebJsonRel.getWebsiteDetailPagesPageId(IndexWebJson.getContent());
                subIds.addAll(WebJsonRel.getWebsiteDetailPagesPageId(IndexWebJson.getAppContent()));
            } else {
                subIds = WebJsonRel.getWebsiteDetailPagesPageId(webJsonRel.getSubPages());
                subIds.addAll(WebJsonRel.getWebsiteDetailPagesPageId(webJsonRel.getAppSubPages()));
            }
            subIds = subIds.stream().distinct().collect(Collectors.toList());

            AtomicBoolean onceUpdate = new AtomicBoolean(false);
            AtomicBoolean needUpdate = new AtomicBoolean(false);

            subIds.parallelStream().forEach(pId -> {
                // 先换原来的webjson
                Page orgPage = null;
                for (Page op : orgSubPages) {
                    if (StringUtils.equals(op.getPublicId(), pId)) {
                        orgPage = op;
                        break;
                    }
                }
                if (null == orgPage) {
                    logger.error("orgPage=null");
                } else {
                    Page tagSubPage;
                    if (alreadyClonePage.containsKey(orgPage.getId())) {
                        tagSubPage = alreadyClonePage.get(orgPage.getId());
                    } else {
                        tagSubPage = websiteV2Service.addSubPage(orgPage.getName(), orgPage.getId(), targetWebsite.getId(), true, targetWebsite.getHomePageId());
                        alreadyClonePage.put(orgPage.getId(), tagSubPage);
                        dealWebsiteIndexSubPage(sourceWebsite, targetWebsite, tagSubPage, orgSubPages, alreadyClonePage);
                    }

                    // page == homepage
                    onceUpdate.set(websiteV2Service.updateFstPageJson(tagSubPage, pId, IndexWebJson, webJsonRel));
                    if (onceUpdate.get()) {
                        needUpdate.set(true);
                    }
                }
            });
            if (needUpdate.get()) {
                webJsonService.updateNoCache(IndexWebJson);
            }
            if (Objects.nonNull(webJsonRel.getId())){
                webJsonRelService.addOrUpdate(webJsonRel);
            }
        }
    }

    /**
     * 生成唯一的网站域名
     *
     * @param wfwfid
     * @return
     */
    public String randomDomain(Integer wfwfid) {
        String domain = wfwfid + RandomStringUtils.randomAlphabetic(4).toLowerCase();
        RedisLock redisLock = new RedisLock(redisson, "create-website-domain-lock");
        if (redisLock.tryLock()) {
            domain = Base62Utils.fromBase10(System.currentTimeMillis());
            redisLock.unlock();
        }
        return domain;
    }

    public Template addTemplate(Integer id, String name, Integer wfwfid, Integer uid) {
        Website website = this.get(id);
        Template template = new Template();
        template.setName(name);
        template.setWebsiteId(id);
        template.setCover(website.getCover());
        template.setStatus(Template.STATUS_ONLINE);
        template.setWfwfid(wfwfid);
        template.setCreateTime(new Date());
        template.setCreateUser(uid);
        templateService.add(template);
        return template;
    }

    public Template addTemplate(Integer id, String name, Integer wfwfid, Integer uid, Integer typeId) {
        Website website = this.get(id);
        Template template = new Template();
        template.setName(name);
        template.setWebsiteId(id);
        template.setCover(website.getCover());
        template.setStatus(Template.STATUS_ONLINE);
        template.setWfwfid(wfwfid);
        template.setCreateTime(new Date());
        template.setCreateUser(uid);
        template.setTypeId(typeId);
        templateService.add(template);
        return template;
    }

    public Template addTemplateAndClone(Integer id, String name, Integer wfwfid, Integer uid, Integer typeId) {
        Website sourceWebsite = this.get(id);

        Website website = new Website();
        BeanUtils.copyProperties(sourceWebsite, website);
        website.setId(null);
        website.setName(name);
        website.setWfwfid(wfwfid);
        website.setCreateUser(uid);
        website.setHide(true);
        // 添加为模板的时候，需要从新给该网站添加域名， 现在新增模板代表一个新的网站。域名也要改。
        website.setDomain(this.randomDomain(wfwfid));
        if (Website.WEB_TYPE_BIG_SCREEN.equals(website.getWebType())) {
            // 大屏
            website.setProductType(1019);
        }
        this.add(website);

        // 异步克隆
        websiteCloneService.asyncClone(id, website, typeId);

        Template template = new Template();
        template.setName(name);
        template.setWebsiteId(website.getId());
        template.setCover(website.getCover());
        template.setStatus(Template.STATUS_ONLINE);
        template.setWfwfid(wfwfid);
        template.setCreateTime(new Date());
        template.setCreateUser(uid);
        if (typeId != null) {
            template.setTypeId(typeId);
        }
        templateService.add(template);

        return template;
    }

    public boolean checkByWfwfid(Integer id, Integer wfwfid) {
        return this.get(id).getWfwfid().equals(wfwfid);
    }

    /**
     * 把某个站点设为系统模板
     */
//	@Transactional
    public Template setSystemTemplate(Integer sourceWebsiteId, String name) {
        if (StringUtils.isBlank(name)) {
            name = "系统模板";
        }
        Website websiteSrc = get(sourceWebsiteId);
        // 生成系统模板，原网站和目标网站，使用同一个wfwfid。
        Website website = new Website(name, null, websiteSrc.getWfwfid(), 1);
        Set<Integer> appIds = new HashSet<>();
        // 找到这个网站的所有子网页,拿到webjson和appIds
        List<Page> listPages = pageService.listByWebsiteId(sourceWebsiteId);
        for (Page p : listPages) {
            WebJson webJson = webJsonService.get(p.getWebJsonId());
            List<Integer> apps = webJsonService.listAppIds(webJson.getContent(), webJson.getId());
            appIds.addAll(apps);
        }

        List<Application> apps = applicationService.getListByWebId(sourceWebsiteId);
        List<Integer> oldAppIds = new ArrayList<>();
        apps.forEach(app -> {
            if (appIds.contains(app.getId()) && StringUtils.isNotEmpty(app.getDataUrl()) && app.getDataUrl().startsWith(Constants.OLD_ENGINE_NAME)) {
                oldAppIds.add(app.getId());
            }
        });

        if (oldAppIds.size() > 0) {
            String msg = "";
            try {
                msg = URLEncoder.encode("网站包含老应用数据，APPID为： " + oldAppIds + " ，网站不允许克隆老应用，请修改为新应用及数据后再克隆", "UTF-8");
            } catch (UnsupportedEncodingException e) {
                e.printStackTrace();
            }
            throw new BusinessException(msg);
        }
        // 复制来源，一个来源只能设置一个系统模板
        website.setOrigin(sourceWebsiteId);
        website.setVersions(Website.VERSION_1);
        website.setWebType(websiteSrc.getWebType());
        website.setPageThemeType(websiteSrc.getPageThemeType());
        this.add(website);

        // 修改域名
        website.setDomain(website.getId().toString());
        // 克隆网站
        this.clone(sourceWebsiteId, website, 0, null, null);

        // 设为系统模板（一个website只能设置为一个系统模板），新逻辑，由于应用的数据，都是新建appID来进行的，所以应用数据相互隔离不会有影响。
        RedisLock redisLock = new RedisLock(redisson, sourceWebsiteId + "SystemTemplate");
        if (redisLock.tryLock()) {
            return this.addTemplate(website.getId(), name, website.getWfwfid(), website.getCreateUser());
        }
        redisLock.unlock();
        return null;
    }

    /**
     * 把某个站点克隆给某个单位
     */
    @Transactional
    public Integer cloneWebsiteToFid(Integer id, Integer wfwfid, Integer uid, Integer originPageId) {
        Website source = this.get(id);
        String name = source.getName();
        String domain = randomDomain(wfwfid);
        Website target = new Website(name, domain, wfwfid, uid);
        if(SpringContextHolder.isMirror()){
            return cloneWebsite(id, target, originPageId, null, null);
        }
        Website tempWebsite = ((WebsiteService) AopContext.currentProxy()).get(id);
        websiteCloneService.doPreAddWebsite(target, tempWebsite, Boolean.FALSE, originPageId, null);
        return target.getId();
    }

    /**
     * 修改网站状态（软删除及恢复）
     *
     * @param id
     */
    public void updateStatus(Integer id, Integer status) {
        Website website = get(id);
        website.setStatus(status);
        ((WebsiteService) AopContext.currentProxy()).update(website);
    }

    /**
     * 查page所属网站
     *
     * @param pageId
     */
    public List<Website> listByPageId(Integer pageId) {
        List<Website> websites = websiteMapper.getByPageId(pageId);
        return websites;
    }

    /**
     * 获取管理的网站列表
     *
     * @param wfwfid
     * @param uid
     * @param status
     * @return
     */
    public List<Website> getManageWebsiteList(Integer wfwfid, Integer uid, Integer status, Integer pageId) {
        List<Website> websites = new ArrayList<>();
        String domain = Utils.portalDomain(request);
        if (SpringContextHolder.isMirror() && Utils.isIP(domain)) {
            if (showWebsiteAll) {
                websites = websiteMapper.getByWfwfid(wfwfid, status);
            } else {
                websites = websiteMapper.getByPageId(pageId);
            }
        } else {
            websites = websiteMapper.getByWfwfid(wfwfid, status);
        }
        UserAppPermission userAppPermission = userAppPermissionService.getByFidUid(wfwfid, uid);
        List<UserAppPermissionNew> uaps = userAppPermissionNewService.getListByWfwfid(wfwfid, uid, UserAppPermissionNew.TYPE_APP);
        List<UserAppPermissionNew> rolePermissionByUid = userAppPermissionNewService.getRolePermissionByUid(wfwfid, uid, UserAppPermissionNew.TYPE_APP);
        uaps.addAll(rolePermissionByUid);
        // 分组取出roleFlag最小的那条记录
        Map map = uaps.stream().collect(Collectors.groupingBy(uap -> uap.getWebId(),
                Collectors.collectingAndThen(Collectors.reducing((c1, c2) -> c1.getRoleFlag() < c2.getRoleFlag() ? c1 : c2), Optional::get)));

        String webs = "";
        String appweb = "";
        if (Objects.nonNull(userAppPermission)) {
            webs = StringUtils.isBlank(userAppPermission.getWebs()) ? "" : userAppPermission.getWebs();
            appweb = StringUtils.isBlank(userAppPermission.getAppWebs()) ? "" : userAppPermission.getAppWebs();
        }
        String[] haswebs = webs.split(",");
        String[] appwebs = appweb.split(",");
        List<Website> listWebs = new ArrayList<>();
        // 确认哪些是自己的网站
        for (Website ws : websites) {
            if (ws.getCreateUser().equals(uid)) {
                ws.setIsSelf(2);
                listWebs.add(ws);
            } else {
                boolean matchWeb = false;
                for (String s : haswebs) {
                    if (s.equals(String.valueOf(ws.getId()))) {
                        ws.setIsSelf(2);
                        listWebs.add(ws);
                        matchWeb = true;
                        break;
                    }
                }
                if (!matchWeb) {
                    for (String s : appwebs) {
                        if (s.equals(String.valueOf(ws.getId()))) {
                            ws.setIsSelf(0);
                            listWebs.add(ws);
                            break;
                        }
                    }
                }

                // 从新的权限列表里面获取网站。
                Object obj = map.get(ws.getId());
                if (null != obj) {
                    UserAppPermissionNew uapn = (UserAppPermissionNew) (obj);
                    if (uapn.getRoleFlag() <= Constants.ROLE_PAGE_ADMIN) {
                        ws.setIsSelf(1);
                    } else {
                        ws.setIsSelf(0);
                    }
                    // 超级管理员
                    if (uapn.getRoleFlag() <= Constants.ROLE_SUPER_ADMIN) {
                        ws.setIsSelf(2);
                    }
                    listWebs.add(ws);
                }
            }
        }
        return listWebs;
    }

    public PageInfo<WebsiteVO> getManageWebsiteListNew(Integer wfwfid, Integer uid, Integer status, Integer productType, Integer pageNum, Integer pageSize, String keyword) {
        PageHelper.startPage(pageNum, pageSize);
        List<WebsiteVO> websiteList = websiteMapper.getManageWebsiteList(wfwfid, uid, status, productType, keyword);
        PageInfo<WebsiteVO> pageInfo = PageInfo.of(websiteList);
        websiteList.forEach(website -> {
            if (website.getCreateUser().equals(uid) || website.getRoleFlag() <= Constants.ROLE_SUPER_ADMIN) {
                website.setIsSelf(2);
            } else if (website.getRoleFlag() <= Constants.ROLE_PAGE_ADMIN) {
                website.setIsSelf(1);
            } else {
                website.setIsSelf(0);
            }
        });
        pageInfo.setList(websiteList);
        return pageInfo;
    }

    public List<Map> getAppListByWebsiteIdAndWfwfid(Integer websiteId, Integer wfwfid) {
        return organizationApplicationMapper.getListByWebsitId(websiteId, wfwfid);
    }

    public List<OrganizationApplicationDTO> listByApplicationIdsAndWebId(List<Integer> ids, Integer websiteId, Integer wfwfid) {
        return organizationApplicationMapper.listByApplicationIdsAndWebId(websiteId, ids, wfwfid);
    }

    /**
     * IP镜像版本获取pageId
     *
     * @param host
     */
    public String getPageId(String host, String pageId) {
        String pageNotExist = Constants.PAGE_NOT_EXIST;
        String domain = Utils.portalDomain(request);
        String domainUrl = domain.replaceAll("http[s]?://", "");
        if (Objects.isNull(host) || !host.contains(":")) {
            return domainUrl.equals(host) ? pageId : pageNotExist;
        }
        String[] split = host.split(":");

        if (!domainUrl.split(":")[0].equals(split[0])) {
            return pageNotExist;
        }
        Website website = websiteMapper.getByDomain(split[1]);
        if (Objects.nonNull(website)) {
            pageId = String.valueOf(website.getHomePageId());
        } else {
            pageId = pageNotExist;
        }
        return pageId;
    }

    /**
     * 直接克隆网站
     */
    public void cloneWebsiteByType(Integer sourceWebsiteId, Website targetWebsite, Integer originPageId) {
        this.clone(sourceWebsiteId, targetWebsite, originPageId, null,null);
    }

    /**
     * 获取网站列表
     *
     * @param pageNum
     * @param pageSize
     * @return
     */
    public PageInfo<Website> getWebsites(Integer pageNum, Integer pageSize, String name, String domain, Integer createUser,
                                         Integer wfwfid, Integer status, Integer productType, Integer websiteId, Integer webType) {
        com.github.pagehelper.Page page = PageHelper.startPage(pageNum, pageSize);
        this.websiteMapper.getWebsitList(name, domain, createUser, wfwfid, status, productType, websiteId, webType);


        return new PageInfo<Website>(page);
    }

    public Website getByDomainExternalAndGlobalType(String domain, String globalType) {
        return websiteMapper.getByDomainExternalAndGlobalType(domain, globalType);
    }

    public String getWebIdOrPubId(Website w) {
        return StringUtils.isEmpty(w.getPublicId()) ? DES.encrypt(w.getId()) : w.getPublicId();
    }

    /**
     * 查询走灰度的单位ID是否存在
     *
     * @param wfwfid
     * @return
     */
    public Integer getGrayWfwfid(Integer wfwfid) {
        return websiteMapper.getGrayWfwfid(wfwfid);
    }


    /**
     * @param uid
     * @param template
     * @return
     */
    public Website getPageByUid(Integer uid, String template) {
        return websiteMapper.getPageByUid(uid, template);
    }

    /**
     * @param uid
     * @param websiteId
     * @return
     */
    public Website getByOriginAndUid(Integer uid, Integer websiteId) {
        return websiteMapper.getByOriginAndUid(uid, websiteId);
    }

    public List<Map> getListByWebsiteIds(List<Integer> websiteIds) {
        return websiteMapper.getListByWebsiteIds(websiteIds);
    }

    public Website getByName(String name) {
        return websiteMapper.getByName(name);
    }

    public List<Website> listByIds(List<Integer> ids) {
        return websiteMapper.listByIds(ids);
    }

    /**
     * 查询定时任务时间段
     *
     * @param websiteId
     * @return
     */
    public JSONObject getOffLineTime(Integer websiteId) {
        String dataType = "WebOffLine";
        List<SysJob> jobList = jobMapper.getByDataIdAndDataType(websiteId, dataType);
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(jobList)) {
            return jobList.get(0).getConfig();
        }

        return null;
    }

    /**
     * 处理网站下线状态判断
     * 返回true 不下线。
     * @param websiteId
     * @param offLineStartTime
     * @param offLineEndTime
     *
     */
    public boolean updateOffLine(Integer websiteId, String offLineStartTime, String offLineEndTime, String visitorUrl, Integer offStatus) {
        String dataType = "WebOffLine";

        if (StringUtils.isBlank(visitorUrl) || Website.STATUS_SHOW.equals(offStatus)) {
            // 暂停定时任务
//            jobMapper.deleteByDataIdAndDataType(websiteId, dataType);
            return true;
        }

        // 任务列表
//        List<SysJob> jobList = jobMapper.getByDataIdAndDataType(websiteId, dataType);
//
//        String cron = "0 %s %s * * ?";
//        String startTime = offLineStartTime;
//        Integer startHour = Integer.parseInt(StringUtils.split(startTime, ":")[0]);
//        Integer startMinute = Integer.parseInt(StringUtils.split(startTime, ":")[1]);
//
//        String endTime = offLineEndTime;
//        Integer endHour = Integer.parseInt(StringUtils.split(endTime, ":")[0]);
//        Integer endMinute = Integer.parseInt(StringUtils.split(endTime, ":")[1]);
//
//        SysJob startJob = new SysJob();
//        SysJob endJob = new SysJob();
//        JSONObject config = new JSONObject();
//        config.put("offStatus", offStatus);
//        config.put("visitorUrl", visitorUrl);
//        config.put("startTime", startTime);
//        config.put("endTime", endTime);
//
//        startJob.setName("网站下线");
//        startJob.setCronExpression(String.format(cron, startMinute, startHour));
//        startJob.setStatus("1");
//        startJob.setType("service");
//        startJob.setContent("websiteDealTimer.upOrDownLine()");
//        startJob.setDataId(websiteId);
//        startJob.setDataType("WebOffLine");
//        startJob.setExecStatus(0);
//        startJob.setCreateTime(new Date());
//        startJob.setUpdateTime(new Date());
//        startJob.setConfig(config);
//
//
//        endJob.setName("网站上线");
//        endJob.setCronExpression(String.format(cron, endMinute, endHour));
//        endJob.setStatus("1");
//        endJob.setType("service");
//        endJob.setContent("websiteDealTimer.upOrDownLine()");
//        endJob.setDataId(websiteId);
//        endJob.setDataType("WebOffLine");
//        endJob.setExecStatus(0);
//        endJob.setCreateTime(new Date());
//        endJob.setUpdateTime(new Date());
//        endJob.setConfig(config);
//
//        if (jobList == null || jobList.size() == 0) {
//            jobList = new ArrayList<>();
//            jobList.add(startJob);
//            jobList.add(endJob);
//            jobMapper.addList(jobList);
//        } else {
//            startJob.setId(jobList.get(0).getId());
//            jobMapper.update(startJob);
//
//            endJob.setId(jobList.get(1).getId());
//            jobMapper.update(endJob);
//        }
        return false;
    }

    public Website getWebsite(String id) {
        Website website;
        if (id.length() == UuidUtils.LEN) {
            website = this.getByPubId(id);
        } else if (Utils.isPositive(id)) {
            website = ((WebsiteService) AopContext.currentProxy()).get(Integer.valueOf(id));
        } else {
            Integer decPageId = DES.decrypt(id);
            website = ((WebsiteService) AopContext.currentProxy()).get(decPageId);
        }
        return website;
    }

    /**
     * 获取管理的网站列表 包括隐藏网站
     *
     * @param wfwfid
     * @param uid
     * @param status
     * @return
     */
    public List<Website> getManageWebsiteListAll(Integer wfwfid, Integer uid, Integer status, Integer pageId) {
        List<Website> websites = new ArrayList<>();
        String domain = Utils.portalDomain(request);
        if (SpringContextHolder.isMirror() && Utils.isIP(domain)) {
            if (showWebsiteAll) {
                websites = websiteMapper.getByWfwfid(wfwfid, status);
            } else {
                websites = websiteMapper.getByPageId(pageId);
            }
        } else {
            websites = websiteMapper.getByWfwfidAll(wfwfid, status);
        }
        UserAppPermission userAppPermission = userAppPermissionService.getByFidUid(wfwfid, uid);
        List<UserAppPermissionNew> uaps = userAppPermissionNewService.getListByWfwfid(wfwfid, uid, UserAppPermissionNew.TYPE_APP);

        // 分组取出roleFlag最小的那条记录
        Map map = uaps.stream().collect(Collectors.groupingBy(uap -> uap.getWebId(),
                Collectors.collectingAndThen(Collectors.reducing((c1, c2) -> c1.getRoleFlag() < c2.getRoleFlag() ? c1 : c2), Optional::get)));

        String webs = "";
        String appweb = "";
        if (Objects.nonNull(userAppPermission)) {
            webs = StringUtils.isBlank(userAppPermission.getWebs()) ? "" : userAppPermission.getWebs();
            appweb = StringUtils.isBlank(userAppPermission.getAppWebs()) ? "" : userAppPermission.getAppWebs();
        }
        String[] haswebs = webs.split(",");
        String[] appwebs = appweb.split(",");
        List<Website> listWebs = new ArrayList<>();
        // 确认哪些是自己的网站
        for (Website ws : websites) {
            if (ws.getCreateUser().equals(uid)) {
                ws.setIsSelf(2);
                listWebs.add(ws);
            } else {
                boolean matchWeb = false;
                for (String s : haswebs) {
                    if (s.equals(String.valueOf(ws.getId()))) {
                        ws.setIsSelf(2);
                        listWebs.add(ws);
                        matchWeb = true;
                        break;
                    }
                }
                if (!matchWeb) {
                    for (String s : appwebs) {
                        if (s.equals(String.valueOf(ws.getId()))) {
                            ws.setIsSelf(0);
                            listWebs.add(ws);
                            break;
                        }
                    }
                }

                // 从新的权限列表里面获取网站。
                Object obj = map.get(ws.getId());
                if (null != obj) {
                    UserAppPermissionNew uapn = (UserAppPermissionNew) (obj);
                    if (uapn.getRoleFlag() <= Constants.ROLE_PAGE_ADMIN) {
                        ws.setIsSelf(1);
                    } else {
                        ws.setIsSelf(0);
                    }
                    // 超级管理员
                    if (uapn.getRoleFlag() <= Constants.ROLE_SUPER_ADMIN) {
                        ws.setIsSelf(2);
                    }
                    listWebs.add(ws);
                }
            }
        }
        return listWebs;
    }


    /**
     * 刷新网站缓存
     */
    public void refreshCache(Integer websiteId, Boolean needUpdateVersion) {
        Website website = this.websiteMapper.get(websiteId);
        Optional.ofNullable(website).orElseThrow(() -> new BusinessException(ErrorCode.CODE_DATA_ERROR.getCode()));

        // 补齐websiteDomain 第三方域名兼容情况
        WebsiteDomain websiteDomain = websiteDomainService.getByWebsiteId(websiteId);
        if(Objects.nonNull(websiteDomain)){
            website.setDomainExternal(websiteDomain.getDomain());
        }
        // 修改版本号
        if (needUpdateVersion != null && needUpdateVersion) {
            this.websiteMapper.updateCdnVersion(websiteId);
        }

        // 清除超星获取域名对应网站缓存
        String keyw = "WebsiteService:id:" + website.getId();
        cacheService.removeCacheKey(Constants.CACHE_NAME, keyw);
        String keyP = "WebsiteService:pubId:" + website.getId();
        cacheService.removeCacheKey(Constants.CACHE_NAME, keyP);
        // 清除超星获取域名对应网站缓存
        String key1 = "WebsiteService:interceptor-domain:" + website.getDomain();
        cacheService.removeCacheKey(Constants.CACHE_NAME, key1);

        if (StringUtils.isNotEmpty(website.getDomain())) {
            String keyDel = "PortalService:checkLogin:domain:" + website.getDomain();
            cacheService.removeCacheKey(Constants.CACHE_NAME, keyDel);
        }
        if (StringUtils.isNotEmpty(website.getDomainExternal())) {
            String keyDel = "PortalService:checkLogin:domain:" + website.getDomainExternal();
            cacheService.removeCacheKey(Constants.CACHE_NAME, keyDel);
        }

        // 清除第三方域名网站缓存
        if (StringUtils.isNotBlank(website.getDomainExternal())) {
            String key3rd = "WebsiteService:external-domain:" + website.getDomainExternal();
            cacheService.removeCacheKey("objectCache", key3rd);
        }

        // 清除页面缓存
        cacheService.removeCacheKey(Constants.CACHE_NAME, "page-content-preview:pageId:" + website.getHomePageId() + ":isMobile:0");
        cacheService.removeCacheKey(Constants.CACHE_NAME, "page-content-preview:pageId:" + website.getHomePageId() + ":isMobile:1");
        // 清除头和底的缓存
        cacheService.removeCacheKey(Constants.CACHE_NAME, "header:getTopDiv:pageId:" + website.getHomePageId() + ":index:0");
        cacheService.removeCacheKey(Constants.CACHE_NAME, "header:getTopDiv:pageId:" + website.getHomePageId() + ":index:1");
        cacheService.removeCacheKey(Constants.CACHE_NAME, "footer:getBottomDiv:pageId:" + website.getHomePageId() + ":index");

        // 刷新engine2的二级页面头底缓存
        this.evictHeaderCache(website.getHomePageId());

        // 查询出来所有的pages，依次清除缓存。
        List<Page> pages = pageService.getByWebsiteId(website.getId());
        // 刷新engine2的二级页面头底缓存

        // 清除插件的缓存
        cacheService.removeCacheKey(Constants.CACHE_NAME, "WebsitePlugsService:websiteId:" + website.getId() + ":pageId:" + website.getHomePageId() + ":type:1");

        cacheService.removeCacheKey(Constants.CACHE_NAME, "WebsitePlugsService:websiteId:" + website.getId() + ":type-html:2" + ":pageId:" + website.getHomePageId());
        cacheService.removeCacheKey(Constants.CACHE_NAME, "WebsitePlugsService:websiteId:" + website.getId() + ":type-html:3" + ":pageId:" + website.getHomePageId());
        cacheService.removeCacheKey(Constants.CACHE_NAME, "WebsitePlugsService:websiteId:" + website.getId() + ":type-html:10" + ":pageId:" + website.getHomePageId());
        pages.forEach(p -> {
            String key = "page-content-preview:pageId:" + p.getId();
            cacheService.removeCacheKey(Constants.CACHE_NAME, key + ":isMobile:0");
            cacheService.removeCacheKey(Constants.CACHE_NAME, key + ":isMobile:1");

            // 刷新头部缓存；
            String key2 = "header:getTopDiv:pageId:" + p.getId() + ":index:0";
            cacheService.removeCacheKey(Constants.CACHE_NAME, key2);

            // 刷新engine2的二级页面头底缓存
            this.evictHeaderCache(p.getId());

            cacheService.removeCacheKey(Constants.CACHE_NAME, "WebsitePlugsService:websiteId:" + website.getId() + ":pageId:" + p.getId() + ":type:1");

            cacheService.removeCacheKey(Constants.CACHE_NAME, "WebsitePlugsService:websiteId:" + website.getId() + ":type-html:2" + ":pageId:" + p.getId());
            cacheService.removeCacheKey(Constants.CACHE_NAME, "WebsitePlugsService:websiteId:" + website.getId() + ":type-html:3" + ":pageId:" + p.getId());
            cacheService.removeCacheKey(Constants.CACHE_NAME, "WebsitePlugsService:websiteId:" + website.getId() + ":type-html:10" + ":pageId:" + p.getId());

            //清除预览页webJson缓存
            cacheService.removeCacheKey(Constants.CACHE_NAME, "WebJsonService:getPreview:" + p.getWebJsonId());
            cacheService.removeCacheKey(Constants.CACHE_NAME, "WebJsonService:id:" + p.getWebJsonId());
        });
    }

    public void checkDomain(String domain, Integer websiteId) {
        if (!SpringContextHolder.isMirror() && testVersion != 1){
            domain = domain.replace(".mh.chaoxing.com", "");
            domain = domain.replace(".portal.chaoxing.com", "");
            Website website = this.getByDomain(domain);
            if (!website.getId().equals(websiteId)){
                throw new BusinessException(ErrorCode.CODE_COOKIE_DOMAIN_ERROR.getCode());
            }
        }
    }

    public Website getByCloneCondition(Integer uid, Integer websiteId,Integer wfwfid) {
        Website byOriginAndUidAndWfwfid = websiteMapper.getByOriginAndUidAndWfwfid(uid, websiteId, wfwfid);
        return byOriginAndUidAndWfwfid;
    }
    public PageInfo listV2TestWebsiteId(Integer pageNum, Integer pageSize, Integer websiteId, Integer wfwfid) {
        com.github.pagehelper.Page<Website> page = PageHelper.startPage(pageNum, pageSize);
        websiteMapper.listV2TestWebsiteId(websiteId, wfwfid);
        return new PageInfo<>(page);
    }

    // 此接口仅限超管后台使用
    public PageInfo listWebsiteCloneList(Integer pageNum, Integer pageSize, Integer status, Integer wfwfid) {
        com.github.pagehelper.Page<Website> page = PageHelper.startPage(pageNum, pageSize);
        websiteMapper.listWebsiteCloneList(status, wfwfid);
        return new PageInfo<>(page);
    }

    // 根据网站状态判断下线，返回true说明下线
    public boolean offWebsite(Integer isMobileInt, Website website, Boolean isHeaderCache, WebsiteConfig websiteConfig) {
        boolean off = false;
        if (StringUtils.isNotEmpty(website.getAsyncLogoutUrl())) {
            if (Objects.isNull(websiteConfig)) { // 地址不为空，配置表是空，走下线逻辑
                return true;
            }
            Integer offStatus = this.getOffStatus(website, websiteConfig);
            if (Objects.isNull(offStatus)) {
                return true;
            }

            if (Website.STATUS_SHOW.equals(offStatus)) {
                return false;
            }
            boolean isMobile = isMobileInt.equals(Constants.STATUS_TRUE);
            if (Website.STATUS_OFF.equals(offStatus)) {
                off = true;
            } else if (Website.STATUS_APP_OFF.equals(offStatus) && isMobile) {
                off = true;
            } else if (Website.STATUS_PC_OFF.equals(offStatus) && !isMobile) {
                off = true;
            }
            // 用户缓存的数据，这里直接返回，不判断时间范围了
            if(isHeaderCache){
                return off;
            }

            // 判断是否在下线时间范围内，
            String upOffTime = websiteConfig.getUpOffTime();
            if(StringUtils.isNotEmpty(upOffTime)){
                String[] upOffTimes = upOffTime.split("-");
                if(upOffTimes.length == 2) {
                    String startTimeStr = upOffTimes[0];
                    String endTimeStr = upOffTimes[1];
                    String currentDateStr = DateUtils.formatDate(new Date(), "yyyy-MM-dd");
                    try {
                        Date d1 = DateUtils.string2Date(currentDateStr + startTimeStr, "yyyy-MM-ddHH:mm");
                        Date d2 = DateUtils.string2Date(currentDateStr + endTimeStr, "yyyy-MM-ddHH:mm");
                        Long currentTime = System.currentTimeMillis();
                        if(d1.after(d2)) {  //隔天的情况
                            if (currentTime >= d1.getTime() || currentTime <= d2.getTime()) {
                                // 在下线时间范围内
                                off = true;
                            } else {
                                off = false;
                            }
                        } else { //当天的情况
                            if (currentTime >= d1.getTime() && currentTime <= d2.getTime()){
                                // 在下线时间范围内
                                off = true;
                            }else {
                                off = false;
                            }
                        }
                    } catch (ParseException e) {
                        e.printStackTrace();
                    }
                }
            }
        }
        return off;
    }

    private Integer getOffStatus(Website website, WebsiteConfig websiteConfig){
        return websiteConfig.getOffStatus();
    }

    public String offUrl(Integer isMobileInt, Website website) {
        if (StringUtils.isNotEmpty(website.getAsyncLogoutUrl())) {
            // 只有offWebsite 判断是下线的时候才返回下线地址
            WebsiteConfig websiteConfig = websiteConfigService.get(website.getId());
            if(offWebsite(isMobileInt, website, true, websiteConfig)){
                return website.getAsyncLogoutUrl();
            }
        }
        return  StringUtils.EMPTY;
    }

    public void evictHeaderCache(Integer pageId) {
        cacheService.removeCacheKey(Constants.CACHE_NAME, "GeneralDataService:getTopBottom:pageId:" + pageId + ":isMobile:0");
        cacheService.removeCacheKey(Constants.CACHE_NAME, "GeneralDataService:getTopBottom:pageId:" + pageId + ":isMobile:1");
        cacheService.removeCacheKey(Constants.CACHE_NAME, "GeneralDataService:getPageMsg:pageId:" + pageId);
        cacheService.removeCacheKey(Constants.CACHE_NAME, "GeneralDataService:getWebsiteMsg:pageId:" + pageId + ":isMobile:false");
        cacheService.removeCacheKey(Constants.CACHE_NAME, "GeneralDataService:getWebsiteMsg:pageId:" + pageId + ":isMobile:true");
    }

    public void evictHeaderCache(Integer pageId, Cache cache) {
        cache.evict("GeneralDataService:getTopBottom:pageId:" + pageId + ":isMobile:0");
        cache.evict("GeneralDataService:getTopBottom:pageId:" + pageId + ":isMobile:1");
        cache.evict("GeneralDataService:getPageMsg:pageId:" + pageId);
        cache.evict("GeneralDataService:getWebsiteMsg:pageId:" + pageId + ":isMobile:false");
        cache.evict("GeneralDataService:getWebsiteMsg:pageId:" + pageId + ":isMobile:true");
    }

    /**
     * 网站参数链接信息替换
     */
    public void replaceUrlAndParams(Website targetWebsite,  Website sourceWebsite) {
        String loginUrlTarget = targetWebsite.getLoginUrl();
        if(StringUtils.isEmpty(loginUrlTarget)){
            return;
        }
        if(!StringUtils.contains(loginUrlTarget,"mh.chaoxing.com") && !loginUrlTarget.startsWith("/")){
            return;
        }

        String loginUrl = StringUtils.replace(targetWebsite.getLoginUrl(), sourceWebsite.getDomain(), targetWebsite.getDomain());
        String logoutUrl = StringUtils.replace(targetWebsite.getLogoutUrl(), sourceWebsite.getDomain(), targetWebsite.getDomain());

        //
        List<Page> targetPageList = pageService.getByWebsiteId(targetWebsite.getId());
        List<Integer> targetPageIds = targetPageList.stream().map(Page::getId).collect(Collectors.toList());
        List<OriginRel> originRels = originRelMapper.getList(targetPageIds, OriginRel.TYPE_PAGE);
        if(!CollectionUtils.isEmpty(originRels)){
            for(OriginRel originRel : originRels){
                loginUrl = StringUtils.replace(loginUrl, originRel.getOriginId().toString(), originRel.getTargetId().toString());
            }
        } else { // 查询当前网站的登录页page/id/show 格式的id值
            logger.error("替换前loginUrl:"+loginUrl);
            Integer pageId = RegexUtil.getPageId2(loginUrl);
            logger.error("pageId:"+pageId);
            for(Page p : targetPageList){
                if(Template.isLoginTemp(p.getTemplate()) && pageId > Constants.STATUS_TRUE){
                    loginUrl = StringUtils.replace(loginUrl, pageId.toString(), p.getId().toString());
                    break;
                }
            }
        }
        logger.error("替换后loginUrl:"+loginUrl);
        targetWebsite.setLoginUrl(loginUrl);
        targetWebsite.setLogoutUrl(logoutUrl);
    }

    public Integer getProductType(Integer websiteId){
        return websiteMapper.getProductType(websiteId);
    }

    public void updateCreateUser(Integer fid, Integer oldUid, Integer newUid) {
        websiteMapper.updateCreateUser(fid, oldUid, newUid);
    }

    public void updateCreateUserByWebsiteId(Integer websiteId, Integer uid) {
        websiteMapper.updateCreateUserByWebsiteId(websiteId, uid);
    }

    public List<Website> getByWfwfidAll(Integer wfwfid, Integer status) {
        return websiteMapper.getByWfwfidAll(wfwfid, status);
    }

    public void updateWebType(Integer websiteId, Integer webType) {
        if (websiteId != null && webType != null) {
            websiteMapper.updateWebType(websiteId, webType);
        }
    }

}

