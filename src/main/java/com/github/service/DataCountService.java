package com.github.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.enums.ErrorCode;
import com.github.model.*;
import com.github.model.extend.DataScan;
import com.github.model.extend.StationsPv;
import com.github.model.vo.StationPvOriginVo;
import com.github.util.*;
import com.github.util.exception.BusinessException;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.text.ParseException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by ZhouXinyu on 2020/10/10 10:39.
 */

@Service
public class DataCountService {
    @Resource
    private WebsitePvService websitePvService;
    @Resource
    private EsDataService esDataService;
    @Resource
    private WebsiteService websiteService;
    @Resource
    private PageService pageService;
    @Resource
    private RestTemplate restTemplate;
    @Resource
    private ApiService apiService;
    @Resource
    private DataCountService dataCountService;
    @Resource
    private InstanceService instanceService;
    @Resource
    private StationsService stationsService;
    @Resource
    private WebVisitMultiService webVisitMultiService;
    @Resource
    private UserService userService;
    @Resource
    private ProxyUrlCountService proxyUrlCountService;

    @Value("${engine-inner.domain-name}")
    private String domainInner;
    @Resource
    private HttpServletRequest request;

    private final Logger logger = LoggerFactory.getLogger(DataCountService.class);

    public List<WebsitePv> getWebsitePvList(Integer websiteId, String startTime, String endTime, Integer type) {
        List<WebsitePv> websitePvList = new ArrayList<>();

        if (StringUtils.isNotBlank(endTime) && endTime.compareTo(WebsitePvService.SEPARATE_TIME_STR) < 0) {
            // 查询分割时间以前的数据，从MYSQL查
            websitePvList = websitePvService.getPvByGroupTime(websiteId, startTime, endTime, type);
        } else if (StringUtils.isNotBlank(startTime) && startTime.compareTo(WebsitePvService.SEPARATE_TIME_STR) >= 0) {
            // 查询分割时间以后的数据，从ES查
            websitePvList = esDataService.websitePvList(websiteId, websitePvService.getDatestamp(startTime), websitePvService.getDatestamp(endTime), type);
        } else {
            List<WebsitePv> mysqlDataList = websitePvService.getPvByGroupTime(websiteId, startTime, websitePvService.getSeparateDateStr(), type);
            List<WebsitePv> esDataList = esDataService.websitePvList(websiteId, websitePvService.getDatestamp(WebsitePvService.SEPARATE_TIME_STRES), websitePvService.getDatestamp(endTime), type);

            websitePvList = websitePvService.mergeWebsitePvList(mysqlDataList, esDataList, type);
        }
        return websitePvList;
    }

    public void exportWebsitePv(Integer websiteId, Integer type, List<WebsitePv> websitePvList, HttpServletResponse response) {
        Workbook wb = new XSSFWorkbook();
        Sheet sheet = wb.createSheet();
        sheet.setColumnWidth(0, 15 * 256);
        Row row1 = sheet.createRow(0);
        int columnIndex = 0;
        row1.createCell(columnIndex++).setCellValue("日期");
        row1.createCell(columnIndex++).setCellValue("访问量");

        for (int i = 0; i < websitePvList.size(); i++) {
            WebsitePv websitePv = websitePvList.get(i);
            Row row = sheet.createRow(i + 1);
            columnIndex = 0;
            row.createCell(columnIndex++).setCellValue(websitePv.getTime());
            row.createCell(columnIndex++).setCellValue(websitePv.getPv());
        }
        try (OutputStream stream = response.getOutputStream()) {
            // 返回xlsx文件类型 设置为二进制流,
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment;fileName=" + URLEncoder.encode("网站" + websiteId + getDateNameByType(type) + "访问量统计.xlsx", "UTF-8"));
            // 写入数据
            wb.write(stream);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void exportWebsiteAppVisit(Integer websiteId, Map<String, Object> appVisitMap, HttpServletResponse response) {
        Workbook wb = new XSSFWorkbook();
        Sheet sheet = wb.createSheet();
        sheet.setColumnWidth(0, 15 * 256);
        sheet.setColumnWidth(1, 15 * 256);
        Row row1 = sheet.createRow(0);
        int columnIndex = 0;
        row1.createCell(columnIndex++).setCellValue("应用类型");
        row1.createCell(columnIndex++).setCellValue("应用名称");
        row1.createCell(columnIndex++).setCellValue("列表访问量");
        row1.createCell(columnIndex++).setCellValue("详情访问量");
        row1.createCell(columnIndex++).setCellValue("总访问量");
        row1.createCell(columnIndex++).setCellValue("资源量");

        List<Map<String, String>> appList = new ArrayList<>();
        appList.addAll((List<Map<String, String>>) appVisitMap.get("text"));
        appList.addAll((List<Map<String, String>>) appVisitMap.get("graphic"));
        appList.addAll((List<Map<String, String>>) appVisitMap.get("image"));
        appList.addAll((List<Map<String, String>>) appVisitMap.get("icon"));
        for (int i = 0; i < appList.size(); i++) {
            Map<String, String> map = appList.get(i);
            Row row = sheet.createRow(i + 1);
            columnIndex = 0;
            row.createCell(columnIndex++).setCellValue(map.get("type"));
            row.createCell(columnIndex++).setCellValue(map.get("appName"));
            row.createCell(columnIndex++).setCellValue(map.get("count"));
            row.createCell(columnIndex++).setCellValue(map.get("article"));
            row.createCell(columnIndex++).setCellValue(map.get("total"));
            row.createCell(columnIndex++).setCellValue(map.get("resourceCount"));
        }
        try (OutputStream stream = response.getOutputStream()) {
            // 返回xlsx文件类型 设置为二进制流,
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment;fileName=" + URLEncoder.encode("网站" + websiteId + "应用访问量统计.xlsx", "UTF-8"));
            // 写入数据
            wb.write(stream);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public String getDateNameByType(Integer type) {
        if (Constants.PV_GROUP_YEAR.equals(type)) {
            return "年";
        } else if (Constants.PV_GROUP_MONTH.equals(type)) {
            return "月";
        } else {
            return "日";
        }
    }

    public int getModuleCount(Integer websiteId) {
        List<Page> listPage = pageService.getByWebsiteId(websiteId);
        Set<String> appIds = new HashSet<>();
        for (Page page : listPage) {
            appIds.addAll(pageService.getPageAppIds(page.getWebJsonId(), Template.isV2Tempalte(page.getTemplate())));
        }
        return appIds.size();
    }

    public int getTotalArticle(Integer websiteId) {
        Website website = websiteService.get(websiteId);
        //根据website找到该网站下的所有应用
        List<Page> pages = pageService.getByWebsiteId(websiteId);
        Set<String> setApps = new HashSet<>();
        for(Page p : pages){
            setApps.addAll(pageService.getPageAppIds(p.getWebJsonId(), Template.isV2Tempalte(p.getTemplate())));
        }
        setApps.remove("1");
        setApps.remove("2");
        setApps.remove("3");

        // 获取首页头部对应图文引擎的appId
        String headerUrl = String.format(domainInner+"/engine2/api/header/appId?pageId=%s", website.getHomePageId());
        JSONObject headerResult = restTemplate.getForObject(headerUrl, JSONObject.class);
        String headerAppId = headerResult.getString("data");
        if (headerAppId != null) {
            setApps.add(headerAppId);
        }

        String appIdStr = StringUtils.join(setApps, ",");
        if(StringUtils.isNotBlank(appIdStr)){
            String url = String.format(domainInner+"/engine2/internal/api/data/general/total", appIdStr, website.getWfwfid(), 0);
            MultiValueMap<String, String> parameterMap = new LinkedMultiValueMap<>();
            parameterMap.add("appIds", appIdStr);
            parameterMap.add("wfwfid", String.valueOf(website.getWfwfid()));
            parameterMap.add("websiteId", "0");
            JSONObject jsonObject = restTemplate.postForObject(url, parameterMap, JSONObject.class);
            return jsonObject.getInteger("data");
        } else {
            return 0;
        }
    }

    public int getTotalArticle(Integer websiteId, Integer instanceId, Integer typeId) {
        Website website = websiteService.get(websiteId);
        //根据website找到该网站下的所有应用
        List<Page> pages = pageService.getByWebsiteId(websiteId);
        Set<String> setApps = new HashSet<>();
        for(Page p : pages){
            setApps.addAll(pageService.getPageAppIds(p.getWebJsonId(), Template.isV2Tempalte(p.getTemplate())));
        }
        setApps.remove("1");
        setApps.remove("2");
        setApps.remove("3");

        // 获取首页头部对应图文引擎的appId
        String headerUrl = String.format(domainInner+"/engine2/api/header/appId?pageId=%s", website.getHomePageId());
        JSONObject headerResult = restTemplate.getForObject(headerUrl, JSONObject.class);
        String headerAppId = headerResult.getString("data");
        if (headerAppId != null) {
            setApps.add(headerAppId);
        }

        String appIdStr = StringUtils.join(setApps, ",");
        if(StringUtils.isNotBlank(appIdStr)){
            String url = String.format(domainInner+"/engine2/internal/api/data/general/total", appIdStr, website.getWfwfid(), 0);
            MultiValueMap<String, String> parameterMap = new LinkedMultiValueMap<>();
            parameterMap.add("appIds", appIdStr);
            parameterMap.add("wfwfid", String.valueOf(website.getWfwfid()));
            parameterMap.add("websiteId", "0");
            JSONObject jsonObject = restTemplate.postForObject(url, parameterMap, JSONObject.class);
            return jsonObject.getInteger("data");
        } else {
            return 0;
        }
    }

    public List<Integer> setDayAndTotalArticle(Integer websiteId) {
        Website website = websiteService.get(websiteId);
        //根据website找到该网站下的所有应用
        List<Page> pages = pageService.getByWebsiteId(websiteId);
        Set<String> setApps = new HashSet<>();
        for(Page p : pages){
            setApps.addAll(pageService.getPageAppIds(p.getWebJsonId(), Template.isV2Tempalte(p.getTemplate())));
        }
        setApps.remove("1");
        setApps.remove("2");
        setApps.remove("3");

        // 获取首页头部对应图文引擎的appId
        String headerUrl = String.format(domainInner + "/engine2/api/header/appId?pageId=%s", website.getHomePageId());
        JSONObject headerResult = restTemplate.getForObject(headerUrl, JSONObject.class);
        String headerAppId = headerResult.getString("data");
        if (headerAppId != null) {
            setApps.add(headerAppId);
        }

        String appIdStr = StringUtils.join(setApps, ",");
        if (StringUtils.isNotBlank(appIdStr)) {
            String url = domainInner + "/engine2/internal/api/data/general/day-and-total";
            MultiValueMap<String, String> parameterMap = new LinkedMultiValueMap<>();
            parameterMap.add("appIds", appIdStr);
            parameterMap.add("wfwfid", String.valueOf(website.getWfwfid()));
            parameterMap.add("websiteId", "0");
            JSONObject jsonObject = restTemplate.postForObject(url, parameterMap, JSONObject.class);
            JSONObject json = jsonObject.getJSONObject("data");
            return Arrays.asList(Optional.ofNullable(json.getInteger("total")).orElse(0), Optional.ofNullable(json.getInteger("dayTotal")).orElse(0));
        } else {
            return Arrays.asList(0, 0);
        }
    }

    /**
     * 获取今日的文章数
     * @param websiteId
     * @return
     */
    public int getTotalArticleAndDay(Integer websiteId) {
        Website website = websiteService.get(websiteId);
        //根据website找到该网站下的所有应用
        List<Page> pages = pageService.getByWebsiteId(websiteId);
        Set<String> setApps = new HashSet<>();
        for(Page p : pages){
            setApps.addAll(pageService.getPageAppIds(p.getWebJsonId(), Template.isV2Tempalte(p.getTemplate())));
        }
        setApps.remove("1");
        setApps.remove("2");
        setApps.remove("3");

        // 获取首页头部对应图文引擎的appId
        String headerUrl = String.format(domainInner+"/engine2/api/header/appId?pageId=%s", website.getHomePageId());
        JSONObject headerResult = restTemplate.getForObject(headerUrl, JSONObject.class);
        String headerAppId = headerResult.getString("data");
        if (headerAppId != null) {
            setApps.add(headerAppId);
        }

        String appIdStr = StringUtils.join(setApps, ",");
        if(StringUtils.isNotBlank(appIdStr)){
            String url = String.format(domainInner+"/engine2/internal/api/data/general/day-and-total", appIdStr, website.getWfwfid(), 0);
            MultiValueMap<String, String> parameterMap = new LinkedMultiValueMap<>();
            parameterMap.add("appIds", appIdStr);
            parameterMap.add("wfwfid", String.valueOf(website.getWfwfid()));
            parameterMap.add("websiteId", website.getId()+"");
            JSONObject jsonObject = restTemplate.postForObject(url, parameterMap, JSONObject.class);
            return jsonObject.getJSONObject("data").getInteger("dayTotal");
        } else {
            return 0;
        }
    }

    public Map<String, Long> getTimeRangeMap(Integer type, String startTime, String endTime) {
        Map<String, Long> map = new HashMap<>();
        Calendar c1 = Calendar.getInstance();
        c1.set(Calendar.HOUR_OF_DAY, 0);
        c1.set(Calendar.MINUTE, 0);
        c1.set(Calendar.SECOND, 0);
        Long start = null;
        Long end = null;
        // 昨日
        if (StringUtils.isNotBlank(startTime) || StringUtils.isNotBlank(endTime)) {
            try {
                if(StringUtils.isNotBlank(startTime)) {
                    start = DateUtils.parseDate(startTime, new String[]{"yyyy-MM-dd"}).getTime();
                }
                if(StringUtils.isNotBlank(endTime)) {
                    end = DateUtils.addDays(DateUtils.parseDate(endTime, new String[]{"yyyy-MM-dd"}),1).getTime();
                }
            } catch (ParseException e) {
                e.printStackTrace();
            }
        } else if (Constants.STATISTIC_TIME_TYPE_RECENT_7_DAY.equals(type)) {
            start = DateUtils.addDays(c1.getTime(),-6).getTime();
            end = DateUtils.addDays(c1.getTime(),1).getTime();
            // 近30天
        } else if(Constants.STATISTIC_TIME_TYPE_RECENT_30_DAY.equals(type)){
            start = DateUtils.addDays(c1.getTime(),-29).getTime();
            end = DateUtils.addDays(c1.getTime(),1).getTime();
        }
        map.put("startTime", start);
        map.put("endTime", end);
        return map;
    }

    public void exportWebsitePvData(Integer websiteId, List<DataScan> list, HttpServletResponse response) {
        Workbook wb = new XSSFWorkbook();
        Sheet sheet = wb.createSheet();
        sheet.setColumnWidth(0, 15 * 256);
        sheet.setColumnWidth(1, 15 * 256);
        sheet.setColumnWidth(2, 20 * 256);
        sheet.setColumnWidth(3, 25 * 256);
        Row row1 = sheet.createRow(0);
        int columnIndex = 0;
        row1.createCell(columnIndex++).setCellValue("IP");
        row1.createCell(columnIndex++).setCellValue("UID");
        row1.createCell(columnIndex++).setCellValue("访问时间");
        row1.createCell(columnIndex++).setCellValue("访问记录");

        for (int i = 0; i < list.size(); i++) {
            DataScan dataScan = list.get(i);
            Row row = sheet.createRow(i + 1);
            columnIndex = 0;
            row.createCell(columnIndex++).setCellValue(dataScan.getIp());
            row.createCell(columnIndex++).setCellValue(dataScan.getUid() == null ? "" : String.valueOf(dataScan.getUid()));
            row.createCell(columnIndex++).setCellValue(DateFormatUtils.format(dataScan.getTime(), "yyyy-MM-dd HH:mm"));
            row.createCell(columnIndex++).setCellValue(dataScan.getvPath());
        }
        try (OutputStream stream = response.getOutputStream()) {
            // 返回xlsx文件类型 设置为二进制流,
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment;fileName=" + URLEncoder.encode("网站" + websiteId + "访问量统计.xlsx", "UTF-8"));
            // 写入数据
            wb.write(stream);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void exportWebsiteUvData(Integer websiteId, List<Map<String, Object>> list, HttpServletResponse response) {
        Workbook wb = new XSSFWorkbook();
        Sheet sheet = wb.createSheet();
        sheet.setColumnWidth(0, 15 * 256);
        sheet.setColumnWidth(1, 15 * 256);
        Row row1 = sheet.createRow(0);
        int columnIndex = 0;
        row1.createCell(columnIndex++).setCellValue("日期");
        row1.createCell(columnIndex++).setCellValue("访客数");

        for (int i = 0; i < list.size(); i++) {
            Map<String, Object> map = list.get(i);
            Row row = sheet.createRow(i + 1);
            columnIndex = 0;
            row.createCell(columnIndex++).setCellValue(String.valueOf(map.get("time")));
            row.createCell(columnIndex++).setCellValue(String.valueOf(map.get("uv")));
        }
        try (OutputStream stream = response.getOutputStream()) {
            // 返回xlsx文件类型 设置为二进制流,
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment;fileName=" + URLEncoder.encode("网站" + websiteId + "访客数统计.xlsx", "UTF-8"));
            // 写入数据
            wb.write(stream);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void exportWebsitePvCount(Integer websiteId, List<Map<String, Object>> list, HttpServletResponse response) {
        Workbook wb = new XSSFWorkbook();
        Sheet sheet = wb.createSheet();
        sheet.setColumnWidth(0, 15 * 256);
        sheet.setColumnWidth(1, 15 * 256);
        Row row1 = sheet.createRow(0);
        int columnIndex = 0;
        row1.createCell(columnIndex++).setCellValue("日期");
        row1.createCell(columnIndex++).setCellValue("访问量");

        for (int i = 0; i < list.size(); i++) {
            Map<String, Object> map = list.get(i);
            Row row = sheet.createRow(i + 1);
            columnIndex = 0;
            row.createCell(columnIndex++).setCellValue(String.valueOf(map.get("time")));
            row.createCell(columnIndex++).setCellValue(String.valueOf(map.get("pv")));
        }
        try (OutputStream stream = response.getOutputStream()) {
            // 返回xlsx文件类型 设置为二进制流,
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment;fileName=" + URLEncoder.encode("网站" + websiteId + "访问量统计.xlsx", "UTF-8"));
            // 写入数据
            wb.write(stream);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public Set<String> getAppsByWebsiteId(Integer websiteId) {
        //根据website找到该网站下的所有应用
        List<Page> pages = pageService.getByWebsiteId(websiteId);
        Set<String> setApps = new HashSet<>();
        for(Page p : pages){
            setApps.addAll(pageService.getPageAppIds(p.getWebJsonId(), Template.isV2Tempalte(p.getTemplate())));
        }
        setApps.remove("1");
        setApps.remove("2");
        setApps.remove("3");
        return setApps;
    }

    public void fillSearchName(List<Map<String, String>> searchList) {
        StringBuffer searchIds = new StringBuffer();
        searchList.forEach(seach -> {
            searchIds.append(seach.get("searchId")).append(",");
        });
        if (searchIds.length() > 0) {
            MultiValueMap<String, String> parameterMap = new LinkedMultiValueMap<>();
            parameterMap.add("idStr", searchIds.substring(0, searchIds.length() - 1));
            JSONObject searchResult = restTemplate.postForObject(domainInner+"/engine2/api/search/list", parameterMap, JSONObject.class);
            JSONArray searches = searchResult.getJSONArray("data");
            searchList.forEach(search -> {
                boolean found = false;
                for (Object realSearch : searches) {
                    JSONObject searchJson = (JSONObject) realSearch;
                    if (search.get("searchId").equals(searchJson.getString("id"))) {
                        search.put("searchName", searchJson.getString("name"));
                        found = true;
                        break;
                    }
                }
                if (!found) {
                    search.put("searchName", "");
                }
            });
        }
    }

    public Map<String, Object> getArticlePvAndUvData(Integer websiteId, Integer type, String startTime, String endTime,
                                                     Integer instanceId, Integer typeId, Integer page, Integer pageSize) {
        Map<String, Long> timeRangeMap = getTimeRangeMap(type, startTime, endTime);
        Website website = websiteService.get(websiteId);
//        Set<String> appIds = dataCountService.getAppsByWebsiteId(websiteId);
//        JSONObject headerData = instanceService.getHeaderData(website.getHomePageId(), website.getHomePageId(), websiteId, website.getWfwfid());
//        appIds.add(headerData.getString("appId"));
        Map<String, Object> articleVisitMap = esDataService.getArticlePvList(website, null, instanceId, typeId, timeRangeMap.get("startTime"), timeRangeMap.get("endTime"), page, pageSize);
//        System.out.println(" 查询时间： "+ (System.currentTimeMillis() - t1));
        List<Map<String,Object>> list = (List<Map<String, Object>>) articleVisitMap.get("list");
        Set<Integer> typeIds = esDataService.getTypeIds(list);
        // TODO 展示屏蔽，查询太慢
//        esDataService.fillDataUv(list, website, timeRangeMap.get("startTime"), timeRangeMap.get("endTime"));
        JSONObject typeIdNameMap = apiService.getTypeIdNameMap(typeIds);
        esDataService.fillTypeName(list, typeIdNameMap);
        Set<Integer> engineIds = esDataService.getEngineIds(list);
        JSONObject engineIdNameMap = apiService.getEngineIdNameMap(engineIds, websiteId, website.getWfwfid());
        esDataService.fillEngineName(list, engineIdNameMap);
//        esDataService.fillHeaderName(list, headerData.getString("instanceId"));
//        esDataService.fillVPath(list, website, timeRangeMap.get("startTime"), timeRangeMap.get("endTime"));
//        System.out.println("最终查询时间:"+(System.currentTimeMillis() - t1));
        return articleVisitMap;
    }

    private String getDefaultString(Object obj) {
        if (obj == null) {
            return "";
        } else {
            return obj.toString();
        }
    }

    public void exportWebsiteArticleVisitDataDb(Integer websiteId, JSONArray list, HttpServletResponse response) {
        Workbook wb = new XSSFWorkbook();
        Sheet sheet = wb.createSheet();
        sheet.setColumnWidth(0, 15 * 256);
        sheet.setColumnWidth(1, 15 * 256);
        sheet.setColumnWidth(2, 20 * 256);
        sheet.setColumnWidth(3, 15 * 256);
        sheet.setColumnWidth(4, 15 * 256);
        sheet.setColumnWidth(5, 25 * 256);
        Row row1 = sheet.createRow(0);
        int columnIndex = 0;
        row1.createCell(columnIndex++).setCellValue("文章名称");
        row1.createCell(columnIndex++).setCellValue("所属模块");
        row1.createCell(columnIndex++).setCellValue("所属分类");
        row1.createCell(columnIndex++).setCellValue("发布人");
        row1.createCell(columnIndex++).setCellValue("发布时间");
        row1.createCell(columnIndex++).setCellValue("总浏览量");
        row1.createCell(columnIndex++).setCellValue("访问地址");

        for (int i = 0; i < list.size(); i++) {
            JSONObject map = list.getJSONObject(i);
            Row row = sheet.createRow(i + 1);
            columnIndex = 0;
            row.createCell(columnIndex++).setCellValue(getDefaultString(map.get("title")));
            row.createCell(columnIndex++).setCellValue(getDefaultString(map.get("engineName")));
            row.createCell(columnIndex++).setCellValue(getDefaultString(map.get("typeName")));
            row.createCell(columnIndex++).setCellValue(getDefaultString(map.get("userName")));
            row.createCell(columnIndex++).setCellValue(getDefaultString(map.get("publishTime")));
            row.createCell(columnIndex++).setCellValue(getDefaultString(map.get("accessBaseNum")));
            row.createCell(columnIndex++).setCellValue(getDefaultString(map.get("vPath")));
        }
        try (OutputStream stream = response.getOutputStream()) {
            // 返回xlsx文件类型 设置为二进制流,
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment;fileName=" + URLEncoder.encode("网站" + websiteId + "文章浏览量统计.xlsx", "UTF-8"));
            // 写入数据
            wb.write(stream);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void exportWebsiteArticleVisitData(Integer websiteId, List<Map<String, Object>> list, HttpServletResponse response) {
        Workbook wb = new XSSFWorkbook();
        Sheet sheet = wb.createSheet();
        sheet.setColumnWidth(0, 15 * 256);
        sheet.setColumnWidth(1, 15 * 256);
        sheet.setColumnWidth(2, 20 * 256);
        sheet.setColumnWidth(3, 15 * 256);
        sheet.setColumnWidth(4, 15 * 256);
        sheet.setColumnWidth(5, 25 * 256);
        Row row1 = sheet.createRow(0);
        int columnIndex = 0;
        row1.createCell(columnIndex++).setCellValue("文章名称");
        row1.createCell(columnIndex++).setCellValue("所属模块");
        row1.createCell(columnIndex++).setCellValue("所属分类");
        row1.createCell(columnIndex++).setCellValue("独立访问次数");
        row1.createCell(columnIndex++).setCellValue("总浏览量");
        row1.createCell(columnIndex++).setCellValue("访问地址");

        for (int i = 0; i < list.size(); i++) {
            Map<String, Object> map = list.get(i);
            Row row = sheet.createRow(i + 1);
            columnIndex = 0;
            row.createCell(columnIndex++).setCellValue(getDefaultString(map.get("title")));
            row.createCell(columnIndex++).setCellValue(getDefaultString(map.get("engineName")));
            row.createCell(columnIndex++).setCellValue(getDefaultString(map.get("typeName")));
            row.createCell(columnIndex++).setCellValue(getDefaultString(map.get("uv")));
            row.createCell(columnIndex++).setCellValue(getDefaultString(map.get("pv")));
            row.createCell(columnIndex++).setCellValue(getDefaultString(map.get("vPath")));
        }
        try (OutputStream stream = response.getOutputStream()) {
            // 返回xlsx文件类型 设置为二进制流,
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment;fileName=" + URLEncoder.encode("网站" + websiteId + "文章浏览量统计.xlsx", "UTF-8"));
            // 写入数据
            wb.write(stream);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void exportWebsiteTypeVisitData(Integer websiteId, List<Map<String, Object>> list, HttpServletResponse response) {
        Workbook wb = new XSSFWorkbook();
        Sheet sheet = wb.createSheet();
        sheet.setColumnWidth(0, 15 * 256);
        sheet.setColumnWidth(1, 10 * 256);
        sheet.setColumnWidth(2, 15 * 256);
        sheet.setColumnWidth(3, 15 * 256);
        sheet.setColumnWidth(4, 15 * 256);
        sheet.setColumnWidth(5, 15 * 256);
        Row row1 = sheet.createRow(0);
        int columnIndex = 0;
        row1.createCell(columnIndex++).setCellValue("分类名称");
        row1.createCell(columnIndex++).setCellValue("文章数");
        row1.createCell(columnIndex++).setCellValue("独立访问次数");
        row1.createCell(columnIndex++).setCellValue("总访问量");
        row1.createCell(columnIndex++).setCellValue("文章独立访问");
        row1.createCell(columnIndex++).setCellValue("文章总浏览量");

        for (int i = 0; i < list.size(); i++) {
            Map<String, Object> map = list.get(i);
            Row row = sheet.createRow(i + 1);
            columnIndex = 0;
            row.createCell(columnIndex++).setCellValue(getDefaultString(map.get("typeName")));
            row.createCell(columnIndex++).setCellValue(getDefaultString(map.get("articleCount")));
            row.createCell(columnIndex++).setCellValue(getDefaultString(map.get("uv")));
            row.createCell(columnIndex++).setCellValue(getDefaultString(map.get("pv")));
            row.createCell(columnIndex++).setCellValue(getDefaultString(map.get("articleUv")));
            row.createCell(columnIndex++).setCellValue(getDefaultString(map.get("articlePv")));
        }
        try (OutputStream stream = response.getOutputStream()) {
            // 返回xlsx文件类型 设置为二进制流,
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment;fileName=" + URLEncoder.encode("网站" + websiteId + "分类访问统计.xlsx", "UTF-8"));
            // 写入数据
            wb.write(stream);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * proxy url 统计代理导出
     * @param websiteId
     * @param list
     * @param response
     */
    public void proxyUrlCountDataExport(Integer websiteId, List<ProxyUrlCount> list, HttpServletResponse response) {
        Workbook wb = new XSSFWorkbook();
        Sheet sheet = wb.createSheet();
        sheet.setColumnWidth(0, 15 * 256);
        sheet.setColumnWidth(1, 10 * 256);
        sheet.setColumnWidth(2, 15 * 256);
        sheet.setColumnWidth(3, 15 * 256);
        sheet.setColumnWidth(4, 15 * 256);
        sheet.setColumnWidth(5, 15 * 256);
        Row row1 = sheet.createRow(0);
        int columnIndex = 0;
        row1.createCell(columnIndex++).setCellValue("入口网站地址");
        row1.createCell(columnIndex++).setCellValue("PV值");
        row1.createCell(columnIndex++).setCellValue("UV值");
        for (int i = 0; i < list.size(); i++) {
            ProxyUrlCount proxyUrlCount = list.get(i);
            Row row = sheet.createRow(i + 1);
            columnIndex = 0;
            row.createCell(columnIndex++).setCellValue(getDefaultString(proxyUrlCount.getSourceUrl()));
            row.createCell(columnIndex++).setCellValue(getDefaultString(proxyUrlCount.getPv()));
            row.createCell(columnIndex++).setCellValue(getDefaultString(proxyUrlCount.getUv()));
        }
        try (OutputStream stream = response.getOutputStream()) {
            // 返回xlsx文件类型 设置为二进制流,
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment;fileName=" + URLEncoder.encode("网站" + websiteId + "访问统计.xlsx", "UTF-8"));
            // 写入数据
            wb.write(stream);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public Map<String, Object> getTypePvAndUvData(Integer websiteId, Integer type, String startTime, String endTime,
                                                     Integer instanceId, Integer page, Integer pageSize) {
        Map<String, Long> timeRangeMap = getTimeRangeMap(type, startTime, endTime);
        Website website = websiteService.get(websiteId);
//        Set<String> appIds = dataCountService.getAppsByWebsiteId(websiteId);
//        JSONObject headerData = instanceService.getHeaderData(website.getHomePageId(), website.getHomePageId(), websiteId, website.getWfwfid());
//        appIds.add(headerData.getString("appId"));

        Map<String, Object> typeVisitMap = esDataService.getTypePvList(website, null, instanceId, timeRangeMap.get("startTime"), timeRangeMap.get("endTime"), page, pageSize);
        List<Map<String,Object>> list = (List<Map<String, Object>>) typeVisitMap.get("list");
        Set<Integer> typeIds = esDataService.getTypeIds(list);
        // TODO 屏蔽UV 太慢
//        esDataService.fillTypeUv(list, website, timeRangeMap.get("startTime"), timeRangeMap.get("endTime"));
        JSONObject typeIdNameMap = apiService.getTypeIdNameMap(typeIds);
        JSONObject articleCountFilterTimeMap = apiService.typesArticleCount(typeIds, instanceId, timeRangeMap.get("startTime"), timeRangeMap.get("endTime"));
        esDataService.fillTypeName(list, typeIdNameMap);
        JSONObject typeIdArticleCountMap = apiService.getTypeIdArticleCountMap(typeIds);
        esDataService.fillTypeArticleCount(list, typeIdArticleCountMap,articleCountFilterTimeMap);
        esDataService.fillTypeArticleUv(list, timeRangeMap.get("startTime"), timeRangeMap.get("endTime"));
        esDataService.fillTypeArticlePv(list, timeRangeMap.get("startTime"), timeRangeMap.get("endTime"));
        return typeVisitMap;
    }

    /**
     * 站群后台 pv等统计输出导出
     * @param idsStr websiteIds
     */
    public void statisticsDataExport(Integer productType, String idsStr, Integer wfwfid, Integer uid, String searchKey,HttpServletResponse response) {
        List<Website> manageWebsiteList = stationsService.getManageWebsiteList(productType, wfwfid, uid, 1, 0, null, null, searchKey, null);
        List<StationsPv> stationsPvs = new ArrayList<>();
        List<StationsPv> finalStationsPvs = stationsPvs;
        //导出的ID集合
        List<Integer> ids = new ArrayList<>();
        for (String id : idsStr.split(",")) {
            ids.add(Integer.parseInt(id));
        }
        manageWebsiteList.parallelStream().filter(website -> ids.contains(website.getId())).forEach(website -> finalStationsPvs.add(stationsService.getWebsitePv(website)));
        //排序
        stationsPvs = stationsPvs.stream().sorted(Comparator.comparing(StationsPv::getWebsiteId).reversed()).collect(Collectors.toList());

        Workbook wb = new XSSFWorkbook();
        Sheet sheet = wb.createSheet();
        sheet.setColumnWidth(0, 20 * 256);
        sheet.setColumnWidth(1, 20 * 256);
        sheet.setColumnWidth(2, 15 * 256);
        sheet.setColumnWidth(3, 15 * 256);
        sheet.setColumnWidth(4, 15 * 256);
        sheet.setColumnWidth(5, 15 * 256);
        sheet.setColumnWidth(6, 15 * 256);
        sheet.setColumnWidth(7, 15 * 256);
        sheet.setColumnWidth(8, 15 * 256);
        Row row1 = sheet.createRow(0);
        int columnIndex = 0;
        row1.createCell(columnIndex++).setCellValue("日期");
        row1.createCell(columnIndex++).setCellValue("站点名称");
        row1.createCell(columnIndex++).setCellValue("今日访问量");
        row1.createCell(columnIndex++).setCellValue("累计访问量");
        row1.createCell(columnIndex++).setCellValue("今日用户访问量");
        row1.createCell(columnIndex++).setCellValue("累计用户访问量");
        row1.createCell(columnIndex++).setCellValue("模块数量");
        row1.createCell(columnIndex++).setCellValue("文章数量");
        row1.createCell(columnIndex++).setCellValue("今日新增文章数量");

        for (int i = 0; i < stationsPvs.size(); i++) {
            int multiple = webVisitMultiService.getMultiple(stationsPvs.get(i).getWebsiteId());

            StationsPv stationsPv = stationsPvs.get(i);
            Row row = sheet.createRow(i + 1);
            columnIndex = 0;
            row.createCell(columnIndex++).setCellValue(LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            row.createCell(columnIndex++).setCellValue(stationsPv.getName());
            row.createCell(columnIndex++).setCellValue(WebVisitMulti.multipleNum(stationsPv.getDayVisit(),multiple).doubleValue());
            row.createCell(columnIndex++).setCellValue(WebVisitMulti.multipleNum(stationsPv.getTotalVisit(),multiple).doubleValue());
            row.createCell(columnIndex++).setCellValue(WebVisitMulti.multipleNum(stationsPv.getDayUVisit(),multiple).doubleValue());
            row.createCell(columnIndex++).setCellValue(WebVisitMulti.multipleNum(stationsPv.getTotalUVisit(),multiple).doubleValue());
            row.createCell(columnIndex++).setCellValue(stationsPv.getAppsCount());
            row.createCell(columnIndex++).setCellValue(stationsPv.getDataCount());
            row.createCell(columnIndex++).setCellValue(stationsPv.getDayDataCount());
        }
        try (OutputStream stream = response.getOutputStream()) {
            // 返回xlsx文件类型 设置为二进制流,
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment;fileName=" + URLEncoder.encode("站群访问量统计.xlsx", "UTF-8"));
            // 写入数据
            wb.write(stream);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    /**
     * 站群后台 站群来访来源排名统计
     * @param type      列表数据类型  origin browser os city
     */
    public void visitRankExport(Integer websiteId, Integer wfwfid, Integer days, String date, String type, HttpServletResponse response) {
        Map<String, Object> map = esDataService.listStationsVisitRankGroupByWebId(websiteId, wfwfid, days, date, null, null, type);
        List<StationPvOriginVo> vos = (List<StationPvOriginVo>) map.get("list");
        Workbook wb = new XSSFWorkbook();
        Sheet sheet = wb.createSheet();
        sheet.setColumnWidth(0, 20 * 256);
        sheet.setColumnWidth(1, 20 * 256);
        sheet.setColumnWidth(2, 20 * 256);
        Row row1 = sheet.createRow(0);
        int columnIndex = 0;
        String title;
        int multiple = webVisitMultiService.getMultiple(websiteId);

        switch (type){
            case "origin":
                row1.createCell(columnIndex++).setCellValue("来源");
                row1.createCell(columnIndex++).setCellValue("访问次数");
                for (int i = 0; i < vos.size(); i++) {
                    StationPvOriginVo vo = vos.get(i);
                    Row row = sheet.createRow(i + 1);
                    columnIndex = 0;
                    row.createCell(columnIndex++).setCellValue(vo.getOrigin());
                    row.createCell(columnIndex++).setCellValue(WebVisitMulti.multipleNum(vo.getCount(),multiple).doubleValue());
                }
                title = "站群来访来源排名统计.xlsx";
                break;
            case "browser":
                row1.createCell(columnIndex++).setCellValue("浏览器名称");
                row1.createCell(columnIndex++).setCellValue("访问次数");
                for (int i = 0; i < vos.size(); i++) {
                    StationPvOriginVo vo = vos.get(i);
                    Row row = sheet.createRow(i + 1);
                    columnIndex = 0;
                    row.createCell(columnIndex++).setCellValue(vo.getOrigin());
                    row.createCell(columnIndex++).setCellValue(WebVisitMulti.multipleNum(vo.getCount(),multiple).doubleValue());
                }
                title = "站群来访浏览器排名统计.xlsx";
                break;
            case "os":

                row1.createCell(columnIndex++).setCellValue("操作系统名称");
                row1.createCell(columnIndex++).setCellValue("访问次数");
                row1.createCell(columnIndex++).setCellValue("比列");
                for (int i = 0; i < vos.size(); i++) {
                    StationPvOriginVo vo = vos.get(i);
                    Row row = sheet.createRow(i + 1);
                    columnIndex = 0;
                    row.createCell(columnIndex++).setCellValue(vo.getOrigin());
                    row.createCell(columnIndex++).setCellValue(WebVisitMulti.multipleNum(vo.getCount(),multiple).doubleValue());
                    row.createCell(columnIndex++).setCellValue(vo.getPercent());
                }
                title = "站群来访操作系统排名统计.xlsx";
                break;
            default:
                return;
        }


        try (OutputStream stream = response.getOutputStream()) {
            // 返回xlsx文件类型 设置为二进制流,
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment;fileName=" + URLEncoder.encode(title, "UTF-8"));
            // 写入数据
            wb.write(stream);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    /**
     * 站群后台 站群访问设备统计导出
     */
    public void accessDeviceExport(Integer websiteId, Integer type, String startTime, String endTime, HttpServletResponse response) {
        Map<String, Long> timeRangeMap = dataCountService.getTimeRangeMap(type, startTime, endTime);

        Workbook wb = new XSSFWorkbook();
        Sheet sheet = wb.createSheet();
        sheet.setColumnWidth(0, 20 * 256);
        sheet.setColumnWidth(1, 20 * 256);
        Row row1 = sheet.createRow(0);
        int columnIndex = 0;
        row1.createCell(columnIndex++).setCellValue("mobilePv");
        row1.createCell(columnIndex++).setCellValue("pcPv");

        Row row = sheet.createRow(1);
        columnIndex = 0;
        Long mobilePv = esDataService.getDevicePv(websiteId, timeRangeMap.get("startTime"), timeRangeMap.get("endTime"), EsConstants.DEVICE_TYPE_MOBILE);
        Long pcPv = esDataService.getDevicePv(websiteId, timeRangeMap.get("startTime"), timeRangeMap.get("endTime"), EsConstants.DEVICE_TYPE_PC);

        int multiple = webVisitMultiService.getMultiple(websiteId);
        row.createCell(columnIndex++).setCellValue(WebVisitMulti.multipleNum(mobilePv, multiple).doubleValue());
        row.createCell(columnIndex++).setCellValue(WebVisitMulti.multipleNum(pcPv, multiple).doubleValue());

        String title = "访问设备统计.xlsx";
        try (OutputStream stream = response.getOutputStream()) {
            // 返回xlsx文件类型 设置为二进制流,
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment;fileName=" + URLEncoder.encode(title, "UTF-8"));
            // 写入数据
            wb.write(stream);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    /**
     * 站群后台 站群模块统计导出
     */
    public void moduleStatisticsExport(Integer websiteId,Integer pageId, HttpServletResponse response, String startTime, String endTime) {
        Map<String, Object> map = dataCountService.engineVisitTable(websiteId, null, null, pageId, startTime, endTime);

        Workbook wb = new XSSFWorkbook();
        Sheet sheet = wb.createSheet();
        sheet.setColumnWidth(0, 20 * 256);
        sheet.setColumnWidth(1, 20 * 256);
        sheet.setColumnWidth(2, 20 * 256);
        sheet.setColumnWidth(3, 20 * 256);
        Row row1 = sheet.createRow(0);
        int columnIndex = 0;
        row1.createCell(columnIndex++).setCellValue("模块名称");
        row1.createCell(columnIndex++).setCellValue("文章数");
        row1.createCell(columnIndex++).setCellValue("独立访客访问数");
        row1.createCell(columnIndex++).setCellValue("总访问量");
        ArrayList<Map<String, Object>> list = (ArrayList)map.getOrDefault("list", new ArrayList<>());

        int multiple = webVisitMultiService.getMultiple(websiteId);
        for (int i = 0; i < list.size(); i++) {
            Map<String, Object> dataMap = list.get(i);
            Row row = sheet.createRow(i + 1);
            columnIndex = 0;
            row.createCell(columnIndex++).setCellValue(dataMap.get("engineName")+"");
            row.createCell(columnIndex++).setCellValue(dataMap.getOrDefault("articleCount","0")+"");
            row.createCell(columnIndex++).setCellValue(WebVisitMulti.multipleNum(dataMap.getOrDefault("uv","0"), multiple).toString());
            row.createCell(columnIndex++).setCellValue(WebVisitMulti.multipleNum(dataMap.getOrDefault("pv","0"), multiple).toString());
        }
        String title = "站群模块统计.xlsx";
        try (OutputStream stream = response.getOutputStream()) {
            // 返回xlsx文件类型 设置为二进制流,
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment;fileName=" + URLEncoder.encode(title, "UTF-8"));
            // 写入数据
            wb.write(stream);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 站群后台 站群搜索统计导出
     */
    public void searchStatisticsExport(Integer websiteId, HttpServletResponse response, String startTime, String endTime) {
        List<Map<String, String>> maps = dataCountService.searchVisitData(websiteId, startTime, endTime);

        Workbook wb = new XSSFWorkbook();
        Sheet sheet = wb.createSheet();
        sheet.setColumnWidth(0, 20 * 256);
        sheet.setColumnWidth(1, 20 * 256);
        sheet.setColumnWidth(2, 20 * 256);
        sheet.setColumnWidth(3, 20 * 256);
        Row row1 = sheet.createRow(0);
        int columnIndex = 0;
        row1.createCell(columnIndex++).setCellValue("搜索名称");
        row1.createCell(columnIndex++).setCellValue("所属页面");
        row1.createCell(columnIndex++).setCellValue("独立检索次数");
        row1.createCell(columnIndex++).setCellValue("检索总次数");

        int multiple = webVisitMultiService.getMultiple(websiteId);
        for (int i = 0; i < maps.size(); i++) {
            Map<String, String> dataMap = maps.get(i);
            Row row = sheet.createRow(i + 1);
            columnIndex = 0;
            row.createCell(columnIndex++).setCellValue(dataMap.get("searchName")+"");
            row.createCell(columnIndex++).setCellValue(dataMap.getOrDefault("pageName","")+"");
            row.createCell(columnIndex++).setCellValue(WebVisitMulti.multipleNum(dataMap.getOrDefault("uv","0"), multiple).toString());
            row.createCell(columnIndex++).setCellValue(WebVisitMulti.multipleNum(dataMap.getOrDefault("count","0"), multiple).toString());
        }
        String title = "站群搜索统计.xlsx";
        try (OutputStream stream = response.getOutputStream()) {
            // 返回xlsx文件类型 设置为二进制流,
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment;fileName=" + URLEncoder.encode(title, "UTF-8"));
            // 写入数据
            wb.write(stream);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 站群模块统计 es 数据 及对应赋值
     * @param websiteId
     * @param page
     * @param pageSize
     * @param pageId
     * @return
     */

    public Map<String, Object> engineVisitTable(Integer websiteId, Integer page, Integer pageSize, Integer pageId, String startTime, String endTime) {
        Website website = websiteService.get(websiteId);
        JSONObject headerData = instanceService.getHeaderData(website.getHomePageId(), website.getHomePageId(), websiteId, website.getWfwfid());

        Set<String> appIds = dataCountService.getAppsByWebsiteId(websiteId);
        appIds.add(headerData.getString("appId"));

        // 查查appId对应的引擎
        JSONArray instanceList = apiService.getInstanceByAppIds(website.getWfwfid(), new ArrayList<String>(appIds));
        List<String> instanceIds = new ArrayList<>();
        if (instanceList != null && instanceList.size() > 0) {
            for (int i = 0; i < instanceList.size(); i++) {
                instanceIds.add(instanceList.getJSONObject(i).getString("instanceId"));
            }
        }

        Long startTimeStamp = DateUtils.getDateStampByFormatter(startTime, DateUtils.FORMATSTR);
        Long endTimeStamp = DateUtils.getDateStampByFormatter(endTime, DateUtils.FORMATSTR);
        List<Map<String, Object>> enginePvList = esDataService.getEnginePvList(null, website, pageId, startTimeStamp, endTimeStamp);

        // 过滤一下引擎，只展示有效的应用
        enginePvList = enginePvList.stream().filter(e->instanceIds.contains(MapUtils.getString(e,"instanceId"))).collect(Collectors.toList());

        int total = enginePvList.size();
        if (Objects.nonNull(page) && Objects.nonNull(pageSize)){
            Integer totalPage = total%pageSize == 0 ? total/pageSize : total/pageSize+1;
            Integer curMax = (page+1)*pageSize > total ? total : (page+1)*pageSize;
            Integer curMin = page*pageSize > total ? total : page*pageSize;
            enginePvList = enginePvList.subList(curMin,curMax);
        }

        Set<Integer> engineIds = esDataService.getEngineIds(enginePvList);
        JSONObject engineIdNameMap = apiService.getEngineIdNameMap(engineIds, websiteId, website.getWfwfid());
        esDataService.fillEngineName(enginePvList, engineIdNameMap);
        esDataService.fillHeaderName(enginePvList, headerData.getString("instanceId"));
        esDataService.fillEngineUv(enginePvList, website, startTimeStamp, endTimeStamp);
        JSONObject idArticleCountMap = apiService.getEngineIdArticleCountMap(engineIds);
        esDataService.fillEngineArticleCount(enginePvList, idArticleCountMap);
        Map<String, Object> result = new HashMap<>();
        result.put("list", enginePvList);
        result.put("total", total);
        return result;
    }

    /**
     * 搜索访问统计 es 数据
     * @param websiteId
     * @return
     */
    public List<Map<String, String>> searchVisitData(Integer websiteId, String startTime, String endTime) {
        Website website = websiteService.get(websiteId);
        Page homePage = pageService.get(website.getHomePageId());
        Set<String> appIds = new HashSet<>(pageService.getPageAppIds(homePage.getWebJsonId(), Template.isV2Tempalte(homePage.getTemplate())));
        appIds.remove("1");
        appIds.remove("2");
        appIds.remove("3");
        Long startTimeStamp = DateUtils.getDateStampByFormatter(startTime, DateUtils.FORMATSTR);
        Long endTimeStamp = DateUtils.getDateStampByFormatter(endTime, DateUtils.FORMATSTR);
        List<Map<String, String>> searchList = esDataService.searchScanList(appIds, website, startTimeStamp, endTimeStamp);
        dataCountService.fillSearchName(searchList);
        esDataService.fillSearchUv(searchList, website, startTimeStamp, endTimeStamp);
        esDataService.fillSearchPageName(website, searchList);
        //屏蔽没有搜索名称和页面名称的记录
        List<Map<String,String>> result = new ArrayList<>();
        for (Map<String, String> item : searchList) {
            String searchName = item.get("searchName");
            String pageName = item.get("pageName");
            if (StringUtils.isBlank(searchName)&&StringUtils.isBlank(pageName)) {
                continue;
            }
            result.add(item);
        }
        return result;
    }
    /**
     * 用户排名统计导出
     */
    public void uvStatisticsExport(Integer websiteId,Integer instanceId, Integer typeId, HttpServletResponse response, HttpServletRequest request, String startTime, String endTime, Integer type, Integer pageSize) {
        Map<String, Long> timeRangeMap = dataCountService.getTimeRangeMap(type, startTime, endTime);
        List<Map<String, Object>> maps;
        try {
            maps = esDataService.getWebsiteUvGroupByUid(websiteId, instanceId, typeId, timeRangeMap.get("startTime"), timeRangeMap.get("endTime"), Constants.PV_GROUP_DAY, pageSize);
            userService.fieldUsersInfo(maps);
        } catch (Exception e) {
            throw new BusinessException(ErrorCode.CODE_INNER_URI_ERROR.getCode());
        }

        Workbook wb = new XSSFWorkbook();
        Sheet sheet = wb.createSheet();
        sheet.setColumnWidth(0, 20 * 256);
        sheet.setColumnWidth(1, 20 * 256);
        sheet.setColumnWidth(2, 20 * 256);
        sheet.setColumnWidth(3, 20 * 256);
        Row row1 = sheet.createRow(0);
        int columnIndex = 0;
        row1.createCell(columnIndex++).setCellValue("序号");
        row1.createCell(columnIndex++).setCellValue("用户名");
        row1.createCell(columnIndex++).setCellValue("UID");
        row1.createCell(columnIndex++).setCellValue("浏览量");

        int multiple = webVisitMultiService.getMultiple(websiteId);
        for (int i = 0; i < maps.size(); i++) {
            Map<String, Object> dataMap = maps.get(i);
            Row row = sheet.createRow(i + 1);
            columnIndex = 0;
            row.createCell(columnIndex++).setCellValue(i+1);
            row.createCell(columnIndex++).setCellValue(dataMap.getOrDefault("name","")+"");
            row.createCell(columnIndex++).setCellValue(WebVisitMulti.multipleNum(dataMap.getOrDefault("uid","0"), multiple).toString());
            row.createCell(columnIndex++).setCellValue(WebVisitMulti.multipleNum(dataMap.getOrDefault("vcount","0"), multiple).toString());
        }
        String title = "个人访问量排行 TOP20.xlsx";
        try (OutputStream stream = response.getOutputStream()) {
            // 返回xlsx文件类型 设置为二进制流,
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment;fileName=" + URLEncoder.encode(title, "UTF-8"));
            // 写入数据
            wb.write(stream);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 获取模块总文章数
     *
     * @param websiteId
     * @param instanceId
     * @param typeId
     * @param day
     * @return
     */
    public int getModuleTotalArticle(Integer websiteId, Integer instanceId, Integer typeId, Integer day) {
        String url = domainInner + "/engine2/internal/api/data/engine/general/total";
        MultiValueMap<String, Integer> parameterMap = new LinkedMultiValueMap<>();
        parameterMap.add("day", day);
        parameterMap.add("instanceId", instanceId);
        parameterMap.add("typeId", typeId);
        parameterMap.add("websiteId", websiteId);
        JSONObject jsonObject = restTemplate.postForObject(url, parameterMap, JSONObject.class);
        return MapUtils.getInteger(jsonObject, "data");
    }


    /**
     * 代理统计
     * @param websiteId
     * @param type
     * @param startTime
     * @param endTime
     * @param page
     * @param pageSize
     * @return
     */
    public Map<String, Object> getProxyUrlCountPvAndUvData(Integer websiteId, Integer type, String startTime, String endTime, Integer page, Integer pageSize) {
        Map<String, Long> timeRangeMap = getTimeRangeMap(type, startTime, endTime);
        List<ProxyUrlCount> configList = proxyUrlCountService.getByWebsiteId(websiteId);
        for (ProxyUrlCount config : configList) {
            Long uv = esDataService.getProxyUrlCountUv(config.getId(), timeRangeMap.get("startTime"), timeRangeMap.get("endTime"));
            config.setUv(uv.intValue());
            Long pv = esDataService.getProxyUrlCountPv(config.getId(), timeRangeMap.get("startTime"), timeRangeMap.get("endTime"));
            config.setPv(pv.intValue());
        }
        List<ProxyUrlCount> sortList = configList.stream().sorted(Comparator.comparing(ProxyUrlCount::getPv).reversed()).collect(Collectors.toList());
        Map<String, Object> result = new HashMap<>();
        if (Objects.nonNull(page) && Objects.nonNull(pageSize)) {
            int fromIndex = page * pageSize;
            int endIndex = Math.min(page * pageSize + pageSize, sortList.size());
            result.put("list", sortList.subList(fromIndex,endIndex));
        } else {
            result.put("list", sortList);
        }
        result.put("total", configList.size());
        return result;
    }

    public List<Map<String, Object>> moduleVisitData(Integer websiteId, String startTime, String endTime) {
        Website website = websiteService.get(websiteId);
        JSONObject headerData = instanceService.getHeaderData(website.getHomePageId(), website.getHomePageId(), websiteId, website.getWfwfid());

        Set<String> appIds = dataCountService.getAppsByWebsiteId(websiteId);
        appIds.add(headerData.getString("appId"));

        // 查查appId对应的引擎
        JSONArray instanceList = apiService.getInstanceByAppIds(website.getWfwfid(), new ArrayList<>(appIds));
        List<String> instanceIds = new ArrayList<>();
        if (instanceList != null && instanceList.size() > 0) {
            for (int i = 0; i < instanceList.size(); i++) {
                instanceIds.add(instanceList.getJSONObject(i).getString("instanceId"));
            }
        }

        Long startTimeStamp = DateUtils.getDateStampByFormatter(startTime, DateUtils.FORMATSTR);
        Long endTimeStamp = DateUtils.getDateStampByFormatter(endTime, DateUtils.FORMATSTR);
        List<Map<String, Object>> enginePvList = esDataService.getEnginePvList(null, website, null, startTimeStamp, endTimeStamp);

        // 过滤一下引擎，只展示有效的应用
        enginePvList = enginePvList.stream().filter(e->instanceIds.contains(MapUtils.getString(e,"instanceId"))).collect(Collectors.toList());

        Set<Integer> engineIds = esDataService.getEngineIds(enginePvList);
        JSONObject engineIdNameMap = apiService.getEngineIdNameMap(engineIds, websiteId, website.getWfwfid());
        esDataService.fillEngineName(enginePvList, engineIdNameMap);
        esDataService.fillHeaderName(enginePvList, headerData.getString("instanceId"));
        esDataService.fillEngineUv(enginePvList, website, startTimeStamp, endTimeStamp);
        esDataService.fillDevicePv(enginePvList, website.getId(), startTimeStamp, endTimeStamp);
        return enginePvList;
    }


}
