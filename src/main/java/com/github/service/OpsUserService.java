/*
 */
package com.github.service;

import com.github.mapper.OpsUserMapper;
import com.github.model.OpsUser;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;


@Service
public class OpsUserService {

    @Resource
    private OpsUserMapper opsUserMapper;

    private final Logger logger = LoggerFactory.getLogger(OpsUserService.class);

    /**
     * 添加
     */
    @CachePut(value = "objectCache", key = "(#root.targetClass.getSimpleName()).concat(':id:').concat(#opsUser.id)")
    public OpsUser add(OpsUser opsUser) {
        this.opsUserMapper.add(opsUser);
        return opsUser;
    }

    /**
     * 删除
     */
    @CacheEvict(value = "objectCache", key = "(#root.targetClass.getSimpleName()).concat(':id:').concat(#id)")
    public void delete(Integer id) {
        this.opsUserMapper.delete(id);
    }

    /**
     * 修改
     */
    @CachePut(value = "objectCache", key = "(#root.targetClass.getSimpleName()).concat(':id:').concat(#opsUser.id)")
    public OpsUser update(OpsUser opsUser) {
        this.opsUserMapper.update(opsUser);
        return opsUser;
    }

    /**
     * 查看 - 从Cache中获取对象
     */
    @Cacheable(value = "objectCache", key = "(#root.targetClass.getSimpleName()).concat(':id:').concat(#id)", unless = "#result eq null")
    public OpsUser get(Integer id) {
        return this.opsUserMapper.get(id);
    }

    public OpsUser getByUid(Integer uid) {
        return this.opsUserMapper.getByUid(uid);
    }

}

