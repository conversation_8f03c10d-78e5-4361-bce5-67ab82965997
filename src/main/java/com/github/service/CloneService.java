/*
 * .............................................
 *
 * 				    _ooOoo_
 * 		  	       o8888888o
 * 	  	  	       88" . "88
 *                 (| -_- |)
 *                  O\ = /O
 *              ____/`---*\____
 *               . * \\| |// `.
 *             / \\||| : |||// \
 *           / _||||| -:- |||||- \
 *             | | \\\ - /// | |
 *            | \_| **\---/** | |
 *           \  .-\__ `-` ___/-. /
 *            ___`. .* /--.--\ `. . __
 *        ."" *< `.___\_<|>_/___.* >*"".
 *      | | : `- \`.;`\ _ /`;.`/ - ` : | |
 *         \ \ `-. \_ __\ /__ _/ .-` / /
 *======`-.____`-.___\_____/___.-`____.-*======
 *
 * .............................................
 *              佛祖保佑 永无BUG
 *
 * 佛曰:
 * 写字楼里写字间，写字间里程序员；
 * 程序人员写程序，又拿程序换酒钱。
 * 酒醒只在网上坐，酒醉还来网下眠；
 * 酒醉酒醒日复日，网上网下年复年。
 * 但愿老死电脑间，不愿鞠躬老板前；
 * 奔驰宝马贵者趣，公交自行程序员。
 * 别人笑我忒疯癫，我笑自己命太贱；
 * 不见满街漂亮妹，哪个归得程序员？
 *
 * 北纬30.√  <EMAIL>
 */
package com.github.service;

import com.alibaba.fastjson.JSONObject;
import com.github.util.Constants;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;


@Service
public class CloneService {

    @Resource
    private RestTemplate restTemplate;

    @Value("${engine-inner.domain-name}")
    private String domainInner;

    public JSONObject clone(String url, Integer sourcePageId, Integer targetPageId, Integer targetWfwfid, Integer targetWebsiteId) {
        MultiValueMap<String, String> parameterMap = new LinkedMultiValueMap<>();
        parameterMap.add("sourcePageId", sourcePageId.toString());
        parameterMap.add("targetPageId", targetPageId.toString());
        parameterMap.add("targetWfwfid", targetWfwfid.toString());
        parameterMap.add("targetWebsiteId", targetWebsiteId.toString());
        return restTemplate.postForObject(url, parameterMap, JSONObject.class);
    }

    public JSONObject cloneTopBottom(String url, Integer sourcePageId, Integer targetPageId, Integer targetWfwfid, Integer targetWebsiteId, Integer originPageId, Integer homePageId) {
        MultiValueMap<String, String> parameterMap = new LinkedMultiValueMap<>();
        parameterMap.add("sourcePageId", sourcePageId.toString());
        parameterMap.add("targetPageId", targetPageId.toString());
        parameterMap.add("targetWfwfid", targetWfwfid.toString());
        parameterMap.add("targetWebsiteId", targetWebsiteId.toString());
        parameterMap.add("originPageId", originPageId == null? "" : originPageId.toString());
        parameterMap.add("homePageId", homePageId == null? "" : homePageId.toString());
        return restTemplate.postForObject(url, parameterMap, JSONObject.class);
    }

    /**
     * 复制顶底应用实例
     */
    public void cloneTopBottom(Integer sourcePageId, Integer targetPageId, Integer targetWfwfid, Integer targetWebsiteId, Integer originPageId, Integer homePageId) {
        JSONObject top = this.cloneTopBottom(domainInner + Constants.HEADER_ENGINE_V + "/header/clone", sourcePageId, targetPageId, targetWfwfid, targetWebsiteId, originPageId, homePageId);
        JSONObject bottom = this.cloneTopBottom(domainInner + Constants.HEADER_ENGINE_V + "/footer/clone", sourcePageId, targetPageId, targetWfwfid, targetWebsiteId, originPageId, homePageId);
    }

    /**
     * 复制登录应用实例
     */
    public void cloneLogin(Integer sourcePageId, Integer targetPageId, Integer targetWfwfid, Integer targetWebsiteId) {
        JSONObject login = this.clone(domainInner + Constants.HEADER_ENGINE_V + "/login/clone", sourcePageId, targetPageId, targetWfwfid, targetWebsiteId);
    }

    /**
     * 复制图书馆模板应用实例
     */
    public void cloneLibrary(Integer sourcePageId, Integer targetPageId, Integer targetWfwfid, Integer targetWebsiteId) {
        JSONObject login = this.clone(domainInner + Constants.HEADER_ENGINE_V + "/library/clone", sourcePageId, targetPageId, targetWfwfid, targetWebsiteId);
    }

    /**
     * 复制北二外模板应用实例
     */
    public void clonePekingLibrary(Integer sourcePageId, Integer targetPageId, Integer targetWfwfid, Integer targetWebsiteId) {
        JSONObject login = this.clone(domainInner + Constants.HEADER_ENGINE_V + "/peking/library/clone", sourcePageId, targetPageId, targetWfwfid, targetWebsiteId);
    }

    /**
     * 复制电子科大模板应用实例
     */
    @Deprecated
    public void cloneUestc(Integer sourcePageId, Integer targetPageId, Integer targetWfwfid, Integer targetWebsiteId) {
        JSONObject login = this.clone(domainInner + Constants.HEADER_ENGINE_V + "/uestc/library/clone", sourcePageId, targetPageId, targetWfwfid, targetWebsiteId);
    }
}

