/*
 */
package com.github.service;

import com.github.mapper.ProxyUrlCountMapper;
import com.github.model.ProxyUrlCount;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;


@Service
public class ProxyUrlCountService {

    @Resource private ProxyUrlCountMapper proxyUrlCountMapper;

    /** 添加 */
    @CachePut(value="objectCache", key="(#root.targetClass.getSimpleName()).concat(':id:').concat(#proxyUrlCount.id)")
    public ProxyUrlCount add(ProxyUrlCount proxyUrlCount) {
        this.proxyUrlCountMapper.add(proxyUrlCount);
        return proxyUrlCount;
    }
    /** 删除 */
    @CacheEvict(value="objectCache", key="(#root.targetClass.getSimpleName()).concat(':id:').concat(#id)")
    public void delete(Integer id) {
        this.proxyUrlCountMapper.delete(id);
    }
    /** 修改 */
    @CachePut(value="objectCache", key="(#root.targetClass.getSimpleName()).concat(':id:').concat(#proxyUrlCount.id)")
    public ProxyUrlCount update(ProxyUrlCount proxyUrlCount) {
        this.proxyUrlCountMapper.update(proxyUrlCount);
        return proxyUrlCount;
    }
    /** 查看 - 从Cache中获取对象 */
    @Cacheable(value="objectCache", key="(#root.targetClass.getSimpleName()).concat(':id:').concat(#id)", unless="#result eq null")
    public ProxyUrlCount get(Integer id) {
        return this.proxyUrlCountMapper.get(id);
    }
    /** 获取列表 */
    public List<ProxyUrlCount> getList() {
        return this.proxyUrlCountMapper.getList();
    }
    /** 获取条件列表 */
    public List<ProxyUrlCount> getProxyUrlCountList(ProxyUrlCount proxyUrlCount) {
        return this.proxyUrlCountMapper.getProxyUrlCountList(proxyUrlCount);
    }
    /* --------------------------------------------------- */
    public PageInfo<ProxyUrlCount> listByPage(Integer pageNum, Integer pageSize, Integer websiteId) {
        Page<ProxyUrlCount> page = PageHelper.startPage(pageNum, pageSize);
        this.proxyUrlCountMapper.listByPage(websiteId);
        return new PageInfo<>(page);
    }

    public ProxyUrlCount getByWebsiteIdAndKey(Integer websiteId, String sourceKey) {
        return this.proxyUrlCountMapper.getByWebsiteIdAndKey(websiteId, sourceKey);
    }

    public List<ProxyUrlCount> getByWebsiteId(Integer websiteId) {
        return this.proxyUrlCountMapper.getByWebsiteId(websiteId);
    }
}

