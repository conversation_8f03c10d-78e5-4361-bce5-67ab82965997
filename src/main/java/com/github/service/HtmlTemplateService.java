package com.github.service;

import org.apache.commons.io.FileUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.net.URL;
import java.nio.charset.Charset;

/**
 * @version v1.0
 * @author: leolin
 * @since: 23/02/2021 6:01 PM
 * @description :类描述
 */
@Service
public class HtmlTemplateService {


    @Cacheable(value="objectCache", key="(#root.targetClass.getSimpleName()).concat(':path:').concat(#path)")
    public String getHtml(String path){
        String result = "";
        URL url = HtmlTemplateService.class.getClassLoader().getResource("");
        if(null == url){
            path = System.getProperty("user.dir")+File.separatorChar+"static"+path;
        }else{
            path = url.getPath()+"static"+path;
        }
        File f = new File(path);
        if (!f.exists()) {
           return null;
        }
        try {
            result = FileUtils.readFileToString(f, Charset.forName("UTF-8"));
        } catch (IOException e) {
            e.printStackTrace();
        }
        return result;
    }
}
