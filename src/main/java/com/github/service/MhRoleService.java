/*
 */
package com.github.service;

import com.github.mapper.MhRoleMapper;
import com.github.model.MhRole;
import com.github.model.MhRoleUid;
import com.github.model.UserAppPermissionNew;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.aop.framework.AopContext;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;


@Service
public class MhRoleService {

    @Resource
    private MhRoleMapper mhRoleMapper;
    @Resource
    private MhRoleUidService mhRoleUidService;
    @Resource
    private UserAppPermissionNewService userAppPermissionNewService;

    /** 添加 */
    @CachePut(value="objectCache", key="(#root.targetClass.getSimpleName()).concat(':id:').concat(#mhRole.id)")
    public MhRole add(MhRole mhRole) {
        this.mhRoleMapper.add(mhRole);
        return mhRole;
    }
    /** 删除 */
    @CacheEvict(value="objectCache", key="(#root.targetClass.getSimpleName()).concat(':id:').concat(#id)")
    public void delete(Integer id) {
        this.mhRoleMapper.delete(id);
    }
    /** 修改 */
    @CachePut(value="objectCache", key="(#root.targetClass.getSimpleName()).concat(':id:').concat(#mhRole.id)")
    public MhRole update(MhRole mhRole) {
        this.mhRoleMapper.update(mhRole);
        return mhRole;
    }
    /** 查看 - 从Cache中获取对象 */
    @Cacheable(value="objectCache", key="(#root.targetClass.getSimpleName()).concat(':id:').concat(#id)", unless="#result eq null")
    public MhRole get(Integer id) {
        return this.mhRoleMapper.get(id);
    }
    /** 获取列表 */
    public List<MhRole> getList() {
        return this.mhRoleMapper.getList();
    }
    /** 获取条件列表 */
    public PageInfo<MhRole> getMhRoleList(Integer websiteId, Integer pageNum, Integer pageSize) {
        Page<MhRole> page = PageHelper.startPage(pageNum, pageSize);
        this.mhRoleMapper.getMhRoleList(websiteId, pageNum, pageSize);
        return new PageInfo<>(page);
    }

    /* --------------------------------------------------- */

    @Transactional(rollbackFor = Exception.class)
    public void deleteByRoleId(Integer id,Integer wfwfid, Integer websiteId) {
        this.mhRoleMapper.delete(id);
        ((MhRoleService)AopContext.currentProxy()).delete(id);
        //删除关联表数据
        userAppPermissionNewService.deleteByFidUidWid(wfwfid, id, websiteId, UserAppPermissionNew.TYPE_APP);
        mhRoleUidService.deleteByRoleId(id);
    }

    @Transactional(rollbackFor = Exception.class)
    public Integer addMhRole(Integer websiteId, String roleName, Integer wfwfid) {
        MhRole mhRole = new MhRole();
        mhRole.setWebsiteId(websiteId);
        mhRole.setRoleName(roleName);
        ((MhRoleService)AopContext.currentProxy()).add(mhRole);
        UserAppPermissionNew userAppPermissionNew = new UserAppPermissionNew();
        userAppPermissionNew.setType(1);
        userAppPermissionNew.setWfwfid(wfwfid);
        userAppPermissionNew.setWebId(websiteId);
        userAppPermissionNew.setName(roleName);
        userAppPermissionNew.setApps("0");
        userAppPermissionNew.setPageId(0);
        userAppPermissionNew.setMhRoleId(mhRole.getId());
        userAppPermissionNew.setUid(mhRole.getId());
        //初始化，定义为普通管理员
        userAppPermissionNew.setRoleFlag(3);
        userAppPermissionNewService.add(userAppPermissionNew);
        return mhRole.getId();
    }

}

