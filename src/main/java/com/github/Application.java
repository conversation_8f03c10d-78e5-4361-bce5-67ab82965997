package com.github;

import com.github.service.V2SqlExecService;
import com.github.util.Constants;
import com.github.util.Utils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.context.annotation.Lazy;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@SpringBootApplication
@EnableTransactionManagement
@EnableAspectJAutoProxy(proxyTargetClass = true, exposeProxy = true)
@EnableScheduling
@EnableCaching
public class Application {
    @Resource
    private JdbcTemplate jdbcTemplate;

    @Value("${project.ismirror:false}")
    private boolean isMirror;

    @Value("${isYzMirror:false}")
    private boolean isYzMirror;
    @Value("${spring.web.upload-location}")
    private String uploadLocation;
    @Value("${portal.wfwfid}")
    private String portalWfwfid;
    @Lazy
    @Resource
    private V2SqlExecService v2SqlExecService;

    public static void main(String[] args) {
        System.setProperty("es.set.netty.runtime.available.processors", "false");
        SpringApplication.run(Application.class, args);
    }

    @PostConstruct
    private void init() throws Exception {
        if (isMirror) {
            // 修改镜像配置
            Constants.FRONTPATHV2 = "/entry/web/page-v21";
            Constants.FRONTPATHSSR = "/entry/web/mhssr2";
            Constants.STATIC_DOMAIN = "";

            Utils.loadCxDomian(jdbcTemplate);
            //查询有没有admin用户
            String sql = "select * from `t_admin` WHERE `status`=1";
            List<Map<String, Object>> mapList = jdbcTemplate.queryForList(sql);
            if(CollectionUtils.isEmpty(mapList)){
                Utils.cipherRandom(jdbcTemplate,portalWfwfid);
            }

            // 异步处理导入2.0基础数据
            v2SqlExecService.execSql();
        }
    }
}

