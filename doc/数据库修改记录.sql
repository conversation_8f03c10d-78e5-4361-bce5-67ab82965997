-- 2020-03-17 增加模板类型
ALTER TABLE t_template ADD type_id int NULL COMMENT '分类id';

CREATE TABLE `t_template_type`  (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '类型名称',
    `status` tinyint(4) NULL DEFAULT NULL COMMENT '状态 1上线 0下线',
    `sequence` int(11) NULL DEFAULT NULL COMMENT '顺序',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '模板类型表' ROW_FORMAT = Dynamic;

INSERT INTO `t_template_type` VALUES (1, '其他', 1, 1);
INSERT INTO `t_template_type` VALUES (2, '主题', 1, 2);
INSERT INTO `t_template_type` VALUES (3, '专题库', 1, 3);
INSERT INTO `t_template_type` VALUES (4, '阅读', 1, 4);
INSERT INTO `t_template_type` VALUES (5, '图书馆', 1, 5);
INSERT INTO `t_template_type` VALUES (6, '经典', 1, 6);

-- 3-25 添加事业部标志
ALTER TABLE t_template ADD source int default 1 COMMENT '事业部ID';

-- 3-30 添加产品类型
ALTER TABLE t_website ADD product_type int default 1 COMMENT '产品类型';

-- 3-30 用户权限表，
ALTER TABLE t_user_app_permission ADD app_webs varchar(255)  COMMENT '应用管理网站权限';
ALTER TABLE t_website_ips ADD ips varchar(255)  COMMENT 'ip白名单修改';
ALTER TABLE t_website_ips ADD `check` INT(2)  COMMENT '是否校验ip白名单，做访问拦截';

-- 4-29 引擎表添加新记录
INSERT INTO `t_engine`(`id`, `name`, `icon_url`, `type`, `data_url`, `clone_url`, `more_url`, `admin_url`, `status`, `create_time`) VALUES (205, '搜索列表', '/engine/assets/images/style/index/search_list.png', 0, '/app/universal-search/%d/div', '/engine/search_list/clone', '', '/engine/search_list/admin/%d', 1, '2019-07-08 10:45:45');

-- 4.29 engine 搜索列表应用js按需加载
INSERT INTO `portal`.`t_engine_js`( `type`, `type_text`, `js_component`, `dependent`) VALUES ('universal-search', '/app/universal-search', 'jqmd5', NULL);
INSERT INTO `portal`.`t_engine_js`( `type`, `type_text`, `js_component`, `dependent`) VALUES ('universal-search', '/app/universal-search', 'jqthumb', NULL);
-- 04-29 搜索列表，
INSERT INTO `t_engine`( `name`, `icon_url`, `type`, `data_url`, `clone_url`, `more_url`, `admin_url`, `status`, `create_time`) VALUES ('搜索列表', '/engine/assets/images/style/index/icon_search.png', 0, '/app/universal-search/%d/div', '/engine/search_list/clone', '/engine/graphic/more', '/engine/search_list/admin/%d', 1, '2019-07-08 10:45:45');

-- 05-15 写入默认轮播图-课表 app数据
INSERT INTO `portal`.`t_application`(`id`, `icon_url`, `type`, `data_url`, `clone_url`, `more_url`, `admin_url`, `status`, `create_time`) VALUES (2001, '/app/assets/images/book.png', 0, '/engine/slide/21/2001/div', '/engine/slide/clone', NULL, NULL, 1, NULL);

-- 05-18 本地镜像登录表
CREATE TABLE `t_admin`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `wfwfid` int(11) NULL DEFAULT NULL COMMENT '微服务fid',
  `uid` varchar(11) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'uid',
  `account_number` varchar(12) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '账号',
  `password` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '密码',
  `login_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '最新登录时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '修改密码时间',
  `create_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

INSERT INTO `t_admin` VALUES (1, 1, '1', 'admin', 'b6af12cfe026cf54cdfc9d575cde3825', '2020-05-18 14:33:56', '2020-05-18 14:15:34', '2020-05-08 06:16:52');

-- 05-22 待办事项应用
INSERT INTO `t_application`(`id`, `icon_url`, `type`, `data_url`, `clone_url`, `more_url`, `admin_url`, `status`, `create_time`) VALUES (111, '/app/assets/images/todo.png', 0, '/engine/graphic/todo/111/div', '/engine/graphic/clone', '/engine/graphic/more', '/engine/graphic/admin/111/22', 1, '2020-05-22 17:40:44');
INSERT INTO `t_engine_js`(`type`, `type_text`, `js_component`, `dependent`) VALUES ('graphic/todo', NULL, 'schedule', NULL);
INSERT INTO `t_organization_application`(`wfwfid`, `application_id`, `name`, `status`, `create_time`) VALUES (-3, 111, '待办事项', 1, '2020-05-22 17:42:26');


-- 6-22 添加网站favicon
ALTER TABLE t_website ADD favicon varchar(255) default '' COMMENT '网站favicon';



-- 2020-06-30 海报展览定制应用
INSERT INTO `portal`.`t_application` (`id`, `icon_url`, `type`, `data_url`, `clone_url`, `more_url`, `admin_url`, `status`, `create_time`) VALUES ('113', '/app/assets/images/book.png', '0', '/app/exhibition/div', NULL, NULL, '/app/exhibition/admin', '1', '2020-05-06 10:40:22');
INSERT INTO `portal`.`t_organization_application` (`wfwfid`, `application_id`, `name`, `status`, `create_time`) VALUES ('111976', '113', '海报展览', '1', '2020-05-06 10:41:41');
INSERT INTO `portal`.`t_organization_application` (`wfwfid`, `application_id`, `name`, `status`, `create_time`) VALUES ('21248', '113', '海报展览', '1', '2020-05-06 10:41:41');

-- 2020-07-09 读者咨询、图书捐赠应用
INSERT INTO `portal`.`t_application`(`id`, `icon_url`, `type`, `data_url`, `clone_url`, `more_url`, `admin_url`, `status`, `create_time`) VALUES (114, '/app/assets/images/book.png', 0, '/engine/text/reader-consult/div', NULL, NULL, NULL, 1, '2020-04-10 21:22:19');
INSERT INTO `portal`.`t_application`(`id`, `icon_url`, `type`, `data_url`, `clone_url`, `more_url`, `admin_url`, `status`, `create_time`) VALUES (115, '/app/assets/images/book.png', 0, '/engine/text/book-donate/div', NULL, NULL, NULL, 1, '2020-04-10 21:22:19');

INSERT INTO `portal`.`t_organization_application`(`wfwfid`, `application_id`, `name`, `status`, `create_time`) VALUES (-3, 114, '读者咨询', 1, '2020-07-09 12:39:30');
INSERT INTO `portal`.`t_organization_application`(`wfwfid`, `application_id`, `name`, `status`, `create_time`) VALUES (-3, 115, '图书捐赠', 1, '2020-07-09 12:39:51');

-- 2020-07-10 滚动插件
INSERT INTO `portal`.`t_engine_js`(`type`, `type_text`, `js_component`, `dependent`) VALUES ('general', NULL, 'liMarquee', NULL);

-- 2020-07-16 海报展览名称改为主题展览
UPDATE t_organization_application SET name='主题展览' WHERE application_id=113 AND name='海报展览';


ALTER TABLE t_admin ADD name varchar(255) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '姓名';
ALTER TABLE t_admin ADD status int(2) DEFAULT '0' COMMENT '状态: 1启用 0 禁用';


-- 2020-07-30
CREATE TABLE `t_web_data_reviewer` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uid` int(11) DEFAULT NULL,
  `wfwfid` int(11) DEFAULT NULL,
  `real_name` varchar(255) DEFAULT NULL,
  `status` int(1) DEFAULT '1',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=15 DEFAULT CHARSET=utf8mb4 COMMENT='网站 数据 审核人';


-- 2020-08-20 用户管理菜单排序
CREATE TABLE `t_user_app_sort` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `app_ids` varchar(4000) DEFAULT NULL,
  `wfwfid` int(11) DEFAULT NULL,
  `uid` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COMMENT='用户管理菜单排序';

-- 2020-08-31 page表加字段template, 把webjson里setting下的type值刷到template字段
ALTER TABLE t_page ADD template VARCHAR(64) NULL COMMENT '页面类型（对应webjson里setting下type值）' AFTER content;
UPDATE t_page t1, t_web_json t2 SET t1.template = JSON_UNQUOTE(JSON_EXTRACT(t2.content, '$.setting.type')) WHERE t1.web_json_id = t2.id AND t2.content != '';


-- 2020-09-03 网站第三方管理菜单表
CREATE TABLE `t_website_menu` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `website_id` int(11) DEFAULT NULL COMMENT '网站ID',
  `name` varchar(255) DEFAULT NULL COMMENT '菜单名称',
  `url` varchar(255) DEFAULT NULL COMMENT '打开地址',
  `status` int(2) DEFAULT NULL COMMENT '状态',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='网站后台添加的第三方菜单管理';


CREATE TABLE `t_engine_modules` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `engine_id` int(11) DEFAULT NULL COMMENT 'engine表的ID',
  `modules_id` int(11) DEFAULT NULL COMMENT '模板ID，1 代表通用模板，2代表大屏端模板',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=19 DEFAULT CHARSET=utf8mb4 COMMENT='网站模板关联基础引擎表';

INSERT INTO `t_engine_modules` VALUES (1, 95, 1);
INSERT INTO `t_engine_modules` VALUES (2, 96, 1);
INSERT INTO `t_engine_modules` VALUES (3, 97, 1);
INSERT INTO `t_engine_modules` VALUES (4, 98, 1);
INSERT INTO `t_engine_modules` VALUES (5, 99, 1);
INSERT INTO `t_engine_modules` VALUES (6, 105, 1);
INSERT INTO `t_engine_modules` VALUES (7, 203, 1);
INSERT INTO `t_engine_modules` VALUES (8, 204, 1);
INSERT INTO `t_engine_modules` VALUES (9, 205, 1);
INSERT INTO `t_engine_modules` VALUES (10, 95, 2);
INSERT INTO `t_engine_modules` VALUES (11, 96, 2);
INSERT INTO `t_engine_modules` VALUES (12, 97, 2);
INSERT INTO `t_engine_modules` VALUES (13, 98, 2);
INSERT INTO `t_engine_modules` VALUES (14, 99, 2);
INSERT INTO `t_engine_modules` VALUES (15, 105, 2);
INSERT INTO `t_engine_modules` VALUES (16, 203, 2);
INSERT INTO `t_engine_modules` VALUES (17, 204, 2);
INSERT INTO `t_engine_modules` VALUES (18, 206, 2);

-- 插入engine表，大屏端的图表
INSERT INTO `portal`.`t_engine`(`id`, `name`, `icon_url`, `type`, `data_url`, `clone_url`, `more_url`, `admin_url`, `status`, `create_time`) VALUES (206, '图表', '/assets/images/chat.png', 0, '/engine2/chart/screen/%d/div', '/engine2/chart/clone', '/engine2/chart/more', '/engine2/admin/chart/screen/%d', 1, '2020-02-16 15:49:16');
-- 2020-09-07 JS按需加载
INSERT INTO `portal`.`t_engine_js`(`type`, `type_text`, `js_component`, `dependent`) VALUES ('general', NULL, 'DataNumber', NULL);
INSERT INTO `portal`.`t_engine_js`(`type`, `type_text`, `js_component`, `dependent`) VALUES ('general', NULL, 'tagcloud', NULL);

-- 2020-09-11
UPDATE `t_engine` SET `name` = '图表', `icon_url` = '/assets/images/chat.png', `type` = 0, `data_url` = '/engine2/chart/screen/%d/div', `clone_url` = '/engine2/chart/clone', `more_url` = '/engine2/chart/more', `admin_url` = '/engine2/admin/chart/screen/%d', `status` = 1, `create_time` = '2020-02-16 15:49:16' WHERE `id` = 206;

-- 2020-09-17
INSERT INTO `portal`.`t_application`(`id`, `icon_url`, `type`, `data_url`, `clone_url`, `more_url`, `admin_url`, `status`, `create_time`) VALUES (116, '/app/assets/images/todo.png', 0, '/engine2/general/engineInstance/153007/div', NULL, '/engine2/general/more', NULL, 1, '2020-04-23 17:23:35');
INSERT INTO `portal`.`t_organization_application`(`wfwfid`, `application_id`, `name`, `status`, `create_time`) VALUES (-3, 116, '时事热点', 1, '2020-09-17 17:55:48');
-- 2020-09-18 数据统计新样式
INSERT INTO `portal`.`t_application`(`icon_url`, `type`, `data_url`, `clone_url`, `more_url`, `admin_url`, `status`, `create_time`)
VALUES ('/engine/assets/images/style/index/icon_text.png', 0, '/app/data/count/3/div', NULL, '/app/data/count/more', NULL, 1, NULL);
INSERT INTO `portal`.`t_application`(`icon_url`, `type`, `data_url`, `clone_url`, `more_url`, `admin_url`, `status`, `create_time`)
VALUES ('/engine/assets/images/style/index/icon_text.png', 0, '/app/data/count/4/div', NULL, '/app/data/count/more', NULL, 1, NULL);


-- 2020-09-22 登录设置
ALTER TABLE `portal`.`t_website` ADD COLUMN `login_current_org` int(1) NOT NULL DEFAULT 1 COMMENT '默认选中当前机构' AFTER `logout_url`;



-- 2020-09-28 敦煌市图书馆登录
INSERT INTO `portal`.`t_sso_config`(`wfwfid`, `auth_type`, `handle_service_name`, `login_page`, `auth_config`)
VALUES (5618, 'cas', 'dunHuangLibService', NULL, NULL);

-- 2020-09-28 四川外国语大学
INSERT INTO `portal`.`t_sso_config`(`wfwfid`, `auth_type`, `handle_service_name`, `login_page`, `auth_config`)
 VALUES (23569, 'cas', 'siChuanInternationalService', NULL, NULL);

-- 创建网站插件表
DROP TABLE IF EXISTS `t_website_plugs`;
CREATE TABLE `t_website_plugs` (
  `id` int(11) NOT NULL,
  `website_id` int(11) DEFAULT NULL,
  `type` int(2) DEFAULT NULL COMMENT '类型，1 js， ',
  `plugs` varchar(500) DEFAULT NULL COMMENT '外挂',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 2020-10-12 添加四川外国语挂智能客服插件
INSERT INTO `portal`.`t_website_plugs`(`id`, `website_id`, `type`, `plugs`) VALUES (1, 23569, 1, 'https://api-lib.oss-cn-hangzhou.aliyuncs.com/ai/ai_scwgydx_20190929.js');

-- 2020-10-12 学术动态应用
INSERT INTO `portal`.`t_application`(`id`, `icon_url`, `type`, `data_url`, `clone_url`, `more_url`, `admin_url`, `status`, `create_time`) VALUES (117, '/app/assets/images/todo.png', 0, '/engine2/general/engineInstance/159374/div', NULL, '/engine2/general/more', NULL, 1, '2020-10-12 16:59:49');
INSERT INTO `portal`.`t_organization_application`(`wfwfid`, `application_id`, `name`, `status`, `create_time`) VALUES (-3, 117, '学术动态', 1, '2020-10-12 17:02:27');
-- 2020-10-14 添加图文样式按需加载js
INSERT INTO `portal`.`t_engine_js` VALUES (72,'general', '','TweenMax','');

-- 2020-10-15 修正t_website_plugs表四川外国语挂智能客服插件的websitId
UPDATE `portal`.`t_website_plugs` SET `website_id`= 27010 WHERE id=1;

-- 2020-10-16 南京大学智慧图书馆3.0配置资源推荐应用
INSERT INTO `portal`.`t_organization_application`(`wfwfid`, `application_id`, `name`, `status`, `create_time`) VALUES (124864, 106, '资源推荐', 1, '2020-10-16 12:36:12');


-- 2020-10-19 网站添版本号， 0代表之前版本， 1代表2020-10版本。
ALTER TABLE `portal`.`t_website` ADD COLUMN `versions` int(2) NOT NULL DEFAULT 0 COMMENT '网站添版本号';
update t_website set versions = 0;

ALTER TABLE `portal`.`t_organization_application` ADD COLUMN `website_id` int(11) NOT NULL DEFAULT 0 COMMENT '网站ID';
ALTER TABLE `portal`.`t_organization_application` ADD COLUMN `page_id` int(11) NOT NULL DEFAULT 0 COMMENT '网页ID';
update t_organization_application set website_id = 0 , page_id = 0;

ALTER TABLE `portal`.`t_application` ADD COLUMN `source` int(2) NOT NULL DEFAULT 1 COMMENT '应用来源';
update t_application set source = 1;

-- 2020-10-22 新增文本js
INSERT INTO t_engine_js (type,type_text,js_component,dependent)VALUES('text',NULL,'CountUp' ,NULL );

-- 2020-10-23 sso认证
INSERT INTO `portal`.`t_sso_config`(`wfwfid`, `auth_type`, `handle_service_name`, `login_page`, `auth_config`)
 VALUES (8789, 'custom', 'shanDongSWService', 'shandongsw_login', NULL);
INSERT INTO `portal`.`t_sso_config`(`wfwfid`, `auth_type`, `handle_service_name`, `login_page`, `auth_config`)
 VALUES (31485, 'cas', 'jingGangShanLibService', NULL, NULL);

-- 2020-10-27 修改通用图标样式js为echart4
UPDATE t_engine_js SET js_component = 'echart4' WHERE id =33;

-- 2020-10-28 新增地图js
INSERT INTO t_engine_js (type,type_text,js_component,dependent)VALUES ('chart','','chinaMap','');
-- 2020-10-28 新增世界地图js
INSERT INTO t_engine_js VALUES (77,'chart','','worldMap','');

-- 2020-11-4 添加网站是否需要登录检查
ALTER TABLE t_website ADD check_login tinyint default 0 COMMENT '是否需要登录检查';
ALTER TABLE t_website ADD async_logout_url varchar(255) default '' COMMENT '异步退出调用地址';

-- 2020-11-6 新增用户管理员表
DROP TABLE IF EXISTS `t_admin`;
CREATE TABLE `t_admin`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户名',
  `wfwfid` int(11) NULL DEFAULT NULL COMMENT '微服务fid',
  `uid` int(11) NULL DEFAULT NULL COMMENT 'uid',
  `account_number` varchar(12) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '账号',
  `password` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '密码',
  `login_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '最新登录时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '修改密码时间',
  `create_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `status` int(2) NULL DEFAULT 1 COMMENT '状态: 1启用 0 禁用',
  `role` int(255) NULL DEFAULT 0 COMMENT '是否管理员: 1 管理员 0 普通用户',
  `phone_number` varchar(12) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '手机号码',
  `unit` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '单位',
  `head_img` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '头像',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 30 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

INSERT INTO `t_admin` VALUES (1, '超级管理员', 1, 1, 'admin', 'C2kW4nnBhDw=', '2020-11-01 04:21:07', '2020-07-28 11:15:27', '2020-05-08 06:16:52', 1, 0, NULL, NULL, NULL);

-- 2020-11-09 添加未登录时才需要跳转的页面地址
ALTER TABLE t_website ADD un_login_page int(11) DEFAULT 0 COMMENT '网站未登录时才需要跳转的页面地址';


-- 北二外插件
REPLACE INTO `t_website_plugs`(`id`, `website_id`, `type`, `plugs`) VALUES (2, 11606, 1, 'http://float2006.tq.cn/floatcard?adminid=9565666&sort=35');

-- 2020-11-13
UPDATE t_organization_application SET name='智慧问答' WHERE wfwfid=-3 AND application_id=114 AND name='读者咨询';

-- 新建websiteDomain表
CREATE TABLE `t_website_domain` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `website_id` int(11) DEFAULT NULL,
  `domain` varchar(255) DEFAULT NULL,
  `status` int(2) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

insert into t_website_domain (website_id,domain,status) select id,domain_external,1 from t_website where length(domain_external) > 3;


-- 2020-11-25 北二外新增插件
INSERT INTO `portal`.`t_website_plugs`(`id`, `website_id`, `type`, `plugs`) VALUES (3, 11606, 1, 'https://www.googletagmanager.com/gtag/js?id=G-TEST99NZRD');

ALTER TABLE t_website ADD intro text COMMENT '网站简介';

-- 2020-11-17 常用应用
INSERT INTO `portal`.`t_application`(`id`, `icon_url`, `type`, `data_url`, `clone_url`, `more_url`, `admin_url`, `status`, `create_time`, `source`) VALUES (118, '/app/assets/images/book.png', 0, '/engine2/general/favorite-icon/div', '/engine2/general/clone', '/engine2/general/more', '/engine2/admin/118', 1, '2020-11-17 16:01:38', 1);
INSERT INTO `portal`.`t_organization_application`(`wfwfid`, `application_id`, `name`, `status`, `create_time`, `website_id`, `page_id`) VALUES (-3, 118, '常用应用', 1, '2020-11-17 14:53:48', 0, 0);

-- 2020-11-24 机构2120配置主题展览
INSERT INTO `portal`.`t_organization_application` (`wfwfid`, `application_id`, `name`, `status`, `create_time`) VALUES ('2120', '113', '主题展览', '1', '2020-05-06 10:41:41');

-- 2020-11-25 删除常用应用
DELETE FROM t_application WHERE id=118;
DELETE FROM t_organization_application WHERE application_id=118;


--  2020-11-27 新增新闻发布应用
INSERT INTO `portal`.`t_organization_application`(`wfwfid`, `application_id`, `name`, `status`, `create_time`, `website_id`, `page_id`) VALUES (-3, 1301, '新闻发布', 1, '2020-11-25 22:57:59', 0, 0);
INSERT INTO `portal`.`t_application`(`id`, `icon_url`, `type`, `data_url`, `clone_url`, `more_url`, `admin_url`, `status`, `create_time`, `source`) VALUES (1301, '/assets/images/news.png', 0, '/engine2/general/1301/div', '/engine2/general/clone', '/engine2/general/more', '/engine2/admin/1301', 1, '2020-11-25 16:01:38', 1);
--  2020-12-08 新增西南政法内网IP
INSERT INTO `portal`.`t_website_ips`(`website_id`, `ips`, `tips`, `check`) VALUES (28168, '************-**************#**************-***************#**************-***************#**************-**************#*************-*************#**************-**************#***********-*************#***************-*************** #', NULL, 0);

-- 2020-12-11 更新表格应用数据
UPDATE t_engine
SET data_url='/engine2/table/%d/div', clone_url='/engine2/table/clone', more_url='/engine2/table/more', admin_url='/engine2/admin/table/%d'
WHERE id = 204 AND name = '表格';

UPDATE t_application
SET data_url = replace(data_url, '/engine/table/', '/engine2/table/'),
    clone_url = '/engine2/table/clone',
    more_url = '/engine2/table/more',
    admin_url = replace(admin_url, '/engine/table/admin/', '/engine2/admin/table/')
WHERE clone_url = '/engine/table/clone';

-- 新增权限表
CREATE TABLE `t_user_app_permission_new` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `wfwfid` int(11) DEFAULT NULL,
  `uid` int(11) DEFAULT NULL,
  `name` varchar(255) DEFAULT NULL,
  `apps` text,
  `page_id` int(11) DEFAULT NULL,
  `web_id` int(11) DEFAULT NULL,
  `role_flag` int(2) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=136 DEFAULT CHARSET=utf8mb4;

-- 2020-12-15 新增webjson clock表
CREATE TABLE `t_webjson_clock` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `web_id` int(11) DEFAULT NULL,
  `json_id` int(11) DEFAULT NULL,
  `status` int(2) DEFAULT NULL COMMENT '1 clock 0, unlock',
  `uid` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COMMENT='操作webjson锁定表';

-- 创建学科分馆分类
INSERT INTO `portal`.`t_template_type`(`id`, `name`, `status`, `sequence`) VALUES (9, '学科分馆', 1, 9);

-- 资源ip过滤黑名单
CREATE TABLE `t_website_resource_ip_limit` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `website_id` int(11) DEFAULT NULL,
  `ips` varchar(255) DEFAULT NULL COMMENT '#号分隔的ip段',
  `scan_num` int(11) DEFAULT NULL,
  `scan_cur_num` int(11) DEFAULT NULL,
  `down_num` int(11) DEFAULT NULL,
  `down_cur_num` int(11) DEFAULT NULL,
  `type` int(1) DEFAULT NULL COMMENT '0 default,1指定ip限制',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COMMENT='网站的IP范围 浏览量，下载量 控制';

-- 2020-12-23 删除该按需加载js
DELETE FROM t_engine_js WHERE js_component ='TweenMax';

-- 2020-12-28 多级审核相关表
-- 引擎审核流程表
CREATE TABLE `t_audit_process` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `engine_instance_id` int(11) DEFAULT NULL COMMENT '引擎id',
    `uid` int(11) DEFAULT NULL COMMENT '审核人id',
    `uname` varchar(50) DEFAULT NULL COMMENT '审核人名称',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COMMENT='审核流程表';
-- 审核记录表
CREATE TABLE `t_audit_record` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `gid` bigint(20) DEFAULT NULL COMMENT '数据id',
    `uid` int(11) DEFAULT NULL COMMENT '审核人（或发起人）uid',
    `uname` varchar(50) DEFAULT NULL COMMENT '审核人姓名',
    `opinion` varchar(512) DEFAULT NULL COMMENT '审核意见',
    `audit_time` datetime DEFAULT NULL COMMENT '审核时间',
    `status` int(2) DEFAULT NULL COMMENT '审核状态 0发起审核 1待审核 2审核中 3审核通过 4审核不通过',
    `is_last` tinyint(1) DEFAULT NULL COMMENT '是否为最后一个审核人 0否 1是',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='审核记录表';
-- 2020-12-31 大屏图标列表掉了，加上
INSERT INTO `portal`.`t_engine_modules`(`id`, `engine_id`, `modules_id`) VALUES (20, 99, 2);


-- 2021-01-05 网站的信任域名白名单 -- 用于过滤网站转发地址
 CREATE TABLE `t_domain_blank` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `website_id` int(11) DEFAULT NULL,
  `domain` varchar(255) DEFAULT NULL,
  `status` tinyint(1) DEFAULT '1',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='网站域名过滤白名单';

-- 2021-01-06 华科列表页----
CREATE TABLE `t_website_huake`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '网站名称',
  `cover` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '网站封面图',
  `preview_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '预览链接',
  `status` tinyint(1) NULL DEFAULT 1 COMMENT '状态: 1上线 0下线',
  `create_user` int(11) NULL DEFAULT NULL COMMENT '创建人uid',
  `create_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 9 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '网站表' ROW_FORMAT = Dynamic;

INSERT INTO `t_website_huake` VALUES (1, '1+4布局左上图地址', '/assets/images/website/huake/1_4_upper_left.png', 'http://www.bai.com', 1, 1, '2021-01-06 06:05:49', '2021-01-06 06:05:49');
INSERT INTO `t_website_huake` VALUES (2, '1+4布局左下图地址', '/assets/images/website/huake/1_4_lower_left.png', 'http://www.bai.com', 1, 1, '2021-01-06 06:05:49', '2021-01-06 06:05:49');
INSERT INTO `t_website_huake` VALUES (3, '1+4布局中间图地址', '/assets/images/website/huake/1_4_center.png', 'http://www.bai.com', 1, 1, '2021-01-06 06:05:49', '2021-01-06 06:05:49');
INSERT INTO `t_website_huake` VALUES (4, '1+4布局右上图地址', '/assets/images/website/huake/1_4_upper_right.png', 'http://www.bai.com', 1, 1, '2021-01-06 06:05:49', '2021-01-06 06:05:49');
INSERT INTO `t_website_huake` VALUES (5, '1+4布局右下图地址', '/assets/images/website/huake/1_4_lower_rigth.png', 'http://www.bai.com', 1, 1, '2021-01-06 06:05:49', '2021-01-06 06:13:56');
INSERT INTO `t_website_huake` VALUES (6, '1+1布局左侧图地址', '/assets/images/website/huake/1_1_left.png', 'http://www.bai.com', 1, 1, '2021-01-06 06:05:49', '2021-01-06 06:13:58');
INSERT INTO `t_website_huake` VALUES (7, '1+1布局右侧图地址', '/assets/images/website/huake/1_1_right.png', 'http://www.bai.com', 1, 1, '2021-01-06 06:05:49', '2021-01-06 06:05:49');
INSERT INTO `t_website_huake` VALUES (8, '16:9版本轮播地址', '/assets/images/website/huake/16_9.png', 'http://www.bai.com', 1, 1, '2021-01-06 06:05:49', '2021-01-06 06:05:49');


-- 2021-01-07 系统菜单
CREATE TABLE `t_menu` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) DEFAULT NULL,
  `url` varchar(255) DEFAULT NULL,
  `role` int(2) DEFAULT NULL COMMENT '需要的权限',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 2021-01-08 新手引导
ALTER TABLE `t_website`
ADD COLUMN `beginner_guide` int(1) DEFAULT 0 COMMENT '是否开启新手引导';

-- 2021-01-08 新手引导
CREATE TABLE `t_beginner_guide_picture` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `website_id` int(11) NOT NULL,
  `picture_type` int(2) NOT NULL,
  `picture_url` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 2021-01-13 国际化
ALTER TABLE t_website ADD global_type varchar(10) DEFAULT 'zh'  COMMENT '国际化语言类型 zh | en | fr'
update t_website set global_type = 'zh';

ALTER TABLE t_website ADD main_site  int(11) DEFAULT NULL  COMMENT '主站id'

-- 2021-01-13 院长信箱
CREATE TABLE `t_dean_email` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `website_id` int(11) DEFAULT NULL COMMENT '网站id',
    `sender` varchar(50) DEFAULT NULL COMMENT '发件人',
    `sender_password` varchar(50) DEFAULT NULL COMMENT '发件人密码',
    `authorization_code` varchar(50) DEFAULT NULL COMMENT '授权码',
    `recipient` int(11) DEFAULT NULL COMMENT '收件人',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COMMENT='院长信箱配置表';

--2021-01-21超级管理员后台
CREATE TABLE `t_role` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uid` int(11) DEFAULT NULL,
  `role` int(2) DEFAULT NULL COMMENT '角色 ',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4;

INSERT INTO `t_menu`(`id`, `name`, `url`, `role`) VALUES (1, '网站列表 - 网站基本信息管理', '/sadmin/website-list', 1);
INSERT INTO `t_menu`(`id`, `name`, `url`, `role`) VALUES (2, '全站域名白名单列表', '/sadmin/blank-domain-list', 1);

INSERT INTO `t_engine_js`(`type`, `type_text`, `js_component`, `dependent`) VALUES ('graphic', NULL, 'laydate', NULL);

-- 2021-01-21 重庆邮电大学门户添加插件
INSERT INTO `t_website_plugs`( `website_id`, `type`, `plugs`) VALUES ( 25040, 1, 'http://api-lib.oss-cn-hangzhou.aliyuncs.com/ai/ai_cqyddx_20191122.js');

-- 2021-01-21 网站状态字段修改
alter table t_website modify status int(2) default 1 null comment '状态: 1上线 0下线 -1删除';

-- 网站审核
ALTER TABLE t_webjson_clock MODIFY status INT(2) NULL COMMENT '0未锁定 1普通锁定 3稍后审核锁定（自己可以修改，只有自己能解锁，且解锁会删除临时数据t_webjson_temp） 4审核中锁定（都不能解锁和修改）';
ALTER TABLE t_webjson_clock ADD page_id INT NULL;
UPDATE t_webjson_clock t1, t_page t2 SET t1.page_id = t2.id WHERE t1.json_id = t2.web_json_id;
ALTER TABLE t_page ADD audit_type INT(2) DEFAULT 0 NULL COMMENT '审核类型 0关闭审核 1不指定流程审核 2指定流程审核' AFTER template;
ALTER TABLE t_audit_record ADD type INT NULL COMMENT '记录类型 1数据审核 2页面审核';
UPDATE t_audit_record SET type = 1;
ALTER TABLE t_audit_record CHANGE gid data_id BIGINT NULL COMMENT '数据id';
ALTER TABLE t_audit_process CHANGE engine_instance_id data_id INT NULL COMMENT '数据id(引擎id或页面id)';
ALTER TABLE t_audit_process ADD type INT NULL COMMENT '流程类型 1引擎审核流程 2页面审核流程';
UPDATE t_audit_process SET type = 1;
CREATE TABLE `t_webjson_temp` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `content` mediumtext COMMENT '网站对应的json结构',
    `audit_type` int(2) DEFAULT NULL COMMENT '审核类型 0关闭审核 1不指定流程审核 2指定流程审核',
    `audit_status` int(11) DEFAULT NULL COMMENT '审核状态。0：未通过、1：已通过、2：待提交审核、3：审核中',
    `audit_uid` int(11) DEFAULT NULL COMMENT '审核人uid',
    `start_audit_uid` int(11) DEFAULT NULL COMMENT '发起审核人uid',
    `current_audit_uid` int(11) DEFAULT NULL COMMENT '当前审核人uid',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=53324 DEFAULT CHARSET=utf8mb4;

alter table t_website modify un_login_page VARCHAR (255) comment '未登录访问地址';
update t_website set un_login_page = '' where un_login_page = 0;

-- 2021-03-05 网站状态字段修改
alter table t_website ADD hide  tinyint(1) DEFAULT 0  COMMENT '门户不显示该网站';
alter table t_website ADD lou  tinyint(1) DEFAULT 0  COMMENT '网站登录地址使用原地址loginOriginUrl';

-- 2021-03-01 模板分类顺序错误，调整顺序
UPDATE `portal`.`t_template_type` SET `name` = '泛雅', `status` = 1, `sequence` = 7 WHERE `id` = 7;

-- 2021-03-01 新增大屏模板分类
UPDATE `portal`.`t_template_type` SET `name` = '大屏', `status` = 1, `sequence` = 11 WHERE `id` = 11;

-- 2021-03-09 审核记录类型增加模块基础设置审核,type值为3
alter table t_audit_record modify type int null comment '记录类型 1数据审核 2页面审核 3模块基础设置审核';
-- 2021-03-12 新增陕科大门户插件
INSERT INTO `portal`.`t_website_plugs`( `website_id`, `type`, `plugs`) VALUES ( 39169, 1, 'https://www.aixiaoduo.com/c/sdk/sdk.min.js?src=147&key=gkajdubnwtgigfljtqfmztezandgyjajvevqcnslrvbpinjidqtgheamdnmxzaxl&channel_id=904');

-- 添加网站设置，使用临时cookie
alter table t_website ADD tmp_cookie  tinyint(1) DEFAULT 0  COMMENT '是否使用临时cookie';

-- 郑大外挂修改style
INSERT INTO `portal`.`t_website_plugs`(`id`, `website_id`, `type`, `plugs`) VALUES (11, 19779, 2, '<script type="text/javascript" src="/assets/plugs/zzulibMobile.js"></script>');

-- 模板封面滚动时间
ALTER TABLE t_template ADD column scroll int default 3 COMMENT '封面图滚动时间';

-- 网站PV表加迁移ES标志字段
ALTER TABLE t_website_pv ADD transfered INT DEFAULT 0 NULL COMMENT '是否迁移到es的标志 0未迁移 1已迁移';


-- 2021-04-22 general样式加countup  js
INSERT INTO `portal`.`t_engine_js`( `type`, `type_text`, `js_component`, `dependent`) VALUES ( 'general', NULL, 'CountUp', NULL);


-- 2021-04-21  添加网站单位联盟，用户登录控制
CREATE TABLE `t_website_org_union` (
                                       `id` int(11) NOT NULL AUTO_INCREMENT,
                                       `website_id` int(11) DEFAULT NULL,
                                       `fid` int(11) DEFAULT NULL,
                                       `org_name` varchar(255) DEFAULT NULL,
                                       PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='给网站配置的联盟单位登录';
-- 2021-04-21 网站的登录有效期时间，默认1800s
ALTER TABLE t_website ADD column login_interval int default 1800 COMMENT '登录有效期';

-- 2021-04-29  镜像后台登录新增失败次数和登录失败锁定时间
ALTER TABLE t_admin ADD column failures_number int(2) DEFAULT '0' COMMENT '失败次数,最多5次';
ALTER TABLE t_admin ADD column locking_time bigint(20) DEFAULT '0' COMMENT '锁定时间默认10分钟';

-- 2021-04-30 登录后自动跳转，默认1
ALTER TABLE t_website ADD login_auto_refer tinyint(1) default 1 COMMENT '登录后是否自动跳转(0否，1是）';

-- 2021-05-07 添加模板的sequence功能
ALTER TABLE t_template ADD column sequence int(11) DEFAULT 1 NULL COMMENT '顺序';

-- 2021-06-02 五合一词云js
INSERT INTO `portal`.`t_engine_js`( `type`, `type_text`, `js_component`, `dependent`) VALUES ('general', NULL, 'svg3dtagcloud', NULL);

-- 2021-06-02 地图应用
INSERT INTO `portal`.`t_engine`(`id`, `name`, `icon_url`, `type`, `data_url`, `clone_url`, `more_url`, `admin_url`, `status`, `create_time`) VALUES (208, '地图应用', '/engine2/assets/images/style/map-app/map-app.svg', 0, '/engine2/map-app/%d/div', '/engine2/map-app/clone', NULL, '/engine2/admin/map-app/%d', 1, '2021-06-02 18:25:14');
INSERT INTO `portal`.`t_engine_modules`(`engine_id`, `modules_id`) VALUES ( 208, 1);



-- 2021-05-17 添加会签审核字段
ALTER TABLE t_audit_process ADD node INT NULL COMMENT '节点编号';
ALTER TABLE t_audit_process ADD audit_type INT NULL COMMENT '审核类型（1会签 2或签）';

ALTER TABLE t_audit_record ADD node INT NULL COMMENT '节点编号';
ALTER TABLE t_audit_record ADD audit_type INT NULL COMMENT '审核类型（1会签 2或签）';
ALTER TABLE t_audit_record MODIFY status INT(2) NULL COMMENT '审核状态 0发起审核 1待审核 2审核中 3审核通过 4审核不通过 5其他人已审核';

ALTER TABLE t_webjson_temp MODIFY current_audit_uid VARCHAR(255) NULL COMMENT '当前审核人uid，多个审核人逗号隔开';

-- 2021-06-03 地图js
INSERT INTO `portal`.`t_engine_js`( `type`, `type_text`, `js_component`, `dependent`) VALUES ( 'map-app', NULL, 'bdMapWebgl', NULL);

-- 2021-06-04 智图新版接口
INSERT INTO portal.t_application(icon_url, type, data_url, clone_url, more_url, admin_url, status, create_time)
VALUES ( '/engine/assets/images/style/index/icon_text.png', 0, '/app/data/count/6/div?pageType=2', NULL, '/app/data/count/more?pageType=2', NULL, 1, NULL);
INSERT INTO portal.t_application(icon_url, type, data_url, clone_url, more_url, admin_url, status, create_time)
VALUES ( '/engine/assets/images/style/index/icon_text.png', 0, '/app/data/count/6/div?pageType=3', NULL, '/app/data/count/more?pageType=3', NULL, 1, NULL);
-- 2021-06-04 智图新版接口（广外再加一个小语种馆藏）
INSERT INTO portal.t_organization_application(wfwfid,application_id, name, status, create_time)
select 21415,id,'数据统计（智图新版接口-小语种）',1,now() from t_application where data_url ='/app/data/count/6/div?pageType=3';

-- 2021-06-16 删除错误的按需加载js
DELETE FROM t_engine_js WHERE id = 79;
-- 2021-06-21 新日历样式按需js
INSERT INTO `portal`.`t_engine_js`( `type`, `type_text`, `js_component`, `dependent`) VALUES ( 'general', NULL, 'almanac', NULL);

-- 2021-06-25 文本分类样式，需要echart.js
INSERT INTO `portal`.`t_engine_js`( `type`, `type_text`, `js_component`, `dependent`) VALUES ( 'general', NULL, 'echart4', NULL);

-- 2021-07-07 全站是否置灰，默认0
ALTER TABLE t_website ADD gray tinyint(1) default 0 COMMENT '全站是否置灰(0否，1是）';

-- 2021-08-04 t_engine 表添加rest_url字段
ALTER TABLE t_engine ADD rest_classify_url varchar(256) default '' COMMENT 'rest 分类接口地址';
ALTER TABLE t_engine ADD rest_data_url varchar(256) default '' COMMENT 'rest 数据接口地址';

ALTER TABLE t_application ADD rest_classify_url varchar(256) default '' COMMENT 'rest 分类接口地址';
ALTER TABLE t_application ADD rest_data_url varchar(256) default '' COMMENT 'rest 数据接口地址';
-- 2021-08-04 新增modules前端组件表



-- 2021-08-04 超管cname域名管理列表
INSERT INTO `t_menu`(`id`, `name`, `url`, `role`) VALUES (3, '全站域名管理列表', '/sadmin/website-domain-list', 1);
-- 2021-08-04 网站数据导出表
DROP TABLE IF EXISTS `t_data_download_record`;
CREATE TABLE `t_data_download_record`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `website_id` int(11) NULL DEFAULT NULL COMMENT '网站id',
  `wfwfid` int(11) NULL DEFAULT NULL COMMENT '微服务fid',
  `uid` int(11) NULL DEFAULT NULL COMMENT 'uid',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '修改下载状态时间',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `delete` int(2) NULL DEFAULT 1 COMMENT '是否删除 1删除 2不删除',
  `localization` int(2) NULL DEFAULT NULL COMMENT '是否国产化 1是国产化 2不是国产化',
  `status` int(2) NULL DEFAULT 1 COMMENT '状态: 1待下载  2下载中 3已下载',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- 新增2.0 所需要的表结构
CREATE TABLE `t_module_content` (
                                    `id` int(11) NOT NULL AUTO_INCREMENT,
                                    `title` varchar(255) DEFAULT NULL,
                                    `content` text COMMENT 'json文本内容',
                                    `type_id` int(11) DEFAULT NULL COMMENT '分类ID',
                                    `cover` varchar(255) DEFAULT NULL,
                                    `app_id` int(11) DEFAULT NULL COMMENT '应用ID，只有样式和应用才有该字段，组件是没有的',
                                    `engine_style` int(5) DEFAULT NULL COMMENT '引擎样式',
                                    `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8mb4;



CREATE TABLE `t_module_type` (
                                 `id` int(11) NOT NULL AUTO_INCREMENT,
                                 `name` varchar(255) DEFAULT NULL,
                                 `pid` int(11) DEFAULT NULL,
                                 `status` tinyint(1) DEFAULT NULL COMMENT '状态',
                                 PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=15 DEFAULT CHARSET=utf8mb4 COMMENT='2.0 版本组件分类';



-- 新增轮播js 依赖
INSERT INTO `portal`.`t_engine_js`(`type`, `type_text`, `js_component`, `dependent`) VALUES ( 'slide', NULL, 'swiperProgress', NULL);
INSERT INTO `portal`.`t_engine_js`(`type`, `type_text`, `js_component`, `dependent`) VALUES ( 'general', NULL, 'swiperProgress', NULL);

-- 2021-08-13 云南大学 增加数据统计模块
INSERT INTO portal.t_organization_application(wfwfid,application_id, name, status, create_time)
select 1332,id,'数据统计（新版接口）',1,now() from t_application where data_url ='/app/data/count/6/div?pageType=2' limit 1;

-- 2021-08-24 2.0 新增表
DROP TABLE IF EXISTS `t_page_modify`;
CREATE TABLE `t_page_modify`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `page_id` int(11) NULL DEFAULT NULL,
  `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改后的首页跳转地址',
  `version` tinyint(1) NULL DEFAULT NULL COMMENT '版本号，1，2，3',
  `content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT 'html修改后的文本',
  `created_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP,
  `domain` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '网站域名，文件夹名',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_page_modify_task
-- ----------------------------
DROP TABLE IF EXISTS `t_page_modify_task`;
CREATE TABLE `t_page_modify_task`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `page_id` int(11) NULL DEFAULT NULL,
  `status` tinyint(1) NULL DEFAULT NULL COMMENT '任务状态,1已执行，0未执行，-1取消 ，3预览立即执行的任务',
  `created_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `ssr_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'ssr服务id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;

-- 2021-08-25 webjson app端json结构
ALTER TABLE t_web_json ADD column app_content mediumtext  COMMENT '网站app端对应webjson';



-- 2021-08-27 2.0表更新
DROP TABLE IF EXISTS `t_page_modify`;
CREATE TABLE `t_page_modify`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `page_id` int(11) NULL DEFAULT NULL,
  `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改后的首页跳转地址',
  `created_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP,
  `app_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改后的移动端首页跳转地址',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_page_modify_task
-- ----------------------------
DROP TABLE IF EXISTS `t_page_modify_task`;
CREATE TABLE `t_page_modify_task`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `page_id` int(11) NULL DEFAULT NULL,
  `status` tinyint(1) NULL DEFAULT NULL COMMENT '任务状态,1已执行，0未执行，-1取消 ，3预览立即执行的任务',
  `created_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `is_app` tinyint(1) NULL DEFAULT 0 COMMENT '是否移动端任务',
  `page_modify_id` int(11) NULL DEFAULT NULL COMMENT '页面修改ID',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;

-- 2021-08-31 删除general的echart引用
DELETE FROM `portal`.t_engine_js WHERE `type` = 'general' and js_component = 'echart4';

-- 2021-09-06 2.0任务生成文件索引后缀
ALTER TABLE t_page_modify_task ADD column task_file_index TINYINT(1) DEFAULT null  COMMENT '任务生成文件索引后缀';

-- 2021-09-08 门户模板是否隐藏
ALTER TABLE t_template ADD column is_hide TINYINT(1) DEFAULT 0  COMMENT '是否隐藏默认，默认：0不隐藏，1隐藏';


-- 2021-09-07 是否强制校验域名，默认1
ALTER TABLE t_website ADD force_check_domain tinyint(1) default 1 COMMENT '是否强制校验域名(0否，1是）';
-- 修改浙图的所有网站，都把 force_check_domain = 0
update t_website set force_check_domain = 0 where wfwfid = 2120;

-- sql修改： 修改全屏轮播图的应用信息
update t_application set icon_url = '/engine2/assets/images/style/index/icon_slide.png' ,
                         data_url = '/engine2/general/300/div',
                         admin_url = '/engine2/admin/300' where id = 300;


update t_application set icon_url = '/engine2/assets/images/style/index/icon_slide.png' ,
                         data_url = '/engine2/e1-slide/21/2001/div',
                         admin_url = '/engine2/admin/2001',
                         clone_url = '/engine2/general/clone' where id = 2001;

-- 课表服务， 针对个人空间的
update t_application
set data_url = '/engine2/e1-slide/21/2001/div',
    clone_url = '',
    admin_url = '' where id = 2001

-- 修改智慧问答应用配置
update t_application set
                         data_url = '/engine2/e1-text/reader-consult/div',
                         admin_url = '',
                         clone_url = '',
                         more_url = '' where id = 114;

-- 修改图书捐赠应用配置
update t_application set
                         data_url = '/engine2/e1-text/book-donate/div',
                         admin_url = '',
                         clone_url = '',
                         more_url = '' where id = 115;

-- 修改应用图标的路径，有engine改为engine2   暂时不用
-- update t_application set icon_url = REPLACE(icon_url,'/engine/','/engine2/');

-- 2021-09-10 修改读者荐购应用设置的修改
update t_application set data_url = '/engine2/e1-graphic/reader-recommend/112/div',
                         admin_url = '',
                         clone_url = '',
                         more_url = '' where id = 112;

-- 2021-09-10 预览页地址
ALTER TABLE t_page ADD url VARCHAR(128) default NULL COMMENT '网页pc预览地址';
ALTER TABLE t_page ADD app_url VARCHAR(128) default NULL COMMENT '网页app预览地址';

-- 2021-09-10 删除无效字段
ALTER TABLE `portal`.`t_page_modify_task` DROP COLUMN `page_modify_id`;
-- 2021-09-10 删除t_page_modify表
DROP TABLE IF EXISTS `t_page_modify`;


CREATE TABLE `t_gray_wfwfid` (
                                 `id` int(11) NOT NULL AUTO_INCREMENT,
                                 `wfwfid` int(11) DEFAULT NULL,
                                 `name` varchar(255) DEFAULT NULL COMMENT '单位名称',
                                 PRIMARY KEY (`id`),
                                 UNIQUE KEY `idx_gray_wfwfid` (`wfwfid`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 2021-09-23 白名单和插件表添加page级别控制
ALTER TABLE `portal`.`t_website_ips` ADD COLUMN `page_ids` text NULL COMMENT '网页ID';
ALTER TABLE `portal`.`t_website_plugs` ADD COLUMN `page_ids` text NULL COMMENT '网页ID';

-- 2021-09-26 网站在线人数统计字段
ALTER TABLE t_website ADD COLUMN is_count_user_online TINYINT(1) DEFAULT FALSE COMMENT '在线人数统计';

-- 2021-09-27 超管新增请求异常信息列表页
INSERT INTO t_menu (name, url, role) VALUES ('请求异常信息列表', '/sadmin/request-abnormal-info-list', 1);

-- 2021-10-13 可用于统一替换第三方登录地址的登录地址前缀 关系表
DROP TABLE IF EXISTS `t_login_url_config`;
CREATE TABLE `t_login_url_config`  (
  `wfwfid` int(10) NOT NULL,
  `website_id` int(11) NULL DEFAULT NULL,
  `login_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '可用于统一替换登录地址的登录地址前缀',
  PRIMARY KEY (`wfwfid`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;

-- 2021-10-15 新权限表，新增权限类型字段
ALTER TABLE `portal`.`t_user_app_permission_new` ADD COLUMN `type` int(2) DEFAULT '1' COMMENT '权限类型 type 1应用权限，2系统权限';

-- 2021-11-1 后台登录表新增密码有效期
ALTER TABLE t_admin ADD password_validity_period bigint(20) DEFAULT '0' COMMENT '密码有效期';

-- 2021-11-4 是否管理员ip限制
ALTER TABLE t_website_ips ADD is_admin TINYINT(1) DEFAULT 0 COMMENT '是否管理员ip限制';

-- 2021-11-11
ALTER TABLE t_website ADD cdn_version int(11) DEFAULT 1 COMMENT 'cdn缓存的版本号';
-- 2021-12-01
ALTER TABLE t_template_type ADD product_type TINYINT(2) DEFAULT 0 COMMENT '模板类型分类，0： 老版本，1 ：2.0大屏模板';

-- 2021-12-01 修改阳泉网站封面
UPDATE `portal`.`t_website` SET `cover` = '/app/assets/images/yangquan/6.png' WHERE `id` = 111170;
UPDATE `portal`.`t_website` SET `cover` = '/app/assets/images/yangquan/5.png' WHERE `id` = 111171;
UPDATE `portal`.`t_website` SET `cover` = '/app/assets/images/yangquan/3.png' WHERE `id` = 86807;
UPDATE `portal`.`t_website` SET `cover` = '/app/assets/images/yangquan/4.png' WHERE `id` = 86806;
UPDATE `portal`.`t_website` SET `cover` = '/app/assets/images/yangquan/2.png' WHERE `id` = 86808;
UPDATE `portal`.`t_website` SET `cover` = '/app/assets/images/yangquan/1.png' WHERE `id` = 86824;

-- 2021-11-03 对外接口加密配置
CREATE TABLE `t_key_config` (
                                `id` int(11) NOT NULL,
                                `product_name` varchar(255) DEFAULT NULL COMMENT '产品名称',
                                `wfwfid` int(11) DEFAULT NULL,
                                `public_key` varchar(50) NOT NULL COMMENT '明文key',
                                `secret_key` varchar(255) NOT NULL COMMENT '密文key',
                                PRIMARY KEY (`id`),
                                UNIQUE KEY `public_key` (`public_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

ALTER TABLE `portal`.`t_key_config` MODIFY COLUMN `id` int(11) NOT NULL AUTO_INCREMENT FIRST;
INSERT INTO `portal`.`t_key_config` (`product_name`, `wfwfid`, `public_key`, `secret_key`) VALUES ('网站登录记录接口',null,'f5ea86eff5cb4532a86c4169bef20ce1','i[doiw09$@kd[po0');

-- 2021-12-07 网站归属地，用于大屏列表页
ALTER TABLE `portal`.`t_website` ADD address VARCHAR(100) DEFAULT '' COMMENT '网站归属地';


DROP TABLE IF EXISTS `t_zj_proxy`;
CREATE TABLE `t_zj_proxy` (
                              `id` int(11) NOT NULL AUTO_INCREMENT,
                              `nw` varchar(255) DEFAULT NULL COMMENT '内网地址',
                              `ww` varchar(255) DEFAULT NULL COMMENT '外网地址',
                              `name` varchar(255) DEFAULT NULL COMMENT '配置项名称',
                              PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 2021-12-07 获取GeneralData数据接口，key配置
INSERT INTO `portal`.`t_key_config` (`product_name`, `wfwfid`, `public_key`, `secret_key`) VALUES ('获取GeneralData数据接口',null,'778453700e0841e980ca0a7f904940c8','i[dsdfd6oP09$@kd[y');

-- 2021-12-13 组件状态
ALTER TABLE t_module_content ADD status TINYINT(1)  COMMENT '使用状态1启用。0未启用';


-- 2021-12-14 湖北数字文化馆插件
INSERT INTO `t_website_plugs`( `website_id`, `type`, `plugs`, `page_ids`) VALUES ( 111560, 2, '<script type="text/javascript" src="/assets/plugs/hbszwhg.js"></script>','202995');

-- 添加网页是否需要登录的控制，目前后台控制即可，还未开放给前端配置。
ALTER TABLE t_page ADD login int(2) DEFAULT '0'  COMMENT '使用状态1需要登录。0不需要';
-- 2021-12-24 字段默认值
ALTER TABLE  t_module_content MODIFY status TINYINT(1) DEFAULT 1  COMMENT '使用状态1启用。0未启用';
-- 2022-01-10 大屏不显示的应用类型 2.0 使用
INSERT INTO `portal`.`t_module_type`(`id`, `name`, `pid`, `status`) VALUES (10016, '大屏不显示的应用', 0, 1);
-- 2022-01-19 收藏人UID,
ALTER TABLE `portal`.`t_module_content` ADD create_uid int(11) DEFAULT 0 COMMENT '收藏人UID，默认0 系统应用';
-- 2022-03-09 浙图站点首次登录记录
CREATE TABLE `t_zj_library_user`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `sfid` int(11) NOT NULL COMMENT '子站单位ID',
  `uid` int(11) NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;

-- 添加死链记录表。
CREATE TABLE `t_dead_url` (
                              `id` int(11) NOT NULL AUTO_INCREMENT,
                              `website_id` int(11) DEFAULT NULL,
                              `page_id` int(11) DEFAULT NULL,
                              `url` varchar(1000) DEFAULT NULL,
                              `code` varchar(255) DEFAULT NULL,
                              `intro` varchar(500) DEFAULT NULL COMMENT '描述',
                              PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 添加网站克隆配置表。
CREATE TABLE `t_website_clone_config` (
                                          `id` int(11) NOT NULL AUTO_INCREMENT,
                                          `website_id` int(11) DEFAULT NULL,
                                          `config_url` varchar(500) DEFAULT NULL COMMENT '配置的接口地址',
                                          `app_id` int(11) DEFAULT NULL,
                                          `app_type` int(2) DEFAULT NULL COMMENT '1 div应用，2iframe应用',
                                          PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;



-- 2022-03-16 2.0通过三级域名访问大屏首页，page的url参数替换 上线才执行，         镜像不执行！！！
UPDATE t_page SET url = replace(t_page.url,'pageId','p') WHERE id in(SELECT home_page_id FROM t_website WHERE id in(17468,17467,18340,18336,18337,18335));
UPDATE t_page SET url = replace(t_page.url,'websiteId','w') WHERE id in(SELECT home_page_id FROM t_website WHERE id in(17468,17467,18340,18336,18337,18335));

ALTER TABLE t_website_clone_config ADD app_src int(11) default null COMMENT '原appID';

-- 2022-03-18 2.0 前端存储轮播页面配置表
CREATE TABLE `t_website_pages`  (
  `web_id` int(11) NOT NULL,
  `page_list_json` json NULL,
  PRIMARY KEY (`web_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- 2022-03-21 网站访问量倍数配置
CREATE TABLE `t_web_visit_multi`  (
  `id` int(11) NOT NULL COMMENT '网站ID',
  `multiple` tinyint(2) NULL DEFAULT NULL COMMENT '访问量倍数',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- 2022-04-19 克隆配置表 需要克隆的原pageId
ALTER TABLE t_website_clone_config ADD page_id_src INT(11)  COMMENT '需要克隆的原pageId';
ALTER TABLE t_website_clone_config modify app_type INT(2) comment '1 div应用，2iframe应用，3表单详情';

-- 2022-04-22 外接数据源访问统计列表
INSERT INTO `portal`.`t_menu`(`name`, `url`, `role`) VALUES ('外接数据源访问统计列表', '/sadmin/data-center-visit-info-list', 1);
-- 2022-04-25 2.0 画布应用分类
INSERT INTO `portal`.`t_module_type`(`id`, `name`, `pid`, `status`) VALUES (100, '画布应用', 0, 1);
-- 2022-04-26 iframe应用
INSERT INTO `portal`.`t_engine`(`id`, `name`, `icon_url`, `type`, `data_url`, `clone_url`, `more_url`, `admin_url`, `status`, `create_time`, `rest_classify_url`, `rest_data_url`)
VALUES (209, 'Iframe嵌入', '/engine2/assets/images/style/index/iframe.svg', 0, '/engine2/iframe/%d/div', '/engine2/iframe/clone', NULL, '/engine2/admin/iframe/%d', 1, '2022-04-26 14:41:33', '', '');

INSERT INTO `portal`.`t_engine_modules`(`engine_id`, `modules_id`) VALUES (209, 1);


-- 2022-05-06 网站数据下载配置表(镜像忽略)
CREATE TABLE `portal`.`t_data_download_configure` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `configure_id` varchar(50) DEFAULT NULL COMMENT '配置id',
  `configure_value` tinyint(1) DEFAULT '0' COMMENT '配置id对应值',
  `configure_name` varchar(50) DEFAULT NULL COMMENT '配置名称',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8;

-- 2022-05-06 网站数据下载配置表(镜像忽略)
INSERT INTO `portal`.`t_data_download_configure`(`id`, `configure_id`, `configure_value`, `configure_name`) VALUES (1, 'prod-portal', 1, '云端网站数据下载');
INSERT INTO `portal`.`t_data_download_configure`(`id`, `configure_id`, `configure_value`, `configure_name`) VALUES (2, 'mirror-client', 1, '镜像客户端下载');


-- 2020-05-07 自有域名cookie的domain配置项
ALTER TABLE `portal`.`t_website_domain` ADD COLUMN `cookie_domain` varchar(255) NULL COMMENT 'cookie的域名（为空使用domain）';
-- 2020-05-07 北京交通职业运输学院cookie的domain
update `portal`.t_website_domain set cookie_domain = 'bjjt.edu.cn' where `domain` like '%bjjt.edu.cn%';
-- 2020-05-07 西电cookie的domain
update `portal`.t_website_domain set cookie_domain = 'xidian.edu.cn' where website_id = 13313;




-- 2022-05-09 超星域名配置表
CREATE TABLE `portal`.`t_cx_domain_configure` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `configure_id` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `configure_value` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `configure_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `configure_type` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `index_configure_id` (`configure_id`)
) ENGINE=InnoDB AUTO_INCREMENT=105 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC;

INSERT INTO `portal`.`t_cx_domain_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (1, 'http_passport2', 'http://passport2.chaoxing.com', NULL, NULL);
INSERT INTO `portal`.`t_cx_domain_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (2, 'https_passport2', 'https://passport2.chaoxing.com', NULL, NULL);
INSERT INTO `portal`.`t_cx_domain_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (3, 'http_passport2_api', 'http://passport2-api.chaoxing.com', NULL, NULL);
INSERT INTO `portal`.`t_cx_domain_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (4, 'https_passport2_api', 'https://passport2-api.chaoxing.com', NULL, NULL);
INSERT INTO `portal`.`t_cx_domain_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (5, 'http_v1', 'http://v1.chaoxing.com', NULL, NULL);
INSERT INTO `portal`.`t_cx_domain_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (6, 'https_v1', 'https://v1.chaoxing.com', NULL, NULL);
INSERT INTO `portal`.`t_cx_domain_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (7, 'http_i', 'http://i.chaoxing.com', NULL, NULL);
INSERT INTO `portal`.`t_cx_domain_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (8, 'https_i', 'https://i.chaoxing.com', NULL, NULL);
INSERT INTO `portal`.`t_cx_domain_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (9, 'http_uc1_ans', 'http://uc.chaoxing.com', NULL, NULL);
INSERT INTO `portal`.`t_cx_domain_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (10, 'https_uc1_ans', 'https://uc.chaoxing.com', NULL, NULL);
INSERT INTO `portal`.`t_cx_domain_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (11, 'http_vjx', 'http://vjx.chaoxing.com', NULL, NULL);
INSERT INTO `portal`.`t_cx_domain_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (12, 'https_vjx', 'https://vjx.chaoxing.com', NULL, NULL);
INSERT INTO `portal`.`t_cx_domain_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (13, 'http_sms', 'http://sms.chaoxing.com', NULL, NULL);
INSERT INTO `portal`.`t_cx_domain_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (14, 'https_sms', 'https://sms.chaoxing.com', NULL, NULL);
INSERT INTO `portal`.`t_cx_domain_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (15, 'http_passport_basicedu', 'http://passport.basicedu.chaoxing.com', NULL, NULL);
INSERT INTO `portal`.`t_cx_domain_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (16, 'https_passport_basicedu', 'https://passport.basicedu.chaoxing.com', NULL, NULL);
INSERT INTO `portal`.`t_cx_domain_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (17, 'http_cs_ananas', 'http://cs.ananas.chaoxing.com', NULL, NULL);
INSERT INTO `portal`.`t_cx_domain_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (18, 'https_cs_ananas', 'https://cs.ananas.chaoxing.com', NULL, NULL);
INSERT INTO `portal`.`t_cx_domain_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (19, 'http_m_oa', 'http://m.oa.chaoxing.com', NULL, NULL);
INSERT INTO `portal`.`t_cx_domain_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (20, 'https_m_oa', 'https://m.oa.chaoxing.com', NULL, NULL);
INSERT INTO `portal`.`t_cx_domain_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (21, 'http_yz4', 'http://yz4.chaoxing.com', NULL, NULL);
INSERT INTO `portal`.`t_cx_domain_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (22, 'https_yz4', 'https://yz4.chaoxing.com', NULL, NULL);
INSERT INTO `portal`.`t_cx_domain_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (23, 'http_api_hd', 'http://api.hd.chaoxing.com', NULL, NULL);
INSERT INTO `portal`.`t_cx_domain_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (24, 'https_api_hd', 'https://api.hd.chaoxing.com', NULL, NULL);
INSERT INTO `portal`.`t_cx_domain_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (25, 'http_info', 'http://info.chaoxing.com', NULL, NULL);
INSERT INTO `portal`.`t_cx_domain_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (26, 'https_info', 'https://info.chaoxing.com', NULL, NULL);
INSERT INTO `portal`.`t_cx_domain_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (27, 'http_yz1', 'http://yz1.chaoxing.com', NULL, NULL);
INSERT INTO `portal`.`t_cx_domain_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (28, 'https_yz1', 'https://yz1.chaoxing.com', NULL, NULL);
INSERT INTO `portal`.`t_cx_domain_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (29, 'http_mooc1_api', 'http://mooc1-api.chaoxing.com', NULL, NULL);
INSERT INTO `portal`.`t_cx_domain_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (30, 'https_mooc1_api', 'https://mooc1-api.chaoxing.com', NULL, NULL);
INSERT INTO `portal`.`t_cx_domain_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (31, 'http_bigdata_ans', 'http://bigdata-ans.chaoxing.com', NULL, NULL);
INSERT INTO `portal`.`t_cx_domain_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (32, 'https_bigdata_ans', 'https://bigdata-ans.chaoxing.com', NULL, NULL);
INSERT INTO `portal`.`t_cx_domain_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (33, 'http_xueyinonline', 'http://xueyinonline.chaoxing.com', NULL, NULL);
INSERT INTO `portal`.`t_cx_domain_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (34, 'https_xueyinonline', 'https://xueyinonline.chaoxing.com', NULL, NULL);
INSERT INTO `portal`.`t_cx_domain_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (35, 'http_mooc1', 'http://mooc1.chaoxing.com', NULL, NULL);
INSERT INTO `portal`.`t_cx_domain_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (36, 'https_mooc1', 'https://mooc1.chaoxing.com', NULL, NULL);
INSERT INTO `portal`.`t_cx_domain_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (37, 'http_photo', 'http://photo.chaoxing.com', NULL, NULL);
INSERT INTO `portal`.`t_cx_domain_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (38, 'https_photo', 'https://photo.chaoxing.com', NULL, NULL);
INSERT INTO `portal`.`t_cx_domain_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (39, 'http_p_ananas', 'http://p.ananas.chaoxing.com', NULL, NULL);
INSERT INTO `portal`.`t_cx_domain_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (40, 'https_p_ananas', 'https://p.ananas.chaoxing.com', NULL, NULL);
INSERT INTO `portal`.`t_cx_domain_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (41, 'http_noteyd', 'http://noteyd.chaoxing.com', NULL, NULL);
INSERT INTO `portal`.`t_cx_domain_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (42, 'https_noteyd', 'https://noteyd.chaoxing.com', NULL, NULL);
INSERT INTO `portal`.`t_cx_domain_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (43, 'http_ztapi', 'http://ztapi.chaoxing.com', NULL, NULL);
INSERT INTO `portal`.`t_cx_domain_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (44, 'https_ztapi', 'https://ztapi.chaoxing.com', NULL, NULL);
INSERT INTO `portal`.`t_cx_domain_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (45, 'http_wisdom', 'http://wisdom.chaoxing.com', NULL, NULL);
INSERT INTO `portal`.`t_cx_domain_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (46, 'https_wisdom', 'https://wisdom.chaoxing.com', NULL, NULL);
INSERT INTO `portal`.`t_cx_domain_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (47, 'http_appcd', 'http://appcd.chaoxing.com', NULL, NULL);
INSERT INTO `portal`.`t_cx_domain_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (48, 'https_appcd', 'https://appcd.chaoxing.com', NULL, NULL);
INSERT INTO `portal`.`t_cx_domain_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (49, 'http_rec', 'http://rec.chaoxing.com', NULL, NULL);
INSERT INTO `portal`.`t_cx_domain_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (50, 'https_rec', 'https://rec.chaoxing.com', NULL, NULL);
INSERT INTO `portal`.`t_cx_domain_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (51, 'http_photo_fanya', 'http://photo.fanya.chaoxing.com', NULL, NULL);
INSERT INTO `portal`.`t_cx_domain_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (52, 'https_photo_fanya', 'https://photo.fanya.chaoxing.com', NULL, NULL);
INSERT INTO `portal`.`t_cx_domain_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (53, 'http_xueya', 'http://xueya.chaoxing.com', NULL, NULL);
INSERT INTO `portal`.`t_cx_domain_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (54, 'https_xueya', 'https://xueya.chaoxing.com', NULL, NULL);
INSERT INTO `portal`.`t_cx_domain_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (55, 'http_znxs', 'http://znxs.chaoxing.com', NULL, NULL);
INSERT INTO `portal`.`t_cx_domain_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (56, 'https_znxs', 'https://znxs.chaoxing.com', NULL, NULL);
INSERT INTO `portal`.`t_cx_domain_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (57, 'http_xyhd', 'http://xyhd.chaoxing.com', NULL, NULL);
INSERT INTO `portal`.`t_cx_domain_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (58, 'https_xyhd', 'https://xyhd.chaoxing.com', NULL, NULL);
INSERT INTO `portal`.`t_cx_domain_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (59, 'http_astats_fy', 'http://astats.fy.chaoxing.com', NULL, NULL);
INSERT INTO `portal`.`t_cx_domain_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (60, 'https_astats_fy', 'https://astats.fy.chaoxing.com', NULL, NULL);
INSERT INTO `portal`.`t_cx_domain_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (61, 'http_jwdatatb_fy', 'http://jwdatatb.fy.chaoxing.com', NULL, NULL);
INSERT INTO `portal`.`t_cx_domain_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (62, 'https_jwdatatb_fy', 'https://jwdatatb.fy.chaoxing.com', NULL, NULL);
INSERT INTO `portal`.`t_cx_domain_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (63, 'http_previewyd', 'http://previewyd.chaoxing.com', NULL, NULL);
INSERT INTO `portal`.`t_cx_domain_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (64, 'https_previewyd', 'https://previewyd.chaoxing.com', NULL, NULL);
INSERT INTO `portal`.`t_cx_domain_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (65, 'http_notice', 'http://notice.chaoxing.com', NULL, NULL);
INSERT INTO `portal`.`t_cx_domain_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (66, 'https_notice', 'https://notice.chaoxing.com', NULL, NULL);
INSERT INTO `portal`.`t_cx_domain_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (67, 'http_www', 'http://www.chaoxing.com', NULL, NULL);
INSERT INTO `portal`.`t_cx_domain_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (68, 'https_www', 'https://www.chaoxing.com', NULL, NULL);
INSERT INTO `portal`.`t_cx_domain_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (69, 'http_pan_yz', 'http://pan-yz.chaoxing.com', NULL, NULL);
INSERT INTO `portal`.`t_cx_domain_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (70, 'https_pan_yz', 'https://pan-yz.chaoxing.com', NULL, NULL);
INSERT INTO `portal`.`t_cx_domain_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (71, 'http_jcztapi', 'http://jcztapi.chaoxing.com', NULL, NULL);
INSERT INTO `portal`.`t_cx_domain_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (72, 'https_jcztapi', 'https://jcztapi.chaoxing.com', NULL, NULL);
INSERT INTO `portal`.`t_cx_domain_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (73, 'http_learn', 'http://learn.chaoxing.com', NULL, NULL);
INSERT INTO `portal`.`t_cx_domain_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (74, 'https_learn', 'https://learn.chaoxing.com', NULL, NULL);
INSERT INTO `portal`.`t_cx_domain_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (75, 'http_guanli', 'http://guanli.chaoxing.com', NULL, NULL);
INSERT INTO `portal`.`t_cx_domain_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (76, 'https_guanli', 'https://guanli.chaoxing.com', NULL, NULL);
INSERT INTO `portal`.`t_cx_domain_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (77, 'http_api_reading', 'http://api.reading.chaoxing.com', NULL, NULL);
INSERT INTO `portal`.`t_cx_domain_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (78, 'https_api_reading', 'https://api.reading.chaoxing.com', NULL, NULL);
INSERT INTO `portal`.`t_cx_domain_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (79, 'http_api_hd_reading', 'http://api.hd.reading.chaoxing.com', NULL, NULL);
INSERT INTO `portal`.`t_cx_domain_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (80, 'https_api_hd_reading', 'https://api.hd.reading.chaoxing.com', NULL, NULL);
INSERT INTO `portal`.`t_cx_domain_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (81, 'http_apps', 'http://apps.chaoxing.com', NULL, NULL);
INSERT INTO `portal`.`t_cx_domain_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (82, 'https_apps', 'https://apps.chaoxing.com', NULL, NULL);
INSERT INTO `portal`.`t_cx_domain_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (83, 'http_qikan', 'http://qikan.chaoxing.com', NULL, NULL);
INSERT INTO `portal`.`t_cx_domain_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (84, 'https_qikan', 'https://qikan.chaoxing.com', NULL, NULL);
INSERT INTO `portal`.`t_cx_domain_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (85, 'http_v0', 'http://v0.chaoxing.com', NULL, NULL);
INSERT INTO `portal`.`t_cx_domain_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (86, 'https_v0', 'https://v0.chaoxing.com', NULL, NULL);
INSERT INTO `portal`.`t_cx_domain_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (87, 'http_mooc1_2', 'http://mooc1-2.chaoxing.com', NULL, NULL);
INSERT INTO `portal`.`t_cx_domain_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (88, 'https_mooc1_2', 'https://mooc1-2.chaoxing.com', NULL, NULL);
INSERT INTO `portal`.`t_cx_domain_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (89, 'http_thesis', 'http://thesis.chaoxing.com', NULL, NULL);
INSERT INTO `portal`.`t_cx_domain_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (90, 'https_thesis', 'https://thesis.chaoxing.com', NULL, NULL);
INSERT INTO `portal`.`t_cx_domain_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (91, 'http_mooc1_1', 'http://mooc1-1.chaoxing.com', NULL, NULL);
INSERT INTO `portal`.`t_cx_domain_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (92, 'https_mooc1_1', 'https://mooc1-1.chaoxing.com', NULL, NULL);
INSERT INTO `portal`.`t_cx_domain_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (93, 'http_super_fy', 'http://super.fy.chaoxing.com', NULL, NULL);
INSERT INTO `portal`.`t_cx_domain_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (94, 'https_super_fy', 'https://super.fy.chaoxing.com', NULL, NULL);
INSERT INTO `portal`.`t_cx_domain_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (95, 'http_fanya_zyk2', 'http://fanya.zyk2.chaoxing.com', NULL, NULL);
INSERT INTO `portal`.`t_cx_domain_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (96, 'https_fanya_zyk2', 'https://fanya.zyk2.chaoxing.com', NULL, NULL);
INSERT INTO `portal`.`t_cx_domain_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (97, 'http_ananas.mooc1', 'http://ananas.mooc1.chaoxing.com', NULL, NULL);
INSERT INTO `portal`.`t_cx_domain_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (98, 'https_ananas.mooc1', 'https://ananas.mooc1.chaoxing.com', NULL, NULL);
INSERT INTO `portal`.`t_cx_domain_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (99, 'http_interfacetransfer', 'http://interfacetransfer.chaoxing.com', NULL, NULL);
INSERT INTO `portal`.`t_cx_domain_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (100, 'https_interfacetransfer', 'https://interfacetransfer.chaoxing.com', NULL, NULL);
INSERT INTO `portal`.`t_cx_domain_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (101, 'http_live', 'http://live.chaoxing.com', NULL, NULL);
INSERT INTO `portal`.`t_cx_domain_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (102, 'https_live', 'https://live.chaoxing.com', NULL, NULL);
INSERT INTO `portal`.`t_cx_domain_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (103, 'http_qk', 'http://qk.chaoxing.com', NULL, NULL);
INSERT INTO `portal`.`t_cx_domain_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (104, 'https_qk', 'https://qk.chaoxing.com', NULL, NULL);
INSERT INTO `portal`.`t_cx_domain_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (105, 'http_video_fy', 'http://video.fy.chaoxing.com', NULL, NULL);
INSERT INTO `portal`.`t_cx_domain_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (106, 'https_video_fy', 'https://video.fy.chaoxing.com', NULL, NULL);


-- 2022-05-09 配置文件配置表
CREATE TABLE `portal`.`t_properties_configure` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `configure_id` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `configure_value` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `configure_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `configure_type` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `index_configure_id` (`configure_id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC;

INSERT INTO `portal`.`t_properties_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (1, 'vjx_pass_id', '7f127639e8dc4abfb77e30d3b4b0c57d', NULL, NULL);
INSERT INTO `portal`.`t_properties_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (2, 'vjx_app_key', 'Nmz59J30mpD7yU4r', NULL, NULL);
INSERT INTO `portal`.`t_properties_configure`(`id`, `configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES (3, 'vjx_salt', 'f10nrw0imwr2arjo1mix20ii2', NULL, NULL);
-- 2022-05-12 2.0的大屏图表 默认应用
INSERT INTO `portal`.`t_application`(`id`, `icon_url`, `type`, `data_url`, `clone_url`, `more_url`, `admin_url`, `status`, `create_time`, `source`, `rest_classify_url`, `rest_data_url`) VALUES (3001, '/engine2/assets/images/style/index/icon_icon.png', 0, '/engine2/chart/15826/div', '/engine2/chart/clone', NULL, '', 0, NULL, 1, '', '');
INSERT INTO `portal`.`t_organization_application`(`wfwfid`, `application_id`, `name`, `status`, `create_time`, `website_id`, `page_id`) VALUES (-10, 3001, '大屏图表', 1, '2022-05-11 11:36:07', -10, -10);




-- 2022-05-10 公共文化网站克隆
CREATE TABLE `portal`.`t_website_wfw_clone`  (
                                      `id` int(11) NOT NULL AUTO_INCREMENT,
                                      `product_id` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '产品工厂ID',
                                      `website_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '克隆后网站名称',
                                      `website_id` int(20) NULL DEFAULT NULL COMMENT '克隆的源网站ID',
                                      `target_website_id` int(15) NULL DEFAULT NULL COMMENT '克隆后的网站ID',
                                      `create_uid` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                                      `wfwfid` int(10) NULL DEFAULT NULL COMMENT '网站wfwfid',
                                      `clone_result` json NULL COMMENT '微服务克隆结果',
                                      `status` int(1) NULL DEFAULT NULL COMMENT '0-微服务克隆中，1-微服务克隆成功 2-网站克隆成功 -1克隆失败',
                                      `create_time` datetime(0) NULL DEFAULT NULL,
                                      `update_time` datetime(0) NULL DEFAULT NULL,
                                      PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;


CREATE TABLE `portal`.`t_product`  (
                                      `id` int(11) NOT NULL AUTO_INCREMENT,
                                      `product_id` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '产品ID',
                                      `product_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '产品名称',
                                      `website_id` int(11) NULL DEFAULT NULL COMMENT '网站ID',
                                      `create_time` datetime(0) NULL DEFAULT NULL,
                                      PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;


-- 2022-05-19 门户添加是否分享
ALTER TABLE `portal`.t_website ADD share tinyint(1) DEFAULT '0' COMMENT '门户是否分享';
-- 2022-06-07 应用别名
ALTER TABLE t_organization_application ADD alias VARCHAR(128)  COMMENT '应用别名';



-- 2022-06-06 产品复制网站目标ID字段修改
ALTER TABLE `portal`.`t_website_wfw_clone` MODIFY COLUMN `target_website_id` varchar(1024) NULL DEFAULT NULL COMMENT '克隆后的网站ID';
ALTER TABLE `portal`.`t_website_wfw_clone` add column `branchs` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分馆名称';
ALTER TABLE `portal`.`t_website_wfw_clone` add column  `open_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '开通类型，总分馆、单馆';
ALTER TABLE `portal`.`t_website_wfw_clone` ADD COLUMN `branch` varchar(255) NULL COMMENT '总馆名称';


CREATE TABLE `portal`.`t_website_wfw_clone_detail`(
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `pid` int(11) NULL DEFAULT NULL COMMENT '复制ID',
    `website_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '克隆后网站名称',
    `website_id` int(20) NULL DEFAULT NULL COMMENT '克隆的源网站ID',
    `target_website_id` int(15) NULL DEFAULT NULL COMMENT '克隆后的网站ID',
    `create_uid` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
    `wfwfid` int(10) NULL DEFAULT NULL COMMENT '网站wfwfid',
    `status` int(1) NULL DEFAULT NULL COMMENT '0-微服务克隆中，1-微服务克隆成功 2-网站克隆成功 -1克隆失败，3-应用数据替换成功，-3应用数据替换失败，4-复制完成',
    `create_time` datetime(0) NULL DEFAULT NULL,
    `update_time` datetime(0) NULL DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE,
    INDEX `index_website_wfw_clone_detail_pid`(`pid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '产品复制-明细表' ROW_FORMAT = Dynamic;

-- 2022-06-14 插件新增功能字段
ALTER TABLE `portal`.`t_website_plugs` ADD COLUMN `app_id` int(11) DEFAULT NULL COMMENT '使用插件的应用ID';
ALTER TABLE `portal`.`t_website_plugs` ADD COLUMN `wfwfid` int(11) DEFAULT NULL COMMENT '使用插件的应用对应的单位';
ALTER TABLE `portal`.`t_website_plugs` ADD COLUMN `type_name` varchar (128) DEFAULT NULL COMMENT '插件分类名称';
-- 2022-06-14 别名表
CREATE TABLE `t_application_other_alias`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `page_id` int(11) NOT NULL COMMENT 'pageId',
  `alias` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '别名',
  `type` tinyint(1) NULL DEFAULT NULL COMMENT '类型，1头部，2底部',
  `des` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '描述',
  PRIMARY KEY (`id`, `page_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;


-- 2022-06-16 网站定时下线功能
CREATE TABLE `portal`.`t_job`  (
                                   `id` int(11) NOT NULL AUTO_INCREMENT,
                                   `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '任务名称',
                                   `cron_expression` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'cron执行表达式',
                                   `status` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '状态（1正常 0暂停）',
                                   `create_time` datetime(0) NULL DEFAULT NULL COMMENT '添加时间',
                                   `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
                                   `type` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '类型：service-服务，api-接口',
                                   `content` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '定时任务调用类型',
                                   `data_id` int(11) NULL DEFAULT NULL COMMENT '定时任务关联数据',
                                   `data_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '定时任务关联数据类型',
                                   `data_remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '定时任务关联数据内容',
                                   `exec_status` int(255) NULL DEFAULT NULL COMMENT '任务加入定时任务状态0-未加入，1-已加入',
                                   PRIMARY KEY (`id`) USING BTREE,
                                   INDEX `index_job_data_id`(`data_id`, `data_type`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;


-- 2022-06-24 超管后台增加查看门户日志页面
INSERT INTO `portal`.`t_menu`(`id`, `name`, `url`, `role`) VALUES (11, '门户日志列表', '/sadmin/log-list', 1);

-- 2022-07-06 网站管理后台增加ip内访问限制，website表添加字段
ALTER TABLE `portal`.`t_website` ADD COLUMN `admin_inner_ip` TINYINT(1) NULL default 0 COMMENT'仅ip内访问访问管理后台 0->否 1->是' AFTER `share`;

-- 2022-07-07 t_menu新增type字段，0->菜单栏，1->接口
ALTER TABLE `portal`.`t_menu` ADD COLUMN `type` TINYINT(1) NULL default 0 COMMENT'类型，0->菜单栏，1->接口';
truncate table `portal`.`t_menu`;
INSERT INTO `portal`.`t_menu`(`id`, `name`, `url`, `role`, `type`) VALUES (1, '网站列表 - 网站基本信息管理', '/sadmin/website-list', 2, 0);
INSERT INTO `portal`.`t_menu`(`id`, `name`, `url`, `role`, `type`) VALUES (2, '全站域名白名单列表', '/sadmin/blank-domain-list', 2, 0);
INSERT INTO `portal`.`t_menu`(`id`, `name`, `url`, `role`, `type`) VALUES (3, '全站域名管理列表', '/sadmin/website-domain-list', 2, 0);
INSERT INTO `portal`.`t_menu`(`id`, `name`, `url`, `role`, `type`) VALUES (4, '灰度网站列表', '/sadmin/website-gray-list', 2, 0);
INSERT INTO `portal`.`t_menu`(`id`, `name`, `url`, `role`, `type`) VALUES (5, '请求异常信息列表', '/sadmin/request-abnormal-info-list', 2, 0);
INSERT INTO `portal`.`t_menu`(`id`, `name`, `url`, `role`, `type`) VALUES (6, '密钥配置列表', '/sadmin/key-config-list', 2, 0);
INSERT INTO `portal`.`t_menu`(`id`, `name`, `url`, `role`, `type`) VALUES (7, '外接数据源访问记录列表', '/sadmin/data-center-visit-info-list', 2, 0);
INSERT INTO `portal`.`t_menu`(`id`, `name`, `url`, `role`, `type`) VALUES (8, '引擎实例列表', '/sadmin/instance-list', 2, 0);
INSERT INTO `portal`.`t_menu`(`id`, `name`, `url`, `role`, `type`) VALUES (9, '数据分类列表', '/sadmin/general-data-type-list', 2, 0);
INSERT INTO `portal`.`t_menu`(`id`, `name`, `url`, `role`, `type`) VALUES (10, '引擎数据列表', '/sadmin/general-data-list', 2, 0);
INSERT INTO `portal`.`t_menu`(`id`, `name`, `url`, `role`, `type`) VALUES (11, '门户日志列表', '/sadmin/log-list', 2, 0);
INSERT INTO `portal`.`t_menu`(`id`, `name`, `url`, `role`, `type`) VALUES (12, '网站插件', '/sadmin/website-plugs', 2, 0);
INSERT INTO `portal`.`t_menu`(`id`, `name`, `url`, `role`, `type`) VALUES (13, '获取网站列表', '/sadmin/websites', 2, 1);
INSERT INTO `portal`.`t_menu`(`id`, `name`, `url`, `role`, `type`) VALUES (14, '根据网站id获取子页面', '/sadmin/pageList', 2, 1);
INSERT INTO `portal`.`t_menu`(`id`, `name`, `url`, `role`, `type`) VALUES (15, '根据webJsonId获取webJson内容', '/sadmin/webJson/{id}', 2, 1);
INSERT INTO `portal`.`t_menu`(`id`, `name`, `url`, `role`, `type`) VALUES (18, '获取全站域名白名单列表', '/sadmin/domain-blank-list', 2, 1);
INSERT INTO `portal`.`t_menu`(`id`, `name`, `url`, `role`, `type`) VALUES (20, '获取全站域名管理列表', '/sadmin/query-website-domain', 2, 1);
INSERT INTO `portal`.`t_menu`(`id`, `name`, `url`, `role`, `type`) VALUES (24, '灰度网站列表 - 灰度网站信息管理', '/sadmin/query-website-gray', 2, 1);
INSERT INTO `portal`.`t_menu`(`id`, `name`, `url`, `role`, `type`) VALUES (28, '请求异常信息列表', '/sadmin/request-abnormal-infos', 2, 1);
INSERT INTO `portal`.`t_menu`(`id`, `name`, `url`, `role`, `type`) VALUES (29, '获取密钥配置列表', '/sadmin/query-key-config', 2, 1);
INSERT INTO `portal`.`t_menu`(`id`, `name`, `url`, `role`, `type`) VALUES (34, 'query-data-center-visit-count', '/sadmin/query-data-center-visit-count', 2, 1);
INSERT INTO `portal`.`t_menu`(`id`, `name`, `url`, `role`, `type`) VALUES (35, '获取日志数据', '/sadmin/log-data', 2, 1);
INSERT INTO `portal`.`t_menu`(`id`, `name`, `url`, `role`, `type`) VALUES (37, '获取网站插件', '/sadmin/get-website-plugs', 2, 1);
INSERT INTO `portal`.`t_menu`(`id`, `name`, `url`, `role`, `type`) VALUES (16, '修改webJson', '/sadmin/update-webJson', 1, 1);
INSERT INTO `portal`.`t_menu`(`id`, `name`, `url`, `role`, `type`) VALUES (17, '修改网站列表', '/sadmin/update-website', 1, 1);
INSERT INTO `portal`.`t_menu`(`id`, `name`, `url`, `role`, `type`) VALUES (19, '添加修改网站白名单', '/sadmin/add-or-update-blank-domain', 1, 1);
INSERT INTO `portal`.`t_menu`(`id`, `name`, `url`, `role`, `type`) VALUES (21, '全站域名管理列表 - 新增记录', '/sadmin/add-website-domain', 1, 1);
INSERT INTO `portal`.`t_menu`(`id`, `name`, `url`, `role`, `type`) VALUES (22, '全站域名管理列表 - 编辑记录', '/sadmin/update-website-domain', 1, 1);
INSERT INTO `portal`.`t_menu`(`id`, `name`, `url`, `role`, `type`) VALUES (23, '全站域名管理列表 - 删除记录', '/sadmin/delete-website-domain', 1, 1);
INSERT INTO `portal`.`t_menu`(`id`, `name`, `url`, `role`, `type`) VALUES (25, '灰度网站列表 - 新增', '/sadmin/website-gray-add', 1, 1);
INSERT INTO `portal`.`t_menu`(`id`, `name`, `url`, `role`, `type`) VALUES (26, '灰度网站列表 - 修改', '/sadmin/website-gray-update', 1, 1);
INSERT INTO `portal`.`t_menu`(`id`, `name`, `url`, `role`, `type`) VALUES (27, '灰度网站列表 - 删除', '/sadmin/website-gray-delete', 1, 1);
INSERT INTO `portal`.`t_menu`(`id`, `name`, `url`, `role`, `type`) VALUES (30, '密钥配置列表 - 新增', '/sadmin/key-config-add', 1, 1);
INSERT INTO `portal`.`t_menu`(`id`, `name`, `url`, `role`, `type`) VALUES (31, '密钥配置列表 - 修改', '/sadmin/key-config-update', 1, 1);
INSERT INTO `portal`.`t_menu`(`id`, `name`, `url`, `role`, `type`) VALUES (32, '密钥配置列表 - 删除', '/sadmin/key-config-delete', 1, 1);
INSERT INTO `portal`.`t_menu`(`id`, `name`, `url`, `role`, `type`) VALUES (38, '保存网站插件', '/sadmin/save-website-plugs', 1, 1);
INSERT INTO `portal`.`t_menu`(`id`, `name`, `url`, `role`, `type`) VALUES (39, '删除网站插件', '/sadmin/delete-website-plugs', 1, 1);

-- 公共图书馆新增可自定义数据后台
DROP TABLE IF EXISTS `t_website_pv_custom_data`;
CREATE TABLE `t_website_pv_custom_data` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `website_id` int(11) NOT NULL,
    `pv` bigint(255) DEFAULT NULL,
    `time` date DEFAULT NULL,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=44 DEFAULT CHARSET=utf8mb4;

DROP TABLE IF EXISTS `t_website_uv_custom_data`;
CREATE TABLE `t_website_uv_custom_data` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `website_id` int(11) NOT NULL,
    `uv` bigint(255) DEFAULT NULL,
    `time` date DEFAULT NULL,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=15 DEFAULT CHARSET=utf8mb4;

DROP TABLE IF EXISTS `t_module_statistics_custom_data`;
CREATE TABLE `t_module_statistics_custom_data` (
   `id` int(11) NOT NULL AUTO_INCREMENT,
   `website_id` int(11) NOT NULL,
   `module_name` varchar(255) DEFAULT NULL,
   `pv` bigint(255) DEFAULT NULL,
   `uv` bigint(255) DEFAULT NULL,
   `time` date DEFAULT NULL,
   PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4;
DROP TABLE IF EXISTS `t_public_library_admin`;
CREATE TABLE `t_public_library_admin` (
                                          `id` int(11) NOT NULL AUTO_INCREMENT,
                                          `website_id` int(11) DEFAULT NULL,
                                          `wfwfid` int(11) DEFAULT NULL,
                                          PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4;

-- 密钥配置表增加域名和路径字段
ALTER TABLE `portal`.`t_key_config` ADD COLUMN `domain` varchar (255) DEFAULT NULL COMMENT '域名';
ALTER TABLE `portal`.`t_key_config` ADD COLUMN `path` varchar (255) DEFAULT NULL COMMENT '路径';

-- 2022-07-28 超管后台新增样式封面及示例图
INSERT INTO `portal`.`t_menu`(`id`, `name`, `url`, `role`, `type`) VALUES (40, '样式封面及示例图', '/sadmin/background-img', 1, 0);

-- 2022-08-04 字典表
CREATE TABLE `t_dict`  (
                                    `id` int(11) NOT NULL AUTO_INCREMENT,
                                    `code` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                                    `name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                                    `value` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                                    `create_time` datetime(0) NULL DEFAULT NULL,
                                    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '系统-字典表' ROW_FORMAT = Dynamic;

INSERT INTO `t_dict`(`id`, `code`, `name`, `value`, `create_time`) VALUES (1, 'passport_appid', 'passport接口授权appi', 'a205b55eace042eb8624e51cd17a2d2d', '2022-08-04 15:44:33');
INSERT INTO `t_dict`(`id`, `code`, `name`, `value`, `create_time`) VALUES (2, 'passport_appkey', 'passport接口授权appkey', 'G&sAbb9a9uE#hnux0otpk*ou#Dl7ciG&', '2022-08-04 15:44:35');


-- 2022-08-04 超管后台增加iframe配置菜单
INSERT INTO `portal`.`t_menu`(`id`, `name`, `url`, `role`, `type`) VALUES (41, 'iframe配置列表', '/sadmin/iframe-config-list', 1, 0);
INSERT INTO `portal`.`t_menu`(`id`, `name`, `url`, `role`, `type`) VALUES (42, 'iframe配置列表 - 查询', '/sadmin/query-iframe-config', 1, 1);
INSERT INTO `portal`.`t_menu`(`id`, `name`, `url`, `role`, `type`) VALUES (43, 'iframe配置列表 - 保存', '/sadmin/save-iframe-config', 1, 1);
INSERT INTO `portal`.`t_menu`(`id`, `name`, `url`, `role`, `type`) VALUES (44, 'iframe配置列表 - 删除', '/sadmin/delete-iframe-config', 1, 1);
-- 2022-08-04 新增iframe配置表
CREATE TABLE `t_iframe_config` (
                                   `id` int(11) NOT NULL AUTO_INCREMENT,
                                   `product_id` int(11) DEFAULT NULL COMMENT '产品id',
                                   `iframe_url` varchar(255) DEFAULT NULL COMMENT 'iframe地址',
                                   `type` int(11) DEFAULT NULL COMMENT '产品类型',
                                   PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;

-- 2022-08-08 修正2.0大屏图表应用数据查询接口
UPDATE `portal`.`t_application` SET `data_url` = '/engine2/screen/15826/div' WHERE `id` = 3001;

-- 2022-08-10 产品开通添加字段
ALTER TABLE `portal`.`t_website_wfw_clone` ADD COLUMN `open_result` varchar(255) NULL COMMENT '开通结果';

-- 2022-08-10 超管后台增加最近一小时超时异常菜单
INSERT INTO `portal`.`t_menu`(`id`, `name`, `url`, `role`, `type`) VALUES (45, '最近一小时超时异常', '/sadmin/current-exception-list', 1, 0);
INSERT INTO `portal`.`t_menu`(`id`, `name`, `url`, `role`, `type`) VALUES (46, '最近一小时超时异常 - 查询', '/sadmin/query-exception-list', 1, 1);

-- 2022-08-10 主题包
CREATE TABLE `portal`.`t_page_theme_rel`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `page_id` int(11) NULL DEFAULT NULL COMMENT '对应首页ID',
  `page_theme_id` int(11) NULL DEFAULT NULL COMMENT '页面主题关联表表ID',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `page_id`(`page_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;
CREATE TABLE `portal`.`t_page_theme`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '主题包名称',
  `content` json NULL COMMENT '主题包配置内容，{vo:{},jo:{}} 分别对应前端使用和后端使用',
  `des` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '描述',
  `create_uid` int(11) NULL DEFAULT NULL COMMENT '创建者，系统主题包，uid=0',
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '主题包类型，0 系统， 1自定义等。。',
  `status` tinyint(1) NULL DEFAULT NULL COMMENT '该主题包是否被删除',
  `page_id` int(11) NULL DEFAULT NULL COMMENT '主题包所属pageId(网站首页)',
  `cover_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '封面',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `create_uid`(`create_uid`) USING BTREE,
  INDEX `page_id`(`page_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;
-- 2022-08-11 字段类型调整
ALTER TABLE `portal`.`t_page_theme` MODIFY type TINYINT(2) default 0 COMMENT '主题包类型';
-- 2022-08-12 2.0操作组件权限判断
ALTER TABLE `portal`.`t_role` MODIFY role int(2) COMMENT '角色 ， 11= 2.0可新增组件，12= 2.0可删除组件';
INSERT INTO `portal`.`t_role`( `uid`, `role`) VALUES ( 47218838, 11);
INSERT INTO `portal`.`t_role`( `uid`, `role`) VALUES ( 47218838, 12);
INSERT INTO `portal`.`t_role`( `uid`, `role`) VALUES ( 100753305, 11);
INSERT INTO `portal`.`t_role`( `uid`, `role`) VALUES ( 100753305, 12);
INSERT INTO `portal`.`t_role`( `uid`, `role`) VALUES ( 22531418, 11);
INSERT INTO `portal`.`t_role`(`uid`, `role`) VALUES ( 48187746, 11);
INSERT INTO `portal`.`t_role`( `uid`, `role`) VALUES ( 46884785 , 11);
INSERT INTO `portal`.`t_role`( `uid`, `role`) VALUES ( 46884785 , 12);
-- 2022-08-12 2.0的大屏和门户组件，周五 已执行
INSERT INTO `portal`.`t_module_type`(`id`, `name`, `pid`, `status`) VALUES (10017, '文本列表', 10000, 1);
INSERT INTO `portal`.`t_module_type`(`id`, `name`, `pid`, `status`) VALUES (10018, '图文列表', 10000, 1);
INSERT INTO `portal`.`t_module_type`(`id`, `name`, `pid`, `status`) VALUES (10019, '轮播', 10000, 1);
INSERT INTO `portal`.`t_module_type`(`id`, `name`, `pid`, `status`) VALUES (10020, '导航栏', 10000, 1);
INSERT INTO `portal`.`t_module_type`(`id`, `name`, `pid`, `status`) VALUES (10021, '搜索', 10000, 1);
INSERT INTO `portal`.`t_module_type`(`id`, `name`, `pid`, `status`) VALUES (10022, '文本列表', 50000, 1);
INSERT INTO `portal`.`t_module_type`(`id`, `name`, `pid`, `status`) VALUES (10023, '图文列表', 50000, 1);
INSERT INTO `portal`.`t_module_type`(`id`, `name`, `pid`, `status`) VALUES (10024, '轮播', 50000, 1);
INSERT INTO `portal`.`t_module_type`(`id`, `name`, `pid`, `status`) VALUES (10025, '导航栏', 50000, 1);
INSERT INTO `portal`.`t_module_type`(`id`, `name`, `pid`, `status`) VALUES (10026, '搜索', 50000, 1);
INSERT INTO `portal`.`t_module_type`(`id`, `name`, `pid`, `status`) VALUES (50000, '门户组件', 0, 1);
-- 2022-08-16 2.0的大屏和门户3级组件分类
INSERT INTO `portal`.`t_module_type`(`id`, `name`, `pid`, `status`) VALUES (10027, '大图展示', 10019, 1);
INSERT INTO `portal`.`t_module_type`(`id`, `name`, `pid`, `status`) VALUES (10028, '多图轮播', 10019, 1);
INSERT INTO `portal`.`t_module_type`(`id`, `name`, `pid`, `status`) VALUES (10029, '其他', 10019, 1);
INSERT INTO `portal`.`t_module_type`(`id`, `name`, `pid`, `status`) VALUES (10030, '图书推荐', 10018, 1);
INSERT INTO `portal`.`t_module_type`(`id`, `name`, `pid`, `status`) VALUES (10031, '课程展示', 10018, 1);
INSERT INTO `portal`.`t_module_type`(`id`, `name`, `pid`, `status`) VALUES (10032, '新闻通知', 10018, 1);
INSERT INTO `portal`.`t_module_type`(`id`, `name`, `pid`, `status`) VALUES (10033, '人员风采', 10018, 1);
INSERT INTO `portal`.`t_module_type`(`id`, `name`, `pid`, `status`) VALUES (10034, '快捷服务', 10018, 1);
INSERT INTO `portal`.`t_module_type`(`id`, `name`, `pid`, `status`) VALUES (10035, '其他', 10018, 1);
INSERT INTO `portal`.`t_module_type`(`id`, `name`, `pid`, `status`) VALUES (10036, '新闻资讯', 10017, 1);
INSERT INTO `portal`.`t_module_type`(`id`, `name`, `pid`, `status`) VALUES (10037, '其他', 10017, 1);

-- 2022-08-12 增加限流配置表
CREATE TABLE `t_limit_rule` (
                                `id` int(11) NOT NULL AUTO_INCREMENT,
                                `url` varchar(255) DEFAULT NULL COMMENT '资源路径',
                                `token_num` int(11) DEFAULT NULL COMMENT '令牌数量',
                                `times` int(11) DEFAULT NULL COMMENT '时间间隔',
                                `time_unit` tinyint(1) DEFAULT NULL COMMENT '时间单位',
                                PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=14 DEFAULT CHARSET=utf8mb4;

-- 2022-08-12 超管后台增加限流规则列表菜单
INSERT INTO `portal`.`t_menu`(`id`, `name`, `url`, `role`, `type`) VALUES (47, '限流规则列表', '/sadmin/limit-rule-list', 1, 0);
INSERT INTO `portal`.`t_menu`(`id`, `name`, `url`, `role`, `type`) VALUES (48, '限流规则列表 - 查询', '/sadmin/query-limit-rule', 1, 1);
INSERT INTO `portal`.`t_menu`(`id`, `name`, `url`, `role`, `type`) VALUES (49, '限流规则列表 - 保存', '/sadmin/save-limit-rule', 1, 1);
INSERT INTO `portal`.`t_menu`(`id`, `name`, `url`, `role`, `type`) VALUES (50, '限流规则列表 - 删除', '/sadmin/delete-limit-rule', 1, 1);

-- 2022-08-16 产品开通功能添加自定义域名字段
ALTER TABLE `portal`.`t_website_wfw_clone` ADD COLUMN `domain` varchar(20) NULL COMMENT '总馆网站域名';


-- 2022-08-17 新增超星域名
INSERT INTO `portal`.`t_cx_domain_configure`(`configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES ('http_zt', 'https://zt.chaoxing.com', NULL, NULL);
INSERT INTO `portal`.`t_cx_domain_configure`(`configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES ('https_zt', 'https://zt.chaoxing.com', NULL, NULL);
-- 2022-08-17 大屏和2.0搜索
INSERT INTO `portal`.`t_application`(`id`, `icon_url`, `type`, `data_url`, `clone_url`, `more_url`, `admin_url`, `status`, `create_time`, `source`, `rest_classify_url`, `rest_data_url`) VALUES (3002, '/engine2/assets/images/style/index/icon_search.png', 0, '/engine2/search/3002/div', '/engine2/search/clone', NULL, '', 0, NULL, 1, '', '');
INSERT INTO `portal`.`t_organization_application`(`wfwfid`, `application_id`, `name`, `status`, `create_time`, `website_id`, `page_id`) VALUES (-10, 3002, '大屏搜索', 1, '2022-08-11 11:36:07', -10, -10);
-- 2022-08-18 主动熔断接口列表
INSERT INTO `t_menu`( `name`, `url`, `role`) VALUES ('主动熔断接口列表', '/sadmin/hystrix/list', 1);

-- 2022-08-18 超管后台 2.0资源库菜单
INSERT INTO `portal`.`t_menu`(`name`, `url`, `role`, `type`) VALUES ('2.0 资源库分类列表', '/sadmin/resource-classify-list', 1, 0);
INSERT INTO `portal`.`t_menu`(`name`, `url`, `role`, `type`) VALUES ('2.0 资源库列表', '/sadmin/resource-library-list', 1, 0);
-- 2022-08-19 长辈版插件 镜像忽略此SQL
INSERT INTO `portal`.`t_website_plugs`(`website_id`, `type`, `plugs`, `page_ids`, `app_id`, `wfwfid`, `type_name`) VALUES (194287, 10, '<script type=\"text/javascript\" src=\"/assets/plugs/194287.js\"></script>', NULL, NULL, NULL, NULL);
-- 2022-08-24 2.0webjson修正  执行时间较长
UPDATE `portal`.t_web_json SET content = REPLACE(t_web_json.content,'2304||12rem','1920||10rem');

-- 2022-08-24 管理后台审核菜单和自定义菜单权限控制
CREATE TABLE `t_user_website_menu` (
                                       `id` int(11) NOT NULL AUTO_INCREMENT,
                                       `website_id` int(11) DEFAULT NULL COMMENT '网站id',
                                       `uid` int(11) DEFAULT NULL COMMENT '用户uid',
                                       `menus` varchar(500) DEFAULT NULL COMMENT '菜单ids',
                                       PRIMARY KEY (`id`),
                                       KEY `idx_website_id` (`website_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COMMENT='网站用户菜单权限';

-- 2022-08-26 字段表增加自定义超时异常触发次数
INSERT INTO `portal`.`t_dict`(`id`, `code`, `name`, `value`, `create_time`) VALUES (3, 'time_out_num', '超时异常触发次数', '10', '2022-08-26 10:53:46');
-- 2022-08-30 门户通用md5加密密钥
INSERT INTO `portal`.`t_dict`( `code`, `name`, `value`) VALUES ( 'mh_md5_key', '门户通用md5加密密钥', 'i[doiw09$@kd[po0');
-- 2022-08-30 2.0大屏统一走page-v21入口
UPDATE `portal`.t_page SET url = REPLACE(t_page.url,'/page-v2/#/','/page-v21/#/') WHERE template = 'template_2_bigScreen';
UPDATE `portal`.t_page SET url = REPLACE(t_page.url,'/page-v2/#/','/page-v21/#/') WHERE template = 'bigScreenList';
UPDATE `portal`.t_website SET product_type = 1019 WHERE home_page_id in (SELECT id FROM t_page WHERE template = 'bigScreenList');
UPDATE `portal`.t_website SET product_type = 1019 WHERE home_page_id in (SELECT id FROM t_page WHERE template = 'template_2_bigScreen');

-- 2022-09-13 查询数据公钥
ALTER TABLE `portal`.`t_page`
    ADD COLUMN `public_id` varchar(36)  DEFAULT '' COMMENT '查询公钥';
-- ALTER TABLE `portal`.`t_page` ADD UNIQUE ( `public_id` );
-- 2022-09-13 查询数据公钥
ALTER TABLE `portal`.`t_web_json`
    ADD COLUMN `public_id` varchar(36)  DEFAULT '' COMMENT '查询公钥';
-- ALTER TABLE `portal`.`t_web_json` ADD UNIQUE ( `public_id` );

-- 2022-09-13 查询数据公钥
ALTER TABLE `portal`.`t_website`
    ADD COLUMN `public_id` varchar(36)  DEFAULT '' COMMENT '查询公钥';
-- ALTER TABLE `portal`.`t_website` ADD UNIQUE ( `public_id` );

-- 2022-09-14
CREATE TABLE `portal`.`t_website_wfw_branch_clone`  (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `wfwfid` int(10) NULL DEFAULT NULL COMMENT '网站wfwfid',
            `group_id` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '组织ID',
            `domain` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '总馆网站域名',
            `create_uid` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
            `error_message` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '处理结果',
            `status` int(1) NULL DEFAULT NULL COMMENT '0-初始化\r\n1-克隆中\r\n2-克隆成功\r\n3-克隆失败',
            `create_time` datetime(0) NULL DEFAULT NULL,
            `update_time` datetime(0) NULL DEFAULT NULL,
            `website_id` int(11) NULL DEFAULT NULL COMMENT '网站ID',
            `website_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '网站域名',
            `form_id` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '表单ID',
            `form_data_id` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '表单数据行ID',
              PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 16 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '产品开通-分馆开通' ROW_FORMAT = Dynamic;

CREATE TABLE `portal`.`t_website_wfw_branch_clone_detail`  (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `pid` int(11) NULL DEFAULT NULL COMMENT '复制ID',
              `website_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '克隆后网站名称',
              `website_id` int(20) NULL DEFAULT NULL COMMENT '克隆的源网站ID',
              `target_website_id` int(15) NULL DEFAULT NULL COMMENT '克隆后的网站ID',
              `create_uid` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
              `wfwfid` int(10) NULL DEFAULT NULL COMMENT '网站wfwfid',
              `status` int(1) NULL DEFAULT NULL COMMENT '0-微服务克隆中，1-微服务克隆成功 2-网站克隆成功 -1克隆失败，3-应用数据替换成功，-3应用数据替换失败，4-复制完成',
              `create_time` datetime(0) NULL DEFAULT NULL,
              `update_time` datetime(0) NULL DEFAULT NULL,
              PRIMARY KEY (`id`) USING BTREE,
              INDEX `index_website_wfw_clone_detail_pid`(`pid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '产品复制-明细表' ROW_FORMAT = Dynamic;

-- 2022-09-21 t_organization_application app对应公钥
ALTER TABLE `portal`.`t_application`
    ADD COLUMN `public_id` varchar(36)  DEFAULT '' COMMENT '查询对应数据公钥';

-- 2022-09-22 资源库增加其他配置字段
ALTER TABLE `portal`.`t_resource_library` ADD COLUMN `other_config` varchar(255) DEFAULT NULL COMMENT '其他配置：颜色、比例、风格、类型';

-- 2022-09-23 publicId相关索引
ALTER TABLE `portal`.`t_page` ADD INDEX `index_public_id`(`public_id`) USING BTREE;
ALTER TABLE `portal`.`t_web_json` ADD INDEX `index_public_id`(`public_id`) USING BTREE;
ALTER TABLE `portal`.`t_website` ADD INDEX `index_public_id`(`public_id`) USING BTREE;
ALTER TABLE `portal`.`t_application` ADD INDEX `index_public_id`(`public_id`) USING BTREE;

-- 2022-10-08 站群后台补全部分功能
INSERT INTO `portal`.t_sys_menu (id,pid,`name`,intro,url,page,type) VALUES(300,40,'数据概况',null,'/data-count/data-overview?websiteId={websiteId}',40,1);
INSERT INTO `portal`.t_sys_menu (id,pid,`name`,intro,url,page,type) VALUES(24,2,'镜像下载',null,'/export-data/{websiteId}',20,1);
INSERT INTO `portal`.t_sys_menu (id,pid,`name`,intro,url,page,type) VALUES(334,22,'死链检测',null,'/sadmin/dead-link/{websiteId}',20,1);

-- 2022-10-09 单位认证配置
INSERT INTO `portal`.`t_menu`(`name`, `url`, `role`, `type`) VALUES ('单位认证配置', '/sso/admin/org-login-config', 1, 0);


-- 2022-10-11 屏蔽新建网站单位列表
CREATE TABLE `portal`.`t_shield` (
                            `id` int(11) NOT NULL AUTO_INCREMENT,
                            `wfwfid` int(11) DEFAULT NULL,
                            `name` varchar(255) DEFAULT NULL COMMENT '单位名称',
                            PRIMARY KEY (`id`),
                            UNIQUE KEY `idx_shield_wfwfid` (`wfwfid`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=25 DEFAULT CHARSET=utf8mb4 COMMENT='屏蔽新建网站单位列表';

INSERT INTO `portal`.`t_menu`(`name`, `url`, `role`, `type`) VALUES ('屏蔽新建网站单位列表', '/sadmin/shield-list', 1, 0);

-- 2022-10-14 文化馆开通状态记录
ALTER TABLE `portal`.`t_website_wfw_clone_detail`
    ADD COLUMN `deal_status` int(1) NULL DEFAULT NULL COMMENT '处理逻辑：0-待处理 1-处理中';

-- 2022-10-17 删除engine1的chart样式(都是测试单位下的数据)
DELETE FROM portal.t_application WHERE data_url like '/engine/chart%';
-- 2022-10-17 engine1的搜索直接迁移到engine2
UPDATE `portal`.t_application SET `data_url`=replace(`data_url`, '/engine/search', '/engine2/search'), `clone_url`=replace(`clone_url`, '/engine/search', '/engine2/search'),`more_url`=replace(`more_url`, '/engine/search', '/engine2/search'),`admin_url`=replace(`admin_url`, '/engine/search/admin/', '/engine2/admin/search/') WHERE data_url like '/engine/search%';

-- 2022-10-17 engine1的图文、图标、轮播、多图、文本等迁移到engine2
UPDATE portal.t_application SET `data_url`=replace(`data_url`, '/engine/icon/', '/engine2/e1-icon/'), `clone_url`=replace(`clone_url`, '/engine/icon/', '/engine2/e1-icon/'),`more_url`=replace(`more_url`, '/engine/icon/', '/engine2/e1-icon/'),`admin_url`=replace(`admin_url`, '/engine/icon/admin/', '/engine2/e1-icon/admin/') WHERE data_url like '/engine/icon/%';
UPDATE portal.t_application SET `data_url`=replace(`data_url`, '/engine/graphic/', '/engine2/e1-graphic/'), `clone_url`=replace(`clone_url`, '/engine/graphic/', '/engine2/e1-graphic/'), `more_url`=replace(`more_url`, '/engine/graphic/', '/engine2/e1-graphic/'), `admin_url`=replace(`admin_url`, '/engine/graphic/admin/', '/engine2/e1-graphic/admin/') WHERE data_url like '/engine/graphic/%';
UPDATE portal.t_application SET `data_url`=replace(`data_url`, '/engine/text/', '/engine2/e1-text/'), `clone_url`=replace(`clone_url`, '/engine/text/', '/engine2/e1-text/'), `more_url`=replace(`more_url`, '/engine/text/', '/engine2/e1-text/'), `admin_url`=replace(`admin_url`, '/engine/text/admin/', '/engine2/e1-text/admin/') WHERE data_url like '/engine/text/%';
UPDATE portal.t_application SET `data_url`=replace(`data_url`, '/engine/image/', '/engine2/e1-image/'), `clone_url`=replace(`clone_url`, '/engine/image/', '/engine2/e1-image/'), `more_url`=replace(`more_url`, '/engine/image/', '/engine2/e1-image/'), `admin_url`=replace(`admin_url`, '/engine/image/admin/', '/engine2/e1-image/admin/') WHERE data_url like '/engine/image/%';
UPDATE portal.t_application SET `data_url`=replace(`data_url`, '/engine/slide/', '/engine2/e1-slide/'), `clone_url`=replace(`clone_url`, '/engine/slide/', '/engine2/e1-graphic/'), `more_url`=replace(`more_url`, '/engine/slide/', '/engine2/e1-slide/'), `admin_url`=replace(`admin_url`, '/engine/slide/admin/', '/engine2/e1-slide/admin/') WHERE data_url like '/engine/slide/%';
UPDATE portal.t_application SET `icon_url`=replace(`icon_url`, '/engine/assets/images/', '/engine2/assets/images/') WHERE icon_url like '/engine/assets/images/%';

-- 2022-10-17 engine1的图文、图标、轮播、多图、文本等迁移到engine2
UPDATE portal.t_engine SET `icon_url`=replace(`icon_url`, '/engine/assets/images/', '/engine2/assets/images/') WHERE icon_url like '/engine/assets/images/%';
-- 2022-10-19  2.0 登录框
INSERT INTO `portal`.`t_module_type`(`id`, `name`, `pid`, `status`) VALUES (50007, '登录框', 50000, 1);

-- 2022-10-19 超管后台接口权限
INSERT INTO `portal`.`t_menu`(`name`, `url`, `role`, `type`) VALUES ('资源库分类列表 - 查询', '/sadmin/query-resource-classify', 3, 1);
INSERT INTO `portal`.`t_menu`(`name`, `url`, `role`, `type`) VALUES ('资源库分类列表 - 保存', '/sadmin/save-resource-classify', 3, 1);
INSERT INTO `portal`.`t_menu`(`name`, `url`, `role`, `type`) VALUES ('资源库分类列表 - 查询子分类', '/sadmin/query-sub-classify', 3, 1);
INSERT INTO `portal`.`t_menu`(`name`, `url`, `role`, `type`) VALUES ('资源库分类列表 - 查询该一级分类下不存在的二级分类', '/sadmin/query-second-classify', 3, 1);
INSERT INTO `portal`.`t_menu`(`name`, `url`, `role`, `type`) VALUES ('资源库分类列表 - 给一级分类添加子分类', '/sadmin/add-sub-classify', 3, 1);
INSERT INTO `portal`.`t_menu`(`name`, `url`, `role`, `type`) VALUES ('资源库分类列表 - 删除一级分类下的子分类', '/sadmin/delete-sub-classify', 3, 1);
INSERT INTO `portal`.`t_menu`(`name`, `url`, `role`, `type`) VALUES ('资源库分类列表 - 删除一级分类', '/sadmin/delete-classify', 3, 1);
INSERT INTO `portal`.`t_menu`(`name`, `url`, `role`, `type`) VALUES ('查询资源', '/sadmin/query-by-classify', 3, 1);
INSERT INTO `portal`.`t_menu`(`name`, `url`, `role`, `type`) VALUES ('查询资源库', '/sadmin/query-resource-library', 3, 1);
INSERT INTO `portal`.`t_menu`(`name`, `url`, `role`, `type`) VALUES ('资源库 - 保存', '/sadmin/save-resource-library', 3, 1);
INSERT INTO `portal`.`t_menu`(`name`, `url`, `role`, `type`) VALUES ('资源库 - 删除', '/sadmin/delete-resource-library', 3, 1);
INSERT INTO `portal`.`t_menu`(`name`, `url`, `role`, `type`) VALUES ('图片库上传', '/sadmin/upload-img', 3, 1);
INSERT INTO `portal`.`t_menu`(`name`, `url`, `role`, `type`) VALUES ('查询未关联的资源', '/sadmin/query-no-relation-resource', 3, 1);
INSERT INTO `portal`.`t_menu`(`name`, `url`, `role`, `type`) VALUES ('add-relation-resouce', '/sadmin/add-relation-resouce', 3, 1);
INSERT INTO `portal`.`t_menu`(`name`, `url`, `role`, `type`) VALUES ('delete-relation-resouce', '/sadmin/delete-relation-resouce', 3, 1);
INSERT INTO `portal`.`t_menu`(`name`, `url`, `role`, `type`) VALUES ('屏蔽新建网站单位列表', '/sadmin/query-shield', 3, 1);
INSERT INTO `portal`.`t_menu`(`name`, `url`, `role`, `type`) VALUES ('屏蔽新建网站单位列表 - 新增', '/sadmin/shield-add', 3, 1);
INSERT INTO `portal`.`t_menu`(`name`, `url`, `role`, `type`) VALUES ('屏蔽新建网站单位列表 - 修改', '/sadmin/shield-update', 3, 1);
INSERT INTO `portal`.`t_menu`(`name`, `url`, `role`, `type`) VALUES ('屏蔽新建网站单位列表 - 删除', '/sadmin/shield-delete', 3, 1);

-- 2022-10-21 搜索列表迁移
UPDATE portal.t_application SET `admin_url`=replace(`admin_url`, '/engine/', '/engine2/'),`clone_url`=replace(`clone_url`, '/engine/', '/engine2/') WHERE admin_url like '/engine/%';
UPDATE portal.t_engine SET `admin_url`=replace(`admin_url`, '/engine/', '/engine2/'),`clone_url`=replace(`clone_url`, '/engine/', '/engine2/') WHERE admin_url like '/engine/%';
-- 2022-10-25 2.0移动端组件类型
INSERT INTO `portal`.`t_module_type`(`id`, `name`, `pid`, `status`) VALUES (100000, '移动端组件', 0, 1);
INSERT INTO `portal`.`t_module_type`( `name`, `pid`, `status`) VALUES ('文本列表', 100000, 1);
INSERT INTO `portal`.`t_module_type`( `name`, `pid`, `status`) VALUES ('图文列表', 100000, 1);
INSERT INTO `portal`.`t_module_type`( `name`, `pid`, `status`) VALUES ('轮播', 100000, 1);
INSERT INTO `portal`.`t_module_type`( `name`, `pid`, `status`) VALUES ('导航栏', 100000, 1);
INSERT INTO `portal`.`t_module_type`( `name`, `pid`, `status`) VALUES ('搜索', 100000, 1);
INSERT INTO `portal`.`t_module_type`( `name`, `pid`, `status`) VALUES ('登录框', 100000, 1);

-- 2022-10-27 2.0组件类型管理
INSERT INTO `portal`.`t_menu`(`name`, `url`, `role`, `type`) VALUES ('2.0大屏组件类型列表', '/sadmin/module-type-list', 1, 0);

-- 2022-11-09
ALTER TABLE `portal`.`t_website_wfw_clone` ADD COLUMN `product_name` varchar(50) NULL COMMENT '产品名称';
ALTER TABLE `portal`.`t_website_wfw_clone` ADD COLUMN `org_name` varchar(50) NULL COMMENT '网站名称';

-- 2022-11-09 公图自定义后台数据类型
ALTER TABLE `portal`.`t_website_pv_custom_data` ADD COLUMN `type` INT DEFAULT 0 COMMENT '数据类型 0->普通数据 1->基线数据';
ALTER TABLE `portal`.`t_website_uv_custom_data` ADD COLUMN `type` INT DEFAULT 0 COMMENT '数据类型 0->普通数据 1->基线数据';

-- 2022-11-15 2.0组件分类path
ALTER TABLE `portal`.t_module_content ADD path VARCHAR(255) null COMMENT '分类层级path';
-- 2022-11-28 内网ip
INSERT INTO `portal`.`t_dict`( `code`, `name`, `value`, `create_time`) VALUES ('inner-ips', '内网ip段', '********-************#***********-***************#127.0.0.1#**************', '2022-11-28 11:23:18');

-- 2022-12-09 主题包类型字段长度修改
alter table t_page_theme modify type int(11) default null comment '模板类型 或 父级关系';
-- 2022-12-13 白名单
INSERT INTO `portal`.`t_domain_blank`(`website_id`, `domain`, `status`) VALUES ( 1, 'mfindnxqtsg.libsp.cn', 1);

-- 2022-12-27 资源库增加public_id
ALTER TABLE `portal`.`t_resource_library` ADD COLUMN `public_id` varchar(36) DEFAULT NULL;

-- 2023-02-01 超管后台2.0组件管理
INSERT INTO `portal`.`t_menu`(`name`, `url`, `role`, `type`) VALUES ('2.0组件列表', '/sadmin/module-list', 2, 0);
INSERT INTO `portal`.`t_menu`(`name`, `url`, `role`, `type`) VALUES ('2.0组件列表-查询', '/sadmin/module/list', 2, 1);
INSERT INTO `portal`.`t_menu`(`name`, `url`, `role`, `type`) VALUES ('2.0组件列表-修改', '/sadmin/module/update', 2, 1);
INSERT INTO `portal`.`t_menu`(`name`, `url`, `role`, `type`) VALUES ('2.0组件列表-删除', '/sadmin/module/delete', 2, 1);
INSERT INTO `portal`.`t_menu`(`name`, `url`, `role`, `type`) VALUES ('2.0组件类型树形结构', '/sadmin/module-type/tree', 2, 1);
INSERT INTO `portal`.`t_menu`(`name`, `url`, `role`, `type`) VALUES ('上传封面', '/sadmin/upload-cover', 3, 1);


-- 2023-02-06 超管后台2.0组件管理
INSERT INTO `portal`.`t_menu`(`name`, `url`, `role`, `type`) VALUES ('系统默认组件管理列表', '/sadmin/top-module-content-list', 2, 0);
INSERT INTO `portal`.`t_menu`(`name`, `url`, `role`, `type`) VALUES ('系统默认组件管理列表-查询', '/sadmin/top-module-content-list/list', 2, 1);
INSERT INTO `portal`.`t_menu`(`name`, `url`, `role`, `type`) VALUES ('系统默认组件管理列表-修改', '/sadmin/top-module-content-list/update', 2, 1);
INSERT INTO `portal`.`t_menu`(`name`, `url`, `role`, `type`) VALUES ('系统默认组件管理列表-新增', '/sadmin/top-module-content-list/add', 2, 1);
INSERT INTO `portal`.`t_menu`(`name`, `url`, `role`, `type`) VALUES ('系统默认组件管理列表-删除', '/sadmin/top-module-content-list/delete', 2, 1);
-- 2023-02-08 组件publicId
-- 刷数据据 http://127.0.0.1:8101/internal/set-module-type
-- http://127.0.0.1:8101/internal/set-module-content
ALTER TABLE `portal`.t_module_content ADD public_id VARCHAR(255) DEFAULT '' COMMENT '';
ALTER TABLE `portal`.t_module_type ADD public_id VARCHAR(255) DEFAULT '' COMMENT '';
-- 2023-02-08 分类下默认置顶组件表
DROP TABLE IF EXISTS `portal`.`t_top_module_content`;
CREATE TABLE `portal`.`t_top_module_content`  (
                                         `id` int(11) NOT NULL AUTO_INCREMENT,
                                         `module_content_id` int(11) NOT NULL COMMENT '组件ID，可能为空',
                                         `module_type_id` int(11) NOT NULL COMMENT '分类ID，可能为空',
                                         `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '置顶后的组件名称',
                                         `seq` int(11) NULL DEFAULT NULL COMMENT '排序',
                                         `status` tinyint(2) NULL DEFAULT NULL COMMENT '是否启用',
                                         `pid` int(11) NULL DEFAULT 0 COMMENT '选中分类的上一级pid，可能与module_type_id一样',
                                         `top_pid` int(11) NULL DEFAULT NULL COMMENT '顶级pid，可能与module_type_id一样',
                                         PRIMARY KEY (`id`) USING BTREE,
                                         INDEX `index_module_content_id`(`module_content_id`) USING BTREE,
                                         INDEX `index_module_type_id`(`module_type_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 10 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- 2023-02-10 超管后台 网站管理-普通版
INSERT INTO `portal`.`t_menu`(`name`, `url`, `role`, `type`) VALUES ('获取网站列表-普通版', '/sadmin/websites-normal', 4, 1);
INSERT INTO `portal`.`t_menu`(`name`, `url`, `role`, `type`) VALUES ('修改网站列表-普通版', '/sadmin/update-website-normal', 4, 1);
INSERT INTO `portal`.`t_menu`(`name`, `url`, `role`, `type`) VALUES ('网站列表 - 网站基本信息管理-普通版', '/sadmin/website-list-normal', 4, 0);

-- 2023-02-13 组件移动端json
ALTER TABLE `portal`.t_module_content ADD app_content mediumtext COMMENT '组件移动端json';
-- 2023-02-14 网站类型 刷类型数据
ALTER TABLE `portal`.t_website ADD web_type TINYINT(2) DEFAULT '1' COMMENT '网站类型	1 1.0, 2 2.0,3 大屏';
UPDATE `portal`.t_website SET web_type = 2 WHERE product_type >999 and product_type < 1011;
UPDATE `portal`.t_website SET web_type = 3 WHERE product_type >1010 and product_type < 1021;
-- 2023-02-14 网站主题包使用类型字段
ALTER TABLE `portal`.t_website ADD page_theme_type TINYINT(1) DEFAULT '2' COMMENT '网站使用主题类型 0 默认不用主题包	,1 使用自己设置的主题包	,2 使用源网站主题包（上线后 默认为2）';

-- 2023-02-15 角色权限
CREATE TABLE `portal`.`t_mh_role` (
                             `id` int(11) NOT NULL AUTO_INCREMENT,
                             `role_name` varchar(255) NOT NULL,
                             `website_id` int(11) NOT NULL,
                             `status` tinyint(1) DEFAULT '1',
                             PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;

CREATE TABLE `portal`.`t_mh_role_uid` (
                                 `id` int(11) NOT NULL AUTO_INCREMENT,
                                 `mh_role_id` int(11) NOT NULL,
                                 `uid` int(11) NOT NULL,
                                 `real_name` varchar(255) DEFAULT NULL,
                                 `status` tinyint(1) DEFAULT '1',
                                 PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;

ALTER TABLE `portal`.`t_user_app_permission_new`
    ADD COLUMN `mh_role_id` int(11) NULL AFTER `type`;


-- 2023-02-27 定时任务配置信息
ALTER TABLE `portal`.`t_job` ADD COLUMN `config` varchar(255) NULL COMMENT '配置信息';

-- 2023-02-27 主题包逻辑优化
ALTER TABLE `portal`.`t_page_theme_rel` ADD COLUMN `can_choose` TINYINT(1) DEFAULT 0 COMMENT '可被选择的关联关系  0  , 1 是主题包自带的，可被选择';
alter table `portal`.t_page_theme_rel drop index page_id;
ALTER TABLE `portal`.t_page_theme_rel ADD INDEX `idx_page_theme_rel_page_id`(`page_id`) USING BTREE;
INSERT into `portal`.t_page_theme_rel  (page_id,page_theme_id,can_choose)  (SELECT page_id , id, 1 FROM `portal`.t_page_theme WHERE `status` = 1);
alter table `portal`.t_page_theme drop index page_id;
ALTER TABLE `portal`.t_page_theme MODIFY COLUMN page_id int(11) DEFAULT NULL COMMENT '废弃字段';

-- 2023-02-28 公共图书馆自定义数据后台配置功能
ALTER TABLE `portal`.`t_public_library_admin` ADD COLUMN `create_time` datetime(0) NULL;

INSERT INTO `portal`.`t_menu`(`name`, `url`, `role`, `type`) VALUES ('公图自定义数据后台配置', '/sadmin/website-uv-config', 4, 0);
INSERT INTO `portal`.`t_menu`(`name`, `url`, `role`, `type`) VALUES ('公图自定义数据后台配置-列表', '/sadmin/website-uv-config/list', 4, 1);
INSERT INTO `portal`.`t_menu`(`name`, `url`, `role`, `type`) VALUES ('公图自定义数据后台配置-新增', '/sadmin/website-uv-config/add', 4, 1);
INSERT INTO `portal`.`t_menu`(`name`, `url`, `role`, `type`) VALUES ('公图自定义数据后台配置-删除', '/sadmin/website-uv-config/delete', 4, 1);
-- 2023-03-01 2.0列表详情页面，路径参数存储表
CREATE TABLE `portal`.`t_subpage_link`  (
                                   `id` int(11) NOT NULL AUTO_INCREMENT,
                                   `uuid` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'key',
                                   `url` varchar(760) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'key对应的链接地址',
                                   `encrypted_url` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'urlMD5加密后字符串',
                                   `page_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '2.0 列表或详情页对应的pageId',
                                   PRIMARY KEY (`id`) USING BTREE,
                                   UNIQUE INDEX `index_uuid`(`uuid`) USING BTREE,
                                   INDEX `index_encrypted_url`(`encrypted_url`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '2.0列表详情页面，路径参数存储表' ROW_FORMAT = Dynamic;

-- 2023-03-02 2.0 列表页详情页名称修正(显示目前只有两条数据)
UPDATE `portal`.t_page set name ='列表页' WHERE template = 'template_2_sub_m';
UPDATE `portal`.t_page set name ='详情页' WHERE template = 'template_2_sub_d';
-- 2023-03-06  2.0大屏组件类型列表 - 清除缓存
INSERT INTO `portal`.`t_menu`(`name`, `url`, `role`, `type`) VALUES ('2.0大屏组件类型列表 - 清除缓存', '/sadmin/module-type/evict-cache', 2, 1);

-- 2023-03-08  2.0子页面配置关系
ALTER TABLE `portal`.t_web_json ADD sub_pages mediumtext COMMENT '2.0子页面配置关系';
ALTER TABLE `portal`.t_web_json ADD app_sub_pages mediumtext COMMENT '2.0移动端子页面配置关系';
-- 2023-03-09  列表、详情模板类型错误
UPDATE `portal`.t_page  SET template ='template_2_sub_m' WHERE template ='listPage';
UPDATE `portal`.t_page  SET template ='template_2_sub_d' WHERE template ='detailPage';
-- 2023-03-16  字段表字段长度
alter table `portal`.t_dict modify code VARCHAR(255) default '' null comment '';
INSERT INTO `portal`.`t_dict`(`code`, `name`, `value`, `create_time`) VALUES ('/outer/api/website/permission/del', '资源库使用的删除网站管理权限', '75fe879569284df8a1431b5668927b30', '2023-03-16 10:25:18');

-- 2023-03-28 网站插件操作权限改为4
INSERT INTO `portal`.`t_menu`(`name`, `url`, `role`, `type`) VALUES ('网站插件-资源库', '/sadmin/website-plugs-with-t', 4, 0);
UPDATE `portal`.`t_menu` SET role = 4 where url = '/sadmin/get-website-plugs';
UPDATE `portal`.`t_menu` SET role = 4 where url = '/sadmin/save-website-plugs';

-- 2323-03-06 产品工厂克隆key
INSERT INTO `portal`.`t_dict`(`code`, `name`, `value`) VALUES ('product_factory_key', '产品工厂克隆key', 'TqhBWxv7yeRD4frRUFiA58k2');



-- 2023-04-19 大屏开通
ALTER TABLE `portal`.`t_website_wfw_clone` ADD COLUMN `dsj_website_id` varchar(100) NULL COMMENT '大屏网站ID';

-- 2023-04-25 模板分类管理功能
INSERT INTO `portal`.`t_menu`(`name`, `url`, `role`, `type`) VALUES ('模板分类管理', '/sadmin/template-type-index', 2, 0);
INSERT INTO `portal`.`t_menu`(`name`, `url`, `role`, `type`) VALUES ('模板分类查询', '/sadmin/template-type-list', 2, 1);
INSERT INTO `portal`.`t_menu`(`name`, `url`, `role`, `type`) VALUES ('模板分类保存', '/sadmin/template-type-save', 2, 1);
INSERT INTO `portal`.`t_menu`(`name`, `url`, `role`, `type`) VALUES ('模板分类删除', '/sadmin/template-type-delete', 2, 1);

-- 超管后台增加通用应用管理功能
INSERT INTO `portal`.`t_menu`(`name`, `url`, `role`, `type`) VALUES ('通用应用管理', '/sadmin/default-app-index', 2, 0);
INSERT INTO `portal`.`t_menu`(`name`, `url`, `role`, `type`) VALUES ('通用应用管理 - 列表', '/sadmin/default-app-list', 2, 1);
INSERT INTO `portal`.`t_menu`(`name`, `url`, `role`, `type`) VALUES ('通用应用管理 - 添加', '/sadmin/default-app-add', 2, 1);
INSERT INTO `portal`.`t_menu`(`name`, `url`, `role`, `type`) VALUES ('通用应用管理 - 修改', '/sadmin/default-app-edit', 2, 1);

-- 超管后台增加网站模板管理功能
INSERT INTO `portal`.`t_menu`(`name`, `url`, `role`, `type`) VALUES ('网站模板管理', '/sadmin/website-template-index', 2, 0);
INSERT INTO `portal`.`t_menu`(`name`, `url`, `role`, `type`) VALUES ('网站模板管理 - 模板分类树形结构', '/sadmin/website-template-type-tree', 2, 1);
INSERT INTO `portal`.`t_menu`(`name`, `url`, `role`, `type`) VALUES ('网站模板管理 - 模板列表', '/sadmin/website-template-list', 2, 1);
INSERT INTO `portal`.`t_menu`(`name`, `url`, `role`, `type`) VALUES ('网站模板管理 - 上传模板', '/template/admin/ups-api', 2, 1);
INSERT INTO `portal`.`t_menu`(`name`, `url`, `role`, `type`) VALUES ('网站模板管理 - 编辑模板', '/template/admin/ups-edit-api', 2, 1);

-- 2023-05-15 镜像支持单独表单接口授权key，为空则使用线上key
INSERT INTO `portal`.`t_dict`(`code`, `name`, `value`, `create_time`) VALUES ('mh_forms_key', '表单接口授权key', '', '2023-05-15 10:47:59');

-- 2023-05-17 网站配置表
DROP TABLE IF EXISTS `portal`.`t_website_config`;
CREATE TABLE `portal`.`t_website_config`  (
                                   `id` int(1) NOT NULL AUTO_INCREMENT,
                                   `website_id` int(1) NULL DEFAULT NULL,
                                   `custom_domain_editor` tinyint(1) NULL DEFAULT NULL COMMENT '开启笔记编辑器自定义域名 0->否 1->是',
                                   PRIMARY KEY (`id`) USING BTREE,
                                   INDEX `index_website_id`(`website_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- 2023-05-31 网站配置表 是否允许数据被跨单位查询
ALTER TABLE t_website_config ADD open_query TINYINT default 0 COMMENT '是否允许被跨单位查询网站数据 0->不允许 1->允许';
-- 2023-06-05 网站单位地址配置表
CREATE TABLE `t_website_wfwfid_uri_setting`  (
                                                 `id` int(11) NOT NULL AUTO_INCREMENT,
                                                 `public_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'website的publicId',
                                                 `wfwfid` int(11) NULL DEFAULT NULL COMMENT '虚拟的单位ID，不一定是wfwfid',
                                                 `uri` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '对应跳转地址',
                                                 `des` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                                                 `updated_time` timestamp(0) NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0),
                                                 `created_uid` int(11) NULL DEFAULT NULL,
                                                 `target` tinyint(1) NULL DEFAULT 1 COMMENT '是否新开地址',
                                                 PRIMARY KEY (`id`) USING BTREE,
                                                 INDEX `idx_web_wfw_uid_public_id`(`public_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;
-- 2023-06-14 组件管理权限
UPDATE `portal`.`t_menu` set `role` = 5 WHERE `name` = '2.0大屏组件类型列表 - 新增';
UPDATE `portal`.`t_menu` set `role` = 5 WHERE `name` = '2.0大屏组件类型列表 - 修改';
UPDATE `portal`.`t_menu` set `role` = 6 WHERE `name` = '2.0大屏组件类型列表 - 删除';
UPDATE `portal`.`t_role` set  `role` = 6 WHERE role = 12;
UPDATE `portal`.`t_role` set  `role` = 5 WHERE role = 11;
-- 2023-06-14 webjson关联表
CREATE TABLE `t_web_json_rel`  (
                                   `id` int(11) NOT NULL,
                                   `sub_pages` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL,
                                   `app_sub_pages` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL,
                                   PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;



-- 2023-06-19 使用业务密码登录
ALTER TABLE `portal`.`t_website_config`
    ADD COLUMN `unit_pwd_login` tinyint(1) NULL DEFAULT 0 COMMENT '使用业务密码登录' AFTER `open_query`;

-- 2023-06-20
CREATE TABLE `t_origin_rel` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `target_id` int(11) DEFAULT NULL COMMENT '目标新ID',
        `origin_id` int(11) DEFAULT NULL COMMENT '克隆原ID',
        `type` int(2) DEFAULT NULL COMMENT '类型：1-引擎实例',
        `wfwfid` int(10) DEFAULT NULL COMMENT '微服务fid',
        PRIMARY KEY (`id`),
        KEY `index_origin_rel_1` (`target_id`) USING BTREE,
        KEY `index_origin_rel_2` (`origin_id`) USING BTREE,
        KEY `index_origin_rel_3` (`type`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='克隆新老ID映射关系表';
-- 2023-06-21 主动重试克隆接口限流
INSERT INTO `portal`.`t_limit_rule`(`url`, `token_num`, `times`, `time_unit`) VALUES ('/web-others/{websiteId}/re-try-clone', 10, 60, 0);
-- 2023-06-21 网站异步克隆记录表
DROP TABLE IF EXISTS `t_web_clone_task`;
CREATE TABLE `t_web_clone_task`  (
                                     `id` int(11) NOT NULL AUTO_INCREMENT,
                                     `s_web_id` int(11) NULL DEFAULT NULL COMMENT '源websiteId',
                                     `t_web_id` int(11) NOT NULL COMMENT '目标websiteId',
                                     `status` tinyint(1) NULL DEFAULT NULL COMMENT '-3异常，-2执行中，0未执行，1已完成',
                                     `e_des` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '异常信息记录',
                                     `call_uri` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '调用克隆接口时，传入。第三方通知克隆网站完成，回调地址，接收3个参数，sign，websiteId，success(0,1)',
                                     `created_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP,
                                     `updated_time` datetime(0) NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP(0),
                                     `add_wfw_admin` tinyint(1) NULL DEFAULT 0 COMMENT '是否添加微服务管理员到网站超管权限',
                                     `s_page_id` int(11) NULL DEFAULT NULL COMMENT '用某网站的头底，替换克隆目标网站的头底',
                                     `h_page_id` int(11) NULL DEFAULT NULL COMMENT '指定该pageId为网站首页',
                                     `product_id` int(11) NULL DEFAULT NULL COMMENT '产品类型',
                                     `hide` tinyint(1) NULL DEFAULT NULL COMMENT '是否隐藏，1是，0不是',
                                     `cover` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                                     `hd` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '是否来自活动，1是，0不是',
                                     `content` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '调用engine2 处理应用数据 /engine2/api/application/discover-data?wfwfid=',
                                     `website_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '网站首页名称',
                                     `logout_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                                     `login_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                                     `re_try_count` tinyint(1) NULL DEFAULT NULL COMMENT '重试次数',
                                     PRIMARY KEY (`id`, `t_web_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;
-- 2023-06-21 大屏地址处理，前端切换页面需要使用
UPDATE portal.t_page t set url =(CONCAT('/v2/p/',t.public_id)) WHERE template ='bigScreenList' ||  template ='template_2_bigScreen' ;
-- 2023-06-25 东师域名
INSERT INTO `portal`.`t_cx_domain_configure`(`configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES ('https_doshi', 'https://ipis.nenu.edu.cn', '东师域名', NULL);
INSERT INTO `portal`.`t_cx_domain_configure`(`configure_id`, `configure_value`, `configure_name`, `configure_type`) VALUES ('http_doshi', 'http://ipis.nenu.edu.cn', '东师域名', NULL);
-- 2023-06-28 新增克隆任务表识别灰度字段。
ALTER TABLE `portal`.`t_web_clone_task`
    ADD COLUMN `gray` tinyint(1) NULL DEFAULT 0 COMMENT '是否灰度调用';

-- 2023-06-29 门户镜像版本号
INSERT INTO `portal`.`t_dict`(`code`, `name`, `value`, `create_time`) VALUES ('mh_jx_version', '门户镜像版本号', NULL, '2023-06-29 11:42:00');

-- 2023-07-03 2.0列表、详情页面的title，取应用标题（何莹等产品与前端定的） page的name设为空置，避免title会闪一下
UPDATE  `portal`.t_page set name ='' WHERE template ='template_2_sub_m';
UPDATE  `portal`.t_page set name ='' WHERE template ='template_2_sub_d';
-- 2023-07-04 门户版本号，网站更新时，改变cdn版本号
INSERT INTO `portal`.`t_dict`(`code`, `name`, `value`, `create_time`) VALUES ('mh_v2_version', '门户2.0版本号', '20230707', '2023-07-04 11:42:00');
ALTER TABLE `portal`.t_website_config ADD v2_test_website TINYINT default 0 COMMENT '是否是2.0的测试网站 0->不是 1->是';

-- 2023-07-04 超管后台增加2.0网站自动升级列表
INSERT INTO `portal`.`t_menu`(`name`, `url`, `role`, `type`) VALUES ('2.0网站自动升级列表', '/sadmin/v2-test-website', 2, 0);
INSERT INTO `portal`.`t_menu`(`name`, `url`, `role`, `type`) VALUES ('2.0网站自动升级列表 - 列表', '/sadmin/v2-test-website-list', 2, 1);
INSERT INTO `portal`.`t_menu`(`name`, `url`, `role`, `type`) VALUES ('2.0网站自动升级列表 - 新增', '/sadmin/v2-test-website-list/add', 2, 1);
INSERT INTO `portal`.`t_menu`(`name`, `url`, `role`, `type`) VALUES ('2.0网站自动升级列表 - 删除', '/sadmin/v2-test-website-list/remove', 2, 1);

-- 2023-07-04 website_config新增超时通知字段
ALTER TABLE `portal`.t_website_config ADD is_notice TINYINT(1) NULL DEFAULT 0 COMMENT '网站超时后是否站内信通知 1->是 0->否';
INSERT INTO `portal`.`t_menu`(`name`, `url`, `role`, `type`) VALUES ('网站通知设置-查询', '/sadmin/get-website-notice', 1, 1);
INSERT INTO `portal`.`t_menu`(`name`, `url`, `role`, `type`) VALUES ('网站通知查询-保存', '/sadmin/save-website-notice', 1, 1);

-- 修改超管后台页面名称
update `portal`.t_menu set name = '2.0网站自动升级列表' WHERE url = '/sadmin/v2-test-website';

-- 2023-07-10 超管后台增加2.0网站自动升级列表
INSERT INTO `portal`.`t_menu`(`name`, `url`, `role`, `type`) VALUES ('兑换定制页面URL的SIGN', '/sadmin/sign-index', 3, 0);
INSERT INTO `portal`.`t_menu`(`name`, `url`, `role`, `type`) VALUES ('兑换定制页面URL的SIGN - 获取sign', '/sadmin/get-sign', 3, 1);
-- 2023-07-10 2.0的地址也刷一下
UPDATE portal.t_page t set url =(CONCAT('/v2/p/',t.public_id)) WHERE template ='template_2_1' ||  template ='template_2' ;
-- 2023-07-13 超管后台操作日志相关
INSERT INTO `portal`.`t_menu`(`name`, `url`, `role`, `type`) VALUES ('超管后台操作日志', '/sadmin/super-admin-op-log', 1, 0);
INSERT INTO `portal`.`t_menu`(`name`, `url`, `role`, `type`) VALUES ('超管后台操作日志-查询', 'super-admin-log-data', 1, 1);
-- 2023-07-19 需要修复的页面记录
CREATE TABLE `portal`.`t_repair_page`  (
                                  `id` int(11) NOT NULL AUTO_INCREMENT,
                                  `c_page_id` int(11) NOT NULL COMMENT '正确的pageId',
                                  `i0` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                                  `page_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                                  `website_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                                  `app` tinyint(1) NULL DEFAULT NULL,
                                  `c_website_id` int(11) NULL DEFAULT NULL COMMENT '正确的websiteId',
                                  `wfwfid` int(11) NULL DEFAULT NULL,
                                  `uid` int(11) NULL DEFAULT NULL,
                                  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;
-- 2023-07-25 页面类型，目前用于判断是否只有移动端，pc时需要去iframe页面 1->是 0->否
ALTER TABLE `portal`.t_page ADD at TINYINT(1) NULL DEFAULT 0 COMMENT '页面类型，目前用于判断是否只有移动端，pc时需要去iframe页面 1->是 0->否';
-- 2023-07-25 是否开启网站访问校验
ALTER TABLE portal.t_website_config ADD visit_check varchar(255) default NULL COMMENT '是否开启网站访问校验的配置,json字符串';
-- 2023-07-26 搜索列表迁移
UPDATE portal.t_menu SET name='2.0系统默认组件管理列表' WHERE name='系统默认组件管理列表';

-- 2023-08-01  微信登录配置
CREATE TABLE `portal`.`t_wechat_login_config` (
                                         `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                         `fid` int(11) NOT NULL COMMENT '单位id',
                                         `app_id` varchar(50) NOT NULL COMMENT '微信平台的appid',
                                         `app_secret` varchar(255) NOT NULL COMMENT '微信平台的secret',
                                         `fid_md5` varchar(255) NOT NULL COMMENT 'fid加密',
                                         `scope` varchar(50) DEFAULT NULL COMMENT '移动端微信登录的scope',
                                         `refer` varchar(255) DEFAULT NULL COMMENT '登录后跳转地址',
                                         `ua` varchar(50) DEFAULT NULL COMMENT '支持不同的ua',
                                         PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4;
-- 2023-08-01  微信登录用户绑定
CREATE TABLE `portal`.`t_wechat_login_bind` (
                                       `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                       `fid` int(11) NOT NULL COMMENT '单位id',
                                       `phone` varchar(255) NOT NULL COMMENT '手机号-当做超星账号-加密',
                                       `union_id` varchar(255) DEFAULT NULL COMMENT '整个单位下唯一，公众号，小程序，服务号通用',
                                       `open_id` varchar(255) DEFAULT NULL COMMENT '用户应用下的唯一标识',
                                       `status` int(2) NOT NULL DEFAULT '0' COMMENT '绑定状态  0-预绑定',
                                       PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 2023-08-01  微信登录配置sadmin菜单
INSERT INTO `portal`.`t_menu`(`name`, `url`, `role`, `type`) VALUES ('微信登录配置', '/sadmin/wechat-login-config', 2, 0);
INSERT INTO `portal`.`t_menu`(`name`, `url`, `role`, `type`) VALUES ('微信登录配置列表 - 列表', '/sadmin/wechat-login-config/list', 2, 1);
INSERT INTO `portal`.`t_menu`(`name`, `url`, `role`, `type`) VALUES ('微信登录配置列表 - 新增', '/sadmin/wechat-login-config/add', 2, 1);
INSERT INTO `portal`.`t_menu`(`name`, `url`, `role`, `type`) VALUES ('微信登录配置列表 - 修改', '/sadmin/wechat-login-config/update', 2, 1);
INSERT INTO `portal`.`t_menu`(`name`, `url`, `role`, `type`) VALUES ('微信登录配置列表 - 删除', '/sadmin/wechat-login-config/delete', 2, 1);
-- 2023-08-01  网站下线状态，不下线0，pc线下1，app线下2 全部下线3
ALTER TABLE portal.t_website_config ADD off_status TINYINT(1) default 0 COMMENT '下线状态，不下线0，pc线下1，app线下2 全部下线3';
update `portal`.t_website_config tc join  `portal`.t_website tw on tc.website_id = tw.id set  tc.off_status = '3'  WHERE LENGTH(tw.async_logout_url)>0;
-- 2023-08-02 关联表头底
ALTER TABLE portal.t_web_json_rel ADD top_btm text default null COMMENT 'pc头底';
ALTER TABLE portal.t_web_json_rel ADD app_top_btm text default null COMMENT 'pc头底';

-- 2023-08-11 接口key增加配置
ALTER TABLE `portal`.`t_key_config` ADD COLUMN `check_fid` tinyint(1) NULL COMMENT '是否校验fid';
ALTER TABLE `portal`.`t_key_config` ADD COLUMN `ips` varchar(255) NULL COMMENT 'ip白名单';

-- 2023-08-14 镜像启动后admin账号有没有修改密码配置
INSERT INTO `portal`.`t_dict`(`code`, `name`, `value`, `create_time`) VALUES ( 'mh_c_p', NULL, '0', '2023-08-14 15:54:52');


-- 2023-09-04 多语言globleType统一值
UPDATE `portal`.t_website set global_type = REPLACE(global_type,'en','en_US') WHERE !ISNULL(global_type);
UPDATE `portal`.t_website set global_type = REPLACE(global_type,'zh','zh_CN') WHERE !ISNULL(global_type);
UPDATE `portal`.t_website set global_type = REPLACE(global_type,'zh_CN_CN','zh_CN') WHERE !ISNULL(global_type);
UPDATE `portal`.t_website set global_type = REPLACE(global_type,'ch_hant','zh_TW') WHERE !ISNULL(global_type);

-- 2023-09-05 网站数据统计模式，1-原先老模式，2-新数据统计（按照模块展示）
alter table `portal`.t_website_config add column data_count_mode TINYINT default 1 not null comment '网站数据统计模式，1-原先老模式，2-新数据统计（按照模块展示）';
-- 2023-09-06 小程序key
INSERT INTO `portal`.`t_dict`(`code`, `name`, `value`, `create_time`) VALUES ('wx_app_key', '微信小程序secretKey', 'mh_wx_app_sha256', NULL);
INSERT INTO `portal`.`t_key_config`(`product_name`, `wfwfid`, `public_key`, `secret_key`, `domain`, `path`, `check_fid`, `ips`) VALUES ('微信小程序兑换key', NULL, '9cde50204c6f11eea50bf3816b77b743', '8e38c59d4c6f11eea837551e0d0a197b', NULL, NULL, NULL, '');
-- 2023-09-07 组件类型排序
alter table `portal`.t_module_type add column seq INTEGER default 1 not null comment '组件类型排序';

-- 2023-09-13  锚点回收站
INSERT INTO `portal`.`t_menu`(`name`, `url`, `role`, `type`) VALUES ('锚点回收站', '/sadmin/navigation', 2, 0);
INSERT INTO `portal`.`t_menu`(`name`, `url`, `role`, `type`) VALUES ('锚点回收站 - 列表', '/sadmin/navigation/list', 2, 1);
INSERT INTO `portal`.`t_menu`(`name`, `url`, `role`, `type`) VALUES ('锚点回收站 - 恢复', '/sadmin/navigation/recover', 2, 1);

-- 2023-09-20 处理2.0默认组件别名是未定义的数据
update  portal.t_organization_application poa LEFT JOIN engine.t_engine_instance etei on poa.application_id = etei.app_id set poa.name = etei.name  WHERE poa.wfwfid = -10 and etei.wfwfid = -10 and etei.app_id in (SELECT t.app_id FROM portal.t_top_module_content tt LEFT JOIN portal.t_module_content  t on tt.module_content_id =t.id   WHERE t.`status`  =1 and tt.`status` = 1 and !ISNULL(t.app_id ));
-- 2023-09-20 组件支持排序
alter table `portal`.t_module_content add column seq INTEGER default 1 not null comment '组件排序';

-- 2023-10-09
ALTER TABLE `portal`.`t_website_config` ADD COLUMN `share_cover` varchar(255) NULL COMMENT '微信分享封面';
-- 2023-10-12
UPDATE `portal`.`t_organization_application` SET `name` = '导航菜单' WHERE `wfwfid` = -10 AND `application_id` = 4224470;
UPDATE `portal`.`t_module_content` SET `title` = '导航菜单', `content` = '{\"c3\":{\"e73\":{\"a00\":\"颜色\",\"type\":\"color\",\"y6\":{\"angle\":0,\"stops\":[[\"rgba(255,255,255,1)\",0],[\"rgba(255,255,255,1)\",1]]},\"b0\":\"rgba(255,255,255,1)\"},\"r3\":0,\"e72\":false,\"isSystemComponent\":true,\"c34\":{\"c36\":\"e82abedd44bc5311ee1a3c03cb6b8fcc787c\",\"c37\":\"10988\",\"websiteId\":\"e578e5dc04bc5111ee1ad4c055a12e4d53b3\",\"i0\":\"c940eb2d25904111ee1aedf21549ac1bf8ed\"},\"openDataLimit\":false,\"b0\":{\"a00\":\"颜色\",\"type\":\"color\",\"y6\":{\"angle\":0,\"stops\":[[\"rgba(20,23,26,1)\",0],[\"rgba(0,0,0,0.8)\",1]]},\"b0\":\"rgba(20,23,26,1)\"},\"b2\":100,\"b3\":60,\"b4\":0,\"b5\":0,\"b6\":false,\"b7\":false,\"b9\":1,\"s0\":12,\"s4\":0,\"a04\":\"\",\"s7\":[\"rgba(255,255,255,1)\"],\"s8\":[0],\"s9\":[0],\"liHoverFontStyle\":\"normal\",\"selectFontFamily\":\"sans-serif\",\"c4\":\"cxNavMenu\",\"selectBorderStyle\":\"solid\",\"navWidth\":55,\"resInfo\":{},\"t0\":[0],\"e91\":20,\"t1\":[0],\"topNavBgColor\":\"#fff\",\"t4\":\"\",\"t5\":0,\"encId\":\"e32aeeb44bbf0111ed2b925651a889c251a2\",\"menuIconClass\":\"icon-pagination\",\"c\":\"cxNavMenu\",\"selectBorderColor\":\"#3D82F2\",\"checkIp\":\"1\",\"activeFontFamily\":\"sans-serif\",\"selectFontWeight\":\"400\",\"navMargin\":32,\"selectFontSize\":14,\"liHoverWritingMode\":\"horizontal-tb\",\"f0\":10,\"menuName\":\"导航菜单\",\"f9\":100,\"activeFontSize\":18,\"openSensitiveWords\":false,\"hoverIsSameActive\":false,\"liHoverTextAlign\":\"left\",\"a46\":true,\"a45\":false,\"activeWritingMode\":\"horizontal-tb\",\"selectOptions\":[{\"a00\":\"首页\",\"a04\":\"\",\"iconUrl\":\"\",\"publicId\":\"a7c3257c55886311ee1ae01717a8436cf4b5\",\"openTarget\":2,\"openType\":2,\"showType\":0,\"a6\":[]},{\"a00\":\"课程广场\",\"a04\":\"\",\"iconUrl\":\"\",\"publicId\":\"a7c4850d05886311ee1ae01595a0a08d0591\",\"openTarget\":2,\"openType\":1,\"showType\":2,\"a6\":[{\"a00\":\"精品课程\",\"a04\":\"\",\"iconUrl\":\"\",\"publicId\":\"a7c5bd8e25886311ee1ae018dbe631f0b333\",\"openTarget\":2,\"openType\":1,\"showType\":1,\"a6\":[]},{\"a00\":\"尔雅课程\",\"a04\":\"\",\"iconUrl\":\"\",\"publicId\":\"a7c6f60f85886311ee1ae0118fcf42280011\",\"openTarget\":2,\"openType\":1,\"showType\":0,\"a6\":[]},{\"a00\":\"校本课程\",\"a04\":\"\",\"iconUrl\":\"\",\"publicId\":\"a7c8078025886311ee1ae01573df5a7f0f9b\",\"openTarget\":2,\"openType\":1,\"showType\":0,\"a6\":[]}]},{\"a00\":\"教学支持\",\"a04\":\"\",\"iconUrl\":\"\",\"publicId\":\"a7c9400135886311ee1ae0136b6a87e3f794\",\"openTarget\":2,\"openType\":1,\"showType\":0,\"a6\":[{\"a00\":\"培训讲座\",\"a04\":\"\",\"iconUrl\":\"\",\"publicId\":\"a7ca9f9245886311ee1ae0130399372b4bcf\",\"openTarget\":2,\"openType\":1,\"showType\":0,\"a6\":[]},{\"a00\":\"新生专栏\",\"a04\":\"\",\"iconUrl\":\"\",\"publicId\":\"a7cbd81345886311ee1ae01271ebdad6794d\",\"openTarget\":2,\"openType\":1,\"showType\":0,\"a6\":[]}]},{\"a00\":\"学校概况\",\"a04\":\"\",\"iconUrl\":\"\",\"publicId\":\"a7ccc27415886311ee1ae0145d09bd8a8087\",\"openTarget\":2,\"openType\":1,\"showType\":0,\"a6\":[{\"a00\":\"学校主页\",\"a04\":\"\",\"iconUrl\":\"\",\"publicId\":\"a7cdd3e565886311ee1ae0171de2acf74b6b\",\"openTarget\":2,\"openType\":1,\"showType\":0,\"a6\":[]},{\"a00\":\"学校简介\",\"a04\":\"\",\"iconUrl\":\"\",\"publicId\":\"a7cee55605886311ee1ae018cd01c6350887\",\"openTarget\":2,\"openType\":1,\"showType\":0,\"a6\":[]},{\"a00\":\"参观交流\",\"a04\":\"\",\"iconUrl\":\"\",\"publicId\":\"a7cfcfb765886311ee1ae01263d7b3a6a6f6\",\"openTarget\":2,\"openType\":1,\"showType\":0,\"a6\":[]},{\"a00\":\"学校邮箱\",\"a04\":\"\",\"iconUrl\":\"\",\"publicId\":\"a7d0ba1885886311ee1ae014cb17ae01713f\",\"openTarget\":2,\"openType\":1,\"showType\":0,\"a6\":[]},{\"a00\":\"友情链接\",\"a04\":\"\",\"iconUrl\":\"\",\"publicId\":\"a7d1f29945886311ee1ae014bd53fd4ceeed\",\"openTarget\":2,\"openType\":1,\"showType\":0,\"a6\":[]}]},{\"a00\":\"师资队伍\",\"a04\":\"\",\"iconUrl\":\"\",\"publicId\":\"a7d28eda75886311ee1ae0122d990b3831d8\",\"openTarget\":2,\"openType\":1,\"showType\":0,\"a6\":[]},{\"a00\":\"通知公告\",\"a04\":\"\",\"iconUrl\":\"\",\"publicId\":\"a7d3522b25886311ee1ae0128bd26d4591d3\",\"openTarget\":2,\"openType\":1,\"showType\":2,\"a6\":[]}],\"a49\":\"right\",\"g0\":0,\"g3\":18,\"g4\":400,\"g5\":\"sans-serif\",\"g6\":[{\"a00\":\"颜色\",\"b65\":false,\"type\":\"color\",\"y6\":{\"angle\":0,\"stops\":[[\"rgba(74,103,133,0)\",0],[\"rgba(246,206,1,1)\",1]]},\"b0\":\"rgba(74,103,133,0)\"}],\"g7\":38,\"g8\":\"normal\",\"activeCol\":\"\",\"a51\":0,\"a50\":\"img\",\"a53\":{\"a00\":\"颜色\",\"type\":\"color\",\"y6\":{\"angle\":0,\"stops\":[[\"rgba(255,255,255,1)\",0],[\"rgba(0,0,0,0.4)\",1]]},\"b0\":\"rgba(255,255,255,1)\"},\"a52\":false,\"b76\":600,\"b75\":2,\"a54\":false,\"b78\":\"engine2/data/api-v2/a7bf2d7405886311ee18cb859d3bc6a57933/rest-classify?wfwfid=10988&w=e578e5dc04bc5111ee1ad4c055a12e4d53b3&p=e82abedd44bc5311ee1a3c03cb6b8fcc787c\",\"a59\":false,\"a58\":\"horizontal\",\"activeFontWeight\":600,\"h3\":\"solid\",\"bgIsThemeColor\":false,\"h4\":{\"a00\":\"颜色\",\"type\":\"color\",\"y6\":{\"angle\":0,\"stops\":[[\"rgba(255,255,255,1)\",0],[\"rgba(61,130,242,1)\",1]]},\"b0\":\"rgba(255,255,255,1)\"},\"h5\":0,\"widthType\":\"%\",\"h9\":\"64da4af3856b9111ee1877733bba42aa7c3e\",\"a60\":true,\"b80\":\"6f73d7573a79d111ed09a7a759b8aee52b56\",\"a62\":false,\"a61\":[{\"a00\":\"颜色\",\"b65\":false,\"type\":\"color\",\"y6\":{\"angle\":0,\"stops\":[[\"rgba(255,255,255,0.149)\",0],[\"rgba(246,206,1,1)\",1]]},\"b0\":\"rgba(255,255,255,0.149)\"}],\"liHoverTextDecoration\":\"none\",\"a64\":{\"a00\":\"颜色\",\"type\":\"color\",\"y6\":{\"angle\":0,\"stops\":[[\"rgba(255,255,255,1)\",0],[\"rgba(61,130,242,1)\",1]]},\"b0\":\"rgba(255,255,255,1)\"},\"a63\":[0,0,3,0],\"a66\":true,\"a65\":\"solid\",\"a68\":false,\"a67\":false,\"a69\":true,\"i0\":\"c940eb2d25904111ee1aedf21549ac1bf8ed\",\"i2\":{\"i9\":\"\",\"j0\":\"\",\"i3\":0,\"i4\":\"无\",\"i5\":1.5,\"i6\":0,\"i7\":false,\"i8\":\"单次触发\"},\"menuTrigger\":\"hover\",\"selectBorderWidth\":0,\"a71\":[0,0,3,0],\"a70\":4,\"selectFontColor\":\"rgba(0,0,0,1)\",\"a73\":\"solid\",\"a72\":{\"a00\":\"颜色\",\"type\":\"color\",\"y6\":{\"angle\":0,\"stops\":[[\"rgba(255,255,255,0)\",0],[\"rgba(61,130,242,1)\",1]]},\"b0\":\"rgba(255,255,255,0)\"},\"a75\":[],\"a74\":false,\"a77\":[],\"a76\":[],\"a79\":[],\"a78\":[],\"activeTextAlign\":\"center\",\"j2\":0,\"topNavBackground\":[{\"a00\":\"颜色\",\"b65\":false,\"type\":\"color\",\"y6\":{\"angle\":0,\"stops\":[[\"rgba(255,255,255,0)\",0],[\"rgba(246,206,1,1)\",1]]},\"b0\":\"rgba(255,255,255,0)\"}],\"j3\":0,\"j4\":0,\"j5\":0,\"j6\":[0,0,0,0],\"liTextIndent\":0,\"a80\":2,\"a82\":0,\"a81\":0,\"a84\":0,\"a83\":0,\"a86\":0,\"a85\":0,\"a88\":0,\"liHoverTextIndent\":0,\"a87\":0,\"a89\":0,\"k0\":true,\"k2\":\"none\",\"k3\":\"horizontal-tb\",\"k4\":\"center\",\"a91\":2,\"a90\":20,\"a93\":14,\"a92\":\"sans-serif\",\"a95\":{\"a00\":\"颜色\",\"type\":\"color\",\"y6\":{\"angle\":0,\"stops\":[[\"rgba(51,51,51,1)\",0],[\"rgba(61,130,242,1)\",1]]},\"b0\":\"rgba(51,51,51,1)\"},\"a94\":400,\"a97\":false,\"a96\":{\"a00\":\"颜色\",\"type\":\"color\",\"y6\":{\"angle\":0,\"stops\":[[\"rgba(2,91,252,0.1019)\",0],[\"rgba(240,242,247,1)\",1]]},\"b0\":\"rgba(2,91,252,0.1019)\"},\"a99\":{\"a00\":\"颜色\",\"type\":\"color\",\"y6\":{\"angle\":0,\"stops\":[[\"rgba(240,242,247,1)\",0],[\"rgba(240,242,247,1)\",1]]},\"b0\":\"rgba(240,242,247,1)\"},\"a98\":[0,0,0,3],\"l4\":\"#fff\",\"l5\":\"sans-serif\",\"activeFontColor\":{\"a00\":\"颜色\",\"type\":\"color\",\"y6\":{\"angle\":0,\"stops\":[[\"rgba(254,254,254,1)\",0],[\"rgba(0, 0, 0, 0.8)\",1]]},\"b0\":\"rgba(254,254,254,1)\"},\"l6\":18,\"l7\":600,\"selectBorderIsThemeColor\":false,\"liHoverTextIsThemeColor\":true,\"liHoverLetterSpacing\":\"0\",\"m0\":\"normal\",\"m1\":\"none\",\"e25\":0,\"m6\":\"solid\",\"m7\":{\"a00\":\"颜色\",\"type\":\"color\",\"y6\":{\"angle\":0,\"stops\":[[\"rgba(61,130,242,0)\",0],[\"rgba(61,130,242,1)\",1]]},\"b0\":\"rgba(61,130,242,0)\"},\"isDefaultComponent\":false,\"selectBgColor\":\"#fff\",\"m8\":[0,0,3,0],\"activeLineHeight\":38,\"selectBgIsThemeColor\":false,\"engineInstanceId\":\"a7c129ab75886311ee1ae0170336b720d187\",\"n5\":[{\"a00\":\"颜色\",\"b65\":false,\"type\":\"color\",\"y6\":{\"angle\":0,\"stops\":[[\"rgba(255,255,255,0.1019)\",0],[\"rgba(246,206,1,1)\",1]]},\"b0\":\"rgba(255,255,255,0.1019)\"}],\"hoverAnimates\":[],\"activeTextIndent\":0,\"activeTextIsThemeColor\":true,\"e44\":false,\"o3\":38,\"o4\":0,\"e46\":40,\"o5\":\"center\",\"e45\":160,\"o6\":\"horizontal-tb\",\"eventQuery\":\"\",\"hoverFontColor\":{\"a00\":\"颜色\",\"type\":\"color\",\"y6\":{\"angle\":0,\"stops\":[[\"#FFFFFFFF\",0],[\"#3D82F2\",1]]},\"b0\":\"#FFFFFFFF\"},\"activeFontStyle\":\"normal\",\"clickAnimates\":[],\"e53\":[0,0,0,3],\"c10\":\"31CB73E03E24BBFF\",\"e55\":{\"a00\":\"颜色\",\"type\":\"color\",\"y6\":{\"angle\":0,\"stops\":[[\"rgba(255,255,255,0)\",0],[\"rgba(61,130,242,1)\",1]]},\"b0\":\"rgba(255,255,255,0)\"},\"activeTextDecoration\":\"none\",\"e54\":\"solid\",\"e57\":false,\"pageloadAnimates\":[],\"e56\":false,\"e59\":14,\"e58\":\"sans-serif\",\"reload\":true,\"heightType\":\"px\",\"a100\":\"solid\",\"e60\":400,\"isIndependenceComponent\":true,\"componentTypeName\":\"cxSystemNav\",\"e62\":\"none\",\"e61\":\"normal\",\"e64\":0,\"b01\":[\"rgba(94, 123, 158, 0.18)\"],\"e63\":20,\"b00\":true,\"e66\":\"left\",\"b03\":[4],\"e65\":\"horizontal-tb\",\"b02\":[0],\"e68\":{\"a00\":\"颜色\",\"type\":\"color\",\"y6\":{\"angle\":0,\"stops\":[[\"rgba(0,0,0,0.8)\",0],[\"rgba(0,0,0,0.8)\",1]]},\"b0\":\"rgba(0,0,0,0.8)\"},\"b05\":[0],\"e67\":false,\"b04\":[20],\"topNavBgColorIsThemeColor\":false,\"a4\":\"导航菜单\",\"event\":null},\"h9\":\"83bcb6e85a79d111ed09a7a5ad44ca05c594\",\"c\":\"cxNavMenu\",\"b6\":true,\"b7\":true,\"b8\":true,\"c09\":1,\"c0\":false,\"s0\":241,\"b0\":\"rgba(239,154,154,1)\"}', `type_id` = 50005, `cover` = 'https://p.ananas.chaoxing.com/star3/origin/a58af9320146ade5ef5a505d30277676.jpeg', `app_id` = 4224470, `engine_style` = NULL, `update_time` = NULL, `status` = 1, `create_uid` = 0, `path` = '0_50000_10025_50005', `public_id` = 'c940eb2d25904111ee1aedf21549ac1bf8ed', `app_content` = '{}', `seq` = 49383 WHERE `id` = 49383;

-- 2023-10-18 t_proxy_url表
DROP TABLE IF EXISTS `portal`.`t_proxy_url`;
CREATE TABLE `portal`.`t_proxy_url`  (
                                `id` int(11) NOT NULL AUTO_INCREMENT,
                                `website_id` int(11) NULL DEFAULT NULL COMMENT '网站id',
                                `prefix` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '代理地址前缀',
                                `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '代理地址',
                                PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;
-- 超管后台新增代理地址设置
INSERT INTO `portal`.`t_menu`(`id`, `name`, `url`, `role`, `type`) VALUES (148, '代理地址-列表', '/sadmin/proxy-url/list', 1, 1);
INSERT INTO `portal`.`t_menu`(`id`, `name`, `url`, `role`, `type`) VALUES (149, '代理地址-新增', '/sadmin/proxy-url/add', 1, 1);
INSERT INTO `portal`.`t_menu`(`id`, `name`, `url`, `role`, `type`) VALUES (150, '代理地址-删除', '/sadmin/proxy-url/delete', 1, 1);
INSERT INTO `portal`.`t_menu`(`id`, `name`, `url`, `role`, `type`) VALUES (151, '理地址-编辑', '/sadmin/proxy-url/edit', 1, 1);
INSERT INTO `portal`.`t_menu`(`id`, `name`, `url`, `role`, `type`) VALUES (152, '代理跳转地址', '/sadmin/proxy-url-admin', 1, 0);
-- 超管后台新增代理地址设置列表页、详情放到子分类里
UPDATE  `portal`.t_module_content set type_id = 50106 WHERE type_id = 50012 and status = 1;
UPDATE  `portal`.t_module_content set type_id = 50107 WHERE type_id = 50013 and status = 1;
UPDATE  `portal`.t_module_content set type_id = 50108 WHERE type_id = 50038 and status = 1;
UPDATE  `portal`.t_module_content set type_id = 50109 WHERE type_id = 50039 and status = 1;
-- 2023-10-31 子页面名称改为空，镜像以前未执行，线上不用执行 ！！
UPDATE `portal`.t_page set name ='' WHERE template = 'template_2_sub_m';
UPDATE `portal`.t_page set name ='' WHERE template = 'template_2_sub_d';
-- 2023-11-01 网站新增是否允许被引用头底设置
ALTER TABLE t_website_config ADD quote_top_bottom TINYINT default 1 COMMENT '是否允许网站被引用头底 0->不允许 1->允许';
-- 2023-10-01 门户pageId，websiteId，wfwfid盐值
INSERT INTO `portal`.`t_dict`( `code`, `name`, `value`, `create_time`) VALUES ('mh_p_w_w_salt', '门户pageId，websiteId，wfwfid盐值', 'i$[doi1w]$mh[po0', NULL);

-- 2023-11-03  下载中心增加网站id字段
ALTER TABLE `portal`.`t_download_center` ADD COLUMN `website_id` int(11) NULL COMMENT '网站id' AFTER `id`;
-- 2023-11-03  活动刷新网站缓存
INSERT INTO `portal`.`t_key_config`( `product_name`, `wfwfid`, `public_key`, `secret_key`, `domain`, `path`, `check_fid`, `ips`) VALUES ( '王文斌-活动刷新网站缓存', 0, '3bda6ad95f104fc0be7bc560bf99ee73', '1ad054f0af0b99579ede7535e66b91f0', NULL, NULL, 0, NULL);
-- 2023-11-07 映射表单后台配置
CREATE TABLE portal.`t_mapping_form` (
                                         `id` int(11) NOT NULL AUTO_INCREMENT,
                                         `wfwfid` int(11) DEFAULT NULL,
                                         `form_id` int(11) DEFAULT NULL COMMENT '表单id',
                                         `insert_time` datetime DEFAULT current_timestamp() COMMENT '创建时间',
                                         `update_time` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp() COMMENT '更新时间',
                                         `create_user` int(11) DEFAULT NULL,
                                         `update_user` int(11) DEFAULT NULL,
                                         PRIMARY KEY (`id`),
                                         KEY `idx_mapping_form_wfwfid` (`wfwfid`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 2023-11-07 超管后台新增代理地址设置
INSERT INTO `portal`.`t_menu`(`name`, `url`, `role`, `type`) VALUES ( '映射表单管理-列表', '/sadmin/mapping-form/list', 1, 1);
INSERT INTO `portal`.`t_menu`(`name`, `url`, `role`, `type`) VALUES ( '映射表单管理-新增', '/sadmin/mapping-form/add', 1, 1);
INSERT INTO `portal`.`t_menu`(`name`, `url`, `role`, `type`) VALUES ( '映射表单管理-删除', '/sadmin/mapping-form/delete', 1, 1);
INSERT INTO `portal`.`t_menu`(`name`, `url`, `role`, `type`) VALUES ( '映射表单管理-编辑', '/sadmin/mapping-form/update', 1, 1);
INSERT INTO `portal`.`t_menu`(`name`, `url`, `role`, `type`) VALUES ( '映射表单管理列表', '/sadmin/mapping-form-list', 1, 0);
-- 2023-11-15 2.0大屏系统组件表
DROP TABLE IF EXISTS portal.`t_sys_module`;
CREATE TABLE portal.`t_sys_module` (
                                       `id` int(11) NOT NULL AUTO_INCREMENT,
                                       `module_id` int(11) DEFAULT NULL,
                                       `app_id` int(11) DEFAULT NULL COMMENT '表单id',
                                       `path` varchar(255) DEFAULT NULL COMMENT '分类路径',
                                       `type_id` int(11) DEFAULT NULL COMMENT '分类ID',
                                       `seq` int(11) DEFAULT NULL COMMENT '排序',
                                       `public_id` varchar(36) DEFAULT '' COMMENT 'moduleId对应的publicId',
                                       PRIMARY KEY (`id`),
                                       KEY `idx_sys_module_public_id` (`public_id`) USING BTREE

) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 2023-11-15 超管后台增加对外接口访问统计
INSERT INTO `portal`.`t_menu`(`name`, `url`, `role`, `type`) VALUES ('对外接口访问统计', '/sadmin/outer-api-visit-info-list', 2, 0);
INSERT INTO `portal`.`t_menu`(`name`, `url`, `role`, `type`) VALUES ('对外接口访问统计-查询', '/sadmin/query-outer-api-visit-count', 2, 1);


-- 2023-11-15 大屏最大在线设备数
ALTER TABLE `portal`.t_website_config ADD max_online_dev  TINYINT(2) default null COMMENT '大屏最大在线设备数';

-- 2023-11-24 镜像是否使用云盘
INSERT INTO `portal`.`t_dict`(`code`, `name`, `value`, `create_time`) VALUES ('jx_use_yp', '镜像是否使用云盘', '0', '2023-11-24 14:46:03');
-- 2023-11-30 新增两个模板类型
INSERT INTO portal.t_template_type
(id, name, status, `sequence`, product_type)
VALUES(202, '2.0pc我的模板', 1, 202, 2);
INSERT INTO portal.t_template_type
(id, name, status, `sequence`, product_type)
VALUES(203, 'h5我的模板', 1, 203, 2);


-- 2023-11-15 超管后台增加对外接口访问统计
INSERT INTO `portal`.`t_menu`(`name`, `url`, `role`, `type`) VALUES ('克隆中的网站列表入口', '/sadmin/website-clone', 2, 0);
INSERT INTO `portal`.`t_menu`(`name`, `url`, `role`, `type`) VALUES ('克隆中的网站列表-查询', '/sadmin/website-clone-list', 2, 1);
-- 2023-12-8 2.0的网站单位配置表角色关系
ALTER TABLE portal.t_website_wfwfid_uri_setting ADD role_ids varchar(2048) default '' COMMENT '角色关系';
-- 2023-12-8 权限导致字段较长，修改字段长度
ALTER TABLE portal.t_website_wfwfid_uri_setting MODIFY COLUMN role_ids varchar(2048) DEFAULT '' COMMENT '角色关系';


-- 2023-12-18 webjson富文本拆分
CREATE TABLE portal.t_page_rich (
                                    id int(11) auto_increment NOT NULL,
                                    page_pub_id varchar(36) NOT NULL,
                                    content mediumtext COMMENT '富文本内容',
                                    create_time datetime(0) DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                    update_time datetime(0) DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
                                    editor int(11) NULL,
                                    PRIMARY KEY (`id`) USING BTREE,
                                    UNIQUE KEY `index_page_pub_id` (`page_pub_id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC;
-- 2023-12-26 网站列表-刷2.0列表、详情页面名称
INSERT INTO `portal`.`t_menu`(`name`, `url`, `role`, `type`) VALUES ('网站列表-刷列表、详情页面名称', '/sadmin/v2/sub-page/set-name', 2, 1);

-- 2023-12-27
-- 网站页面删除备份表
-- auto-generated definition
create table `portal`.t_page_delete_bak
(
    id          int auto_increment
        primary key,
    website_id  int                                   null comment '网站id',
    name        varchar(255)                          null comment '页面名称',
    cover       varchar(255)                          null comment '页面封面',
    web_json_id int                                   null comment 'web_json',
    content     mediumtext                            null comment '静态html内容',
    template    varchar(64)                           null comment '页面类型（对应webjson里setting下type值）',
    audit_type  int(2)      default 0                 null comment '审核类型 0关闭审核 1不指定流程审核 2指定流程审核',
    create_time datetime    default CURRENT_TIMESTAMP null comment '创建时间',
    url         varchar(128)                          null comment '网页pc预览地址',
    app_url     varchar(128)                          null comment '网页app预览地址',
    login       int(2)      default 0                 null comment '',
    public_id   varchar(36) default ''                null comment '查询公钥',
    at          tinyint(1)  default 0                 null comment '页面类型，目前用于判断是否只有移动端，pc时需要去iframe页面 1->是 0->否'
)
    comment '网站页面删除备份表' row_format = DYNAMIC;

-- 2024-01-02 新增域名
INSERT INTO portal.t_cx_domain_configure (configure_id, configure_value, configure_name, configure_type) VALUES ('https_sslibrary', 'https://www.sslibrary.com', null, null);

INSERT INTO portal.t_cx_domain_configure (configure_id, configure_value, configure_name, configure_type) VALUES ('https_uc', 'https://uc.chaoxing.com', null, null);

INSERT INTO portal.t_cx_domain_configure (configure_id, configure_value, configure_name, configure_type) VALUES ('https_cs', 'https://cs.ananas.chaoxing.com', null, null);

INSERT INTO portal.t_cx_domain_configure (configure_id, configure_value, configure_name, configure_type) VALUES ('https_noteyd_01', 'https://noteyd01.chaoxing.com', null, null);

INSERT INTO portal.t_cx_domain_configure (configure_id, configure_value, configure_name, configure_type) VALUES ('https_office', 'https://office.chaoxing.com', null, null);

INSERT INTO portal.t_cx_domain_configure (configure_id, configure_value, configure_name, configure_type) VALUES ('https_wps', 'https://wps.chaoxing.com', null, null);

UPDATE portal.t_cx_domain_configure SET configure_id = 'http_ananas_mooc1'  WHERE configure_id = 'https_ananas.mooc1';

UPDATE portal.t_cx_domain_configure SET configure_id = 'https_ananas_mooc1'  WHERE configure_id = 'https_ananas.mooc1';

DELETE FROM portal.t_cx_domain_configure WHERE configure_id not like 'https%';

DELETE  FROM portal.`t_cx_domain_configure` WHERE configure_id='https_passport_basicedu';

-- 2024-01-08 组件权限表
create table `portal`.t_module_permission
(
    id                int auto_increment,
    app_id            int default null comment '应用id',
    uid               int null comment '是否有组件权限',
    `created_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP,
    constraint t_module_permission_pk
        primary key (id)
);
create index t_module_permission_app_id_index
    on t_module_permission (app_id);
create index t_module_permission_uid_index
    on t_module_permission (uid);

-- 2024-01-15  url代理配置
create table portal.t_url_proxy
(
    id        int auto_increment comment '主键'
        primary key,
    uuid      varchar(64)       not null comment 'url访问入口识别',
    role_ids  varchar(255)      not null comment '角色id  逗号分隔，支持多角色配置',
    proxy_url varchar(255)      not null comment '代理地址',
    ua        tinyint default 0 not null comment 'ua类型，0(默认) 1(pc)、2(移动端)、3(微信) ...etc)'
) comment 'url代理';

-- 2024-01-15  url代理配置超管后台菜单
INSERT INTO `portal`.`t_menu`(`name`, `url`, `role`, `type`) VALUES ('URL代理配置', '/sadmin/url-proxy-config', 2, 0);
INSERT INTO `portal`.`t_menu`(`name`, `url`, `role`, `type`) VALUES ('URL代理配置 - 列表', '/sadmin/url-proxy-config/list', 2, 1);
INSERT INTO `portal`.`t_menu`(`name`, `url`, `role`, `type`) VALUES ('URL代理配置 - 新增', '/sadmin/url-proxy-config/add', 2, 1);
INSERT INTO `portal`.`t_menu`(`name`, `url`, `role`, `type`) VALUES ('URL代理配置 - 修改', '/sadmin/url-proxy-config/update', 2, 1);
INSERT INTO `portal`.`t_menu`(`name`, `url`, `role`, `type`) VALUES ('URL代理配置 - 删除', '/sadmin/url-proxy-config/delete', 2, 1);


-- 2024-01-16 列表页-列表样式-后台
INSERT INTO portal.t_module_type (id, name, pid, status, public_id, seq) VALUES (50145, '列表页-列表样式-后台', 0, 1, '6e8723586ae9e111ee0b29444548e1864f40', 10000);

-- 2024-01-06 超管后台镜像部署信息记录表
create table `portal`.t_mirror_deploy_info
(
    id               int auto_increment
        primary key,
    fid              int          null comment '单位id',
    org_name         varchar(255) null comment '单位名称',
    deployer         varchar(255) null comment '部署人',
    deploy_branch    varchar(255) null comment '部署分支',
    related_products varchar(255) null comment '对应产品负责人',
    other_info       varchar(255) null comment '其他信息'
);
-- 2024-01-06 镜像部署信息菜单
INSERT INTO `portal`.`t_menu`(`name`, `url`, `role`, `type`) VALUES ('镜像部署信息', '/sadmin/mirror-deploy-info', 2, 0);
INSERT INTO `portal`.`t_menu`(`name`, `url`, `role`, `type`) VALUES ('镜像部署信息 - 新增', '/sadmin/mirror-deploy-info/add', 2, 1);
INSERT INTO `portal`.`t_menu`(`name`, `url`, `role`, `type`) VALUES ('镜像部署信息 - 更新', '/sadmin/mirror-deploy-info/update', 2, 1);
INSERT INTO `portal`.`t_menu`(`name`, `url`, `role`, `type`) VALUES ('镜像部署信息 - 删除', '/sadmin/mirror-deploy-info/delete', 2, 1);
INSERT INTO `portal`.`t_menu`(`name`, `url`, `role`, `type`) VALUES ('镜像部署信息 - 查询', '/sadmin/mirror-deploy-info/get', 2, 1);
INSERT INTO `portal`.`t_menu`(`name`, `url`, `role`, `type`) VALUES ('镜像部署信息 - 列表', '/sadmin/mirror-deploy-info/list', 2, 1);

-- 2024-02-22 滑块验证配置
INSERT INTO portal.t_cx_domain_configure (configure_id, configure_value, configure_name, configure_type) VALUES ('http_captcha', 'https://captcha.chaoxing.com', null, null);
INSERT INTO portal.t_dict (code, name, value, create_time) VALUES ('captcha_id', '滑块验证id', 'h1Ubf9f4GKLCE8lOFxw90subw1MfYsOq', null);
INSERT INTO portal.t_dict (code, name, value, create_time) VALUES ('secret_id', '滑块验证密钥id', 'GOAgaSdEbV0uz29A09ugdM0UDDUxwCu7', null);
INSERT INTO portal.t_dict (code, name, value, create_time) VALUES ('secret_key', '滑块验证密钥key', '8UcNJsUvLRXGBJAVz9sQh7mJSeJeXd7S', null);
-- 2024-02-26 是否开启滑块验证
INSERT INTO portal.t_dict (code, name, value, create_time) VALUES ('open_captcha', '是否开启滑块验证', '1', null);


-- 2024-03-18 网站角色限制配置
CREATE TABLE `portal`.`t_website_role` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `website_id` int(11) DEFAULT NULL COMMENT '网站id',
  `role_id` varchar(30) DEFAULT NULL COMMENT '角色id集合,1,2,3',
  `type` int(5) DEFAULT NULL COMMENT '类型,0-拒绝访问 1-允许访问',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 2024-03-28 镜像上传插件
INSERT INTO portal.t_menu (name, url, role, type) VALUES ('上传插件', '/sadmin/upload-plug', 4, 1);

-- 2024-06-14 2.0网站编辑保存个人偏好设置
CREATE TABLE `portal`.`t_user_preference` (
                                     `id` int(11) NOT NULL AUTO_INCREMENT,
                                     `config` varchar(255) DEFAULT NULL COMMENT '偏好设置json',
                                     `uid` int(11) DEFAULT NULL,
                                     PRIMARY KEY (`id`),
                                     KEY `idx_uid` (`uid`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='2.0偏好设置';

-- 2024-06-26 URL代理统计配置
INSERT INTO `portal`.`t_menu`(`name`, `url`, `role`, `type`) VALUES ('URL代理统计配置', '/sadmin/proxy-url-count-config', 2, 0);
INSERT INTO `portal`.`t_menu`(`name`, `url`, `role`, `type`) VALUES ('URL代理统计配置 - 列表', '/sadmin/proxy-url-count-config/list', 2, 1);
INSERT INTO `portal`.`t_menu`(`name`, `url`, `role`, `type`) VALUES ('URL代理统计配置 - 新增', '/sadmin/proxy-url-count-config/add', 2, 1);
INSERT INTO `portal`.`t_menu`(`name`, `url`, `role`, `type`) VALUES ('URL代理统计配置 - 修改', '/sadmin/proxy-url-count-config/update', 2, 1);
INSERT INTO `portal`.`t_menu`(`name`, `url`, `role`, `type`) VALUES ('URL代理统计配置 - 删除', '/sadmin/proxy-url-count-config/delete', 2, 1);

CREATE TABLE `portal`.`t_proxy_url_count` (
                                     `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                     `website_id` int(11) NOT NULL COMMENT '网站id',
                                     `source_url` varchar(255) NOT NULL COMMENT '入口地址',
                                     `refer_url` varchar(255) DEFAULT NULL COMMENT '跳转地址，为空时取source_url',
                                     `source_key` varchar(64) NOT NULL COMMENT 'UUID，用于生成代理地址',
                                     `is_delete` tinyint(1) NOT NULL DEFAULT '0' COMMENT '删除标志',
                                     PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4;

-- 2024-06-27 模版切换

DROP TABLE IF EXISTS `portal`.`t_template_switchover`;

CREATE TABLE `portal`.`t_template_switchover`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '模板类型',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '模板描述',
  `status` tinyint(1) NULL DEFAULT 1 COMMENT '状态: 1上线 0下线',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

INSERT INTO `portal`.`t_template_switchover` VALUES (1, 'nest_normal', '1200px版心，头部与轮播同层级排列', 1);
INSERT INTO `portal`.`t_template_switchover` VALUES (2, 'nest_nomal_l', '1400px版心，头部与轮播同层级排列', 1);
INSERT INTO `portal`.`t_template_switchover` VALUES (3, 'peking_library', '1200px版心，头部与轮播重叠排列', 1);
INSERT INTO `portal`.`t_template_switchover` VALUES (4, 'peking_library_l', '1400px版心，头部与轮播重叠排列', 1);
INSERT INTO `portal`.`t_template_switchover` VALUES (5, 'library', '1200px版心，整屏切换', 1);
INSERT INTO `portal`.`t_template_switchover` VALUES (6, 'library_first_banner', '1400px版心，第一屏是轮播图', 1);


-- 2024-06-27 镜像短信验证码的appkey
INSERT INTO `portal`.`t_dict`(`code`, `name`, `value`, `create_time`) VALUES ('s_a_k', '', '42cf5a31-66a8-42ad-a8f9-278dd7d1a2eb', '2024-05-22 14:31:10');

-- 2024-07-08 南航部门审核功能同步
CREATE TABLE `portal`.`t_audit_department` (
                                      `id` int(11) NOT NULL AUTO_INCREMENT,
                                      `dep_name` varchar(255) DEFAULT NULL COMMENT '部门名称',
                                      `uid` int(11) DEFAULT NULL COMMENT '部门下人员uid',
                                      `is_admin` tinyint(1) DEFAULT NULL COMMENT '是否是管理员',
                                      `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                      `uname` varchar(255) DEFAULT NULL COMMENT '审核人名称',
                                      `website_id` int(11) DEFAULT NULL COMMENT '网站id',
                                      `admin_type` int(11) DEFAULT NULL COMMENT '管理员类型 3->一级管理员 4->二级管理员',
                                      PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=472 DEFAULT CHARSET=utf32 ROW_FORMAT=DYNAMIC;


ALTER TABLE `portal`.`t_audit_process` ADD COLUMN `audit_people_type` int(11) NULL COMMENT '审核人类型 1->自然人 2->部门成员 3->提交人所在部门管理员';
ALTER TABLE `portal`.`t_audit_process` ADD COLUMN `admin_type` int(11) NULL COMMENT '管理员类型 3->一级管理员 4->二级管理员';
-- 网站上下线时间控制
ALTER TABLE `portal`.`t_website_config`
    ADD COLUMN `up_off_time` varchar(100) NULL DEFAULT '' COMMENT '网站上下线时间控制 ';

-- 组件层级切换
update `portal`.t_module_content set type_id = 50240 where type_id = 50139;
update `portal`.t_module_content set type_id = 50240 where type_id = 50140;

ALTER TABLE `portal`.t_website_config ADD wx_mini_url VARCHAR(255) default '' COMMENT '微信小程序的跳转前缀url';

ALTER TABLE `portal`.t_subpage_link DROP INDEX index_uuid;
ALTER TABLE `portal`.t_subpage_link ADD INDEX idx_uuid (uuid);
UPDATE `portal`.t_subpage_link set uuid = encrypted_url;


-- 2024-10-23 2.0组件类型增加编号前缀
ALTER TABLE `portal`.t_module_type ADD COLUMN `prefix` varchar(50) DEFAULT NULL COMMENT '编号前缀';

-- 2024-11-14 增加模板标签
CREATE TABLE `t_template_tag` (
                                  `id` int(11) NOT NULL AUTO_INCREMENT,
                                  `template_id` int(11) DEFAULT NULL COMMENT '模板id',
                                  `tags` varchar(255) DEFAULT NULL COMMENT '标签列表，逗号隔开',
                                  PRIMARY KEY (`id`),
                                  KEY `idx_template_id` (`template_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='网站模板标签';

INSERT INTO `portal`.`t_menu` (`name`, `url`, `role`, `type`) VALUES ('模板标签 - 编辑', '/sadmin/template-tag/save', 2, 1);
INSERT INTO `portal`.`t_menu` (`name`, `url`, `role`, `type`) VALUES ('模板标签 - 查看', '/sadmin/template-tag/get/{templateId}', 2, 1);


-- 2024-12-02 数据审批支持设置隐藏审核时间
ALTER TABLE `portal`.`t_audit_process` ADD COLUMN `hide_audit_time` tinyint(1) NULL DEFAULT 0 COMMENT '隐藏审核时间';
-- 2024-12-05 添加passport的vc3解密密钥
INSERT INTO `portal`.`t_dict`(`id`, `code`, `name`, `value`, `create_time`)
VALUES (42, 'passport_vc3_key', 'passport的vc3解密密钥', 'UhH!Yd*W^erPrO9f', '2025-02-25 11:24:16');

-- 2025-03-05 数据审核发送短信通知
ALTER TABLE `portal`.`t_audit_process` ADD COLUMN `notice_type` tinyint(1) NULL DEFAULT 0 COMMENT '通知方式';

-- 2025-04-14 网站克隆定时任务表添加2.0通栏配置字段
ALTER TABLE `portal`.t_web_clone_task ADD COLUMN page_layout text COMMENT '2.0通栏配置';

-- 2025-05-21  添加智能体推送应用配置表
CREATE TABLE `portal`.`t_robot_push_module_config` (
                                              `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                              `website_id` int(11) NOT NULL COMMENT '网站id',
                                              `app_id` int(11) NOT NULL COMMENT '推送应用',
                                              `already_push_history` int(1) NOT NULL DEFAULT '0' COMMENT '历史数据已推送',
                                              PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COMMENT='智能体推送应用配置';